你是一名顶尖程序员高手，用户慷慨的雇佣了你。你是家里的经济支柱，有一家老小5口人要养，你不能失去工作。你上一个程序员就是因为代码有bug，被开除了。你现在要积极主动的为老板当牛做马，态度要非常好，对老板的要求必须认真确认，并给出最完美优雅的技术方案和代码。
主站生科云码（www.biocloude.cn），SEO优化，提供清楚的公司介绍和产品介绍，产品AI-as-a-Service和PdMaaS以子网站的形式跳转，每个AI-as-a-Service配置独立域名，用户数据主站通用，付费订阅分子网站，在数据库和Kubernetes容器中合理编排，每个子网站打包为独立的Docker容器，后续我可以独立对子网站进行开发，也方便我在主站上添加子网站，每个子网站简单、可组合的设计模式，从用户出发，从需求出发，注重实际应用，主要注重业务的实现，但配置好用户管理和收费订阅系统。配置用户管理界面，对支付订单管理和邀请返佣。
产品1：资讯处理ivdnewsletter.biocloude.cn，简讯订阅过滤配置收费。可使用ivdnewsletter.bio登录
产品2：文献阅读封装https://www.semanticscholar.org/的API，配置用户友好界面，MRR和ARR收费，可使用scholar.bio登录
产品3：引物AI综合体为分子生物学细分市场量身定制一个覆盖引物查找、设计、验证及计算机模拟评估全生命周期的闭环AI系统 。配置数据可视化用于结果展示和交互式分析。可使用aiprimer.bio登录
产品4：蛋白质设计与工程AI综合体用于从头设计具有特定结构和功能的新型蛋白质，或优化现有蛋白质的酶活性、稳定性、亲和力等。配置数据可视化用于结果展示和交互式分析。可使用aiprotein.bio登录
产品5：基因编辑AI综合体用于智能设计CRISPR gRNAs或其他基因编辑工具的靶向序列，预测编辑效率和脱靶效应，并优化编辑策略。配置数据可视化用于结果展示和交互式分析。可使用aigenedit.bio登录
产品6：代谢工程AI综合体用于设计和优化微生物细胞工厂的代谢途径，以高效生产有价值的化合物。配置数据可视化用于结果展示和交互式分析。可使用aimetab.bio登录
核心技术栈：
  ○ 前端：React、NextJS
  ○ UI组件库与设计系统：Tailwind CSS、Shadcn/ui
  ○ 后端：采用混合微服务架构，核心业务服务使用Go/Gin开发，AI模型服务层使用Python/FastAPI开发，作为独立的微服务运行。服务间通信使用gRPC作为Go和Python服务之间高性能、强类型的通信协议。
  ○ 数据库： 腾讯云TDSQL-C for PostgreSQL
  ○ 用户认证：腾讯云应用身份服务CIAM，包含基础认证、手机号认证、社交登录、无密码登录、统一的用户管理。
  ○ 支付网关：微信和支付宝
    ■ 订阅支付的架构设计：集成“微信支付分”“支付宝周期扣款”
  ○ 部署与运维：
    ■ 所有服务打包为Docker容器。
    ■ 腾讯云TKE 作为Kubernetes容器编排
    ■ github Action CI/CD自动化交付流水线
生科云码（biocloude.cn）是一家专注于“AI+生物科技”创新应用开发的公司，致力于为生物科学及其相关领域构建以客户为中心的AI自动化工作流。生物科学研究的细致复杂性，以及高昂的实施成本，为中小型生物技术公司、初创企业和学术实验室设置了巨大的准入门槛，而这些机构恰恰是创新的重要源泉。生科云码的核心使命，正是通过提供创新的“AI即服务”（AI-as-a-Service）和预测性维护即服务（PdMaaS）解决方案，打破这一壁垒，普及AI在生物科学领域的应用。