{"version": 3, "sources": ["../../../src/server/lib/app-info-log.ts"], "sourcesContent": ["import { loadEnvConfig } from '@next/env'\nimport * as Log from '../../build/output/log'\nimport { bold, purple } from '../../lib/picocolors'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_PRODUCTION_BUILD,\n} from '../../shared/lib/constants'\nimport loadConfig, {\n  getConfiguredExperimentalFeatures,\n  type ConfiguredExperimentalFeature,\n} from '../config'\n\nexport function logStartInfo({\n  networkUrl,\n  appUrl,\n  envInfo,\n  experimentalFeatures,\n  maxExperimentalFeatures = Infinity,\n}: {\n  networkUrl: string | null\n  appUrl: string | null\n  envInfo?: string[]\n  experimentalFeatures?: ConfiguredExperimentalFeature[]\n  maxExperimentalFeatures?: number\n}) {\n  let bundlerSuffix\n  if (process.env.TURBOPACK) {\n    bundlerSuffix = ' (Turbopack)'\n  } else if (process.env.NEXT_RSPACK) {\n    bundlerSuffix = ' (Rspack)'\n  } else {\n    bundlerSuffix = ''\n  }\n\n  Log.bootstrap(\n    `${bold(\n      purple(`${Log.prefixes.ready} Next.js ${process.env.__NEXT_VERSION}`)\n    )}${bundlerSuffix}`\n  )\n  if (appUrl) {\n    Log.bootstrap(`- Local:        ${appUrl}`)\n  }\n  if (networkUrl) {\n    Log.bootstrap(`- Network:      ${networkUrl}`)\n  }\n  if (envInfo?.length) Log.bootstrap(`- Environments: ${envInfo.join(', ')}`)\n\n  if (experimentalFeatures?.length) {\n    Log.bootstrap(`- Experiments (use with caution):`)\n    // only show a maximum number of flags\n    for (const exp of experimentalFeatures.slice(0, maxExperimentalFeatures)) {\n      const symbol =\n        exp.type === 'boolean'\n          ? exp.value === true\n            ? bold('✓')\n            : bold('⨯')\n          : '·'\n\n      const suffix = exp.type === 'number' ? `: ${exp.value}` : ''\n\n      Log.bootstrap(`  ${symbol} ${exp.name}${suffix}`)\n    }\n    /* indicate if there are more than the maximum shown no. flags */\n    if (experimentalFeatures.length > maxExperimentalFeatures) {\n      Log.bootstrap(`  · ...`)\n    }\n  }\n\n  // New line after the bootstrap info\n  Log.info('')\n}\n\nexport async function getStartServerInfo(\n  dir: string,\n  dev: boolean\n): Promise<{\n  envInfo?: string[]\n  experimentalFeatures?: ConfiguredExperimentalFeature[]\n}> {\n  let experimentalFeatures: ConfiguredExperimentalFeature[] = []\n  await loadConfig(\n    dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_BUILD,\n    dir,\n    {\n      onLoadUserConfig(userConfig) {\n        const configuredExperimentalFeatures =\n          getConfiguredExperimentalFeatures(userConfig.experimental)\n\n        experimentalFeatures = configuredExperimentalFeatures.sort(\n          ({ name: a }, { name: b }) => a.length - b.length\n        )\n      },\n    }\n  )\n\n  // we need to reset env if we are going to create\n  // the worker process with the esm loader so that the\n  // initial env state is correct\n  let envInfo: string[] = []\n  const { loadedEnvFiles } = loadEnvConfig(dir, true, console, false)\n  if (loadedEnvFiles.length > 0) {\n    envInfo = loadedEnvFiles.map((f) => f.path)\n  }\n\n  return {\n    envInfo,\n    experimentalFeatures,\n  }\n}\n"], "names": ["loadEnvConfig", "Log", "bold", "purple", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_BUILD", "loadConfig", "getConfiguredExperimentalFeatures", "logStartInfo", "networkUrl", "appUrl", "envInfo", "experimentalFeatures", "maxExperimentalFeatures", "Infinity", "bundlerSuffix", "process", "env", "TURBOPACK", "NEXT_RSPACK", "bootstrap", "prefixes", "ready", "__NEXT_VERSION", "length", "join", "exp", "slice", "symbol", "type", "value", "suffix", "name", "info", "getStartServerInfo", "dir", "dev", "onLoadUserConfig", "userConfig", "configuredExperimentalFeatures", "experimental", "sort", "a", "b", "loadedEnvFiles", "console", "map", "f", "path"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAW;AACzC,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,IAAI,EAAEC,MAAM,QAAQ,uBAAsB;AACnD,SACEC,wBAAwB,EACxBC,sBAAsB,QACjB,6BAA4B;AACnC,OAAOC,cACLC,iCAAiC,QAE5B,YAAW;AAElB,OAAO,SAASC,aAAa,EAC3BC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,oBAAoB,EACpBC,0BAA0BC,QAAQ,EAOnC;IACC,IAAIC;IACJ,IAAIC,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzBH,gBAAgB;IAClB,OAAO,IAAIC,QAAQC,GAAG,CAACE,WAAW,EAAE;QAClCJ,gBAAgB;IAClB,OAAO;QACLA,gBAAgB;IAClB;IAEAd,IAAImB,SAAS,CACX,GAAGlB,KACDC,OAAO,GAAGF,IAAIoB,QAAQ,CAACC,KAAK,CAAC,SAAS,EAAEN,QAAQC,GAAG,CAACM,cAAc,EAAE,KAClER,eAAe;IAErB,IAAIL,QAAQ;QACVT,IAAImB,SAAS,CAAC,CAAC,gBAAgB,EAAEV,QAAQ;IAC3C;IACA,IAAID,YAAY;QACdR,IAAImB,SAAS,CAAC,CAAC,gBAAgB,EAAEX,YAAY;IAC/C;IACA,IAAIE,2BAAAA,QAASa,MAAM,EAAEvB,IAAImB,SAAS,CAAC,CAAC,gBAAgB,EAAET,QAAQc,IAAI,CAAC,OAAO;IAE1E,IAAIb,wCAAAA,qBAAsBY,MAAM,EAAE;QAChCvB,IAAImB,SAAS,CAAC,CAAC,iCAAiC,CAAC;QACjD,sCAAsC;QACtC,KAAK,MAAMM,OAAOd,qBAAqBe,KAAK,CAAC,GAAGd,yBAA0B;YACxE,MAAMe,SACJF,IAAIG,IAAI,KAAK,YACTH,IAAII,KAAK,KAAK,OACZ5B,KAAK,OACLA,KAAK,OACP;YAEN,MAAM6B,SAASL,IAAIG,IAAI,KAAK,WAAW,CAAC,EAAE,EAAEH,IAAII,KAAK,EAAE,GAAG;YAE1D7B,IAAImB,SAAS,CAAC,CAAC,EAAE,EAAEQ,OAAO,CAAC,EAAEF,IAAIM,IAAI,GAAGD,QAAQ;QAClD;QACA,+DAA+D,GAC/D,IAAInB,qBAAqBY,MAAM,GAAGX,yBAAyB;YACzDZ,IAAImB,SAAS,CAAC,CAAC,OAAO,CAAC;QACzB;IACF;IAEA,oCAAoC;IACpCnB,IAAIgC,IAAI,CAAC;AACX;AAEA,OAAO,eAAeC,mBACpBC,GAAW,EACXC,GAAY;IAKZ,IAAIxB,uBAAwD,EAAE;IAC9D,MAAMN,WACJ8B,MAAMhC,2BAA2BC,wBACjC8B,KACA;QACEE,kBAAiBC,UAAU;YACzB,MAAMC,iCACJhC,kCAAkC+B,WAAWE,YAAY;YAE3D5B,uBAAuB2B,+BAA+BE,IAAI,CACxD,CAAC,EAAET,MAAMU,CAAC,EAAE,EAAE,EAAEV,MAAMW,CAAC,EAAE,GAAKD,EAAElB,MAAM,GAAGmB,EAAEnB,MAAM;QAErD;IACF;IAGF,iDAAiD;IACjD,qDAAqD;IACrD,+BAA+B;IAC/B,IAAIb,UAAoB,EAAE;IAC1B,MAAM,EAAEiC,cAAc,EAAE,GAAG5C,cAAcmC,KAAK,MAAMU,SAAS;IAC7D,IAAID,eAAepB,MAAM,GAAG,GAAG;QAC7Bb,UAAUiC,eAAeE,GAAG,CAAC,CAACC,IAAMA,EAAEC,IAAI;IAC5C;IAEA,OAAO;QACLrC;QACAC;IACF;AACF"}