{"version": 3, "sources": ["../../../src/server/after/after-context.ts"], "sourcesContent": ["import PromiseQueue from 'next/dist/compiled/p-queue'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { AfterCallback, AfterTask } from './after'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { withExecuteRevalidates } from '../revalidation-utils'\nimport { bindSnapshot } from '../app-render/async-local-storage'\nimport {\n  workUnitAsyncStorage,\n  type WorkUnitStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\n\nexport type AfterContextOpts = {\n  waitUntil: RequestLifecycleOpts['waitUntil'] | undefined\n  onClose: RequestLifecycleOpts['onClose']\n  onTaskError: RequestLifecycleOpts['onAfterTaskError'] | undefined\n}\n\nexport class AfterContext {\n  private waitUntil: RequestLifecycleOpts['waitUntil'] | undefined\n  private onClose: RequestLifecycleOpts['onClose']\n  private onTaskError: RequestLifecycleOpts['onAfterTaskError'] | undefined\n\n  private runCallbacksOnClosePromise: Promise<void> | undefined\n  private callbackQueue: PromiseQueue\n  private workUnitStores = new Set<WorkUnitStore>()\n\n  constructor({ waitUntil, onClose, onTaskError }: AfterContextOpts) {\n    this.waitUntil = waitUntil\n    this.onClose = onClose\n    this.onTaskError = onTaskError\n\n    this.callbackQueue = new PromiseQueue()\n    this.callbackQueue.pause()\n  }\n\n  public after(task: AfterTask): void {\n    if (isThenable(task)) {\n      if (!this.waitUntil) {\n        errorWaitUntilNotAvailable()\n      }\n      this.waitUntil(\n        task.catch((error) => this.reportTaskError('promise', error))\n      )\n    } else if (typeof task === 'function') {\n      // TODO(after): implement tracing\n      this.addCallback(task)\n    } else {\n      throw new Error('`after()`: Argument must be a promise or a function')\n    }\n  }\n\n  private addCallback(callback: AfterCallback) {\n    // if something is wrong, throw synchronously, bubbling up to the `after` callsite.\n    if (!this.waitUntil) {\n      errorWaitUntilNotAvailable()\n    }\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      this.workUnitStores.add(workUnitStore)\n    }\n\n    const afterTaskStore = afterTaskAsyncStorage.getStore()\n\n    // This is used for checking if request APIs can be called inside `after`.\n    // Note that we need to check the phase in which the *topmost* `after` was called (which should be \"action\"),\n    // not the current phase (which might be \"after\" if we're in a nested after).\n    // Otherwise, we might allow `after(() => headers())`, but not `after(() => after(() => headers()))`.\n    const rootTaskSpawnPhase = afterTaskStore\n      ? afterTaskStore.rootTaskSpawnPhase // nested after\n      : workUnitStore?.phase // topmost after\n\n    // this should only happen once.\n    if (!this.runCallbacksOnClosePromise) {\n      this.runCallbacksOnClosePromise = this.runCallbacksOnClose()\n      this.waitUntil(this.runCallbacksOnClosePromise)\n    }\n\n    // Bind the callback to the current execution context (i.e. preserve all currently available ALS-es).\n    // We do this because we want all of these to be equivalent in every regard except timing:\n    //   after(() => x())\n    //   after(x())\n    //   await x()\n    const wrappedCallback = bindSnapshot(async () => {\n      try {\n        await afterTaskAsyncStorage.run({ rootTaskSpawnPhase }, () =>\n          callback()\n        )\n      } catch (error) {\n        this.reportTaskError('function', error)\n      }\n    })\n\n    this.callbackQueue.add(wrappedCallback)\n  }\n\n  private async runCallbacksOnClose() {\n    await new Promise<void>((resolve) => this.onClose!(resolve))\n    return this.runCallbacks()\n  }\n\n  private async runCallbacks(): Promise<void> {\n    if (this.callbackQueue.size === 0) return\n\n    for (const workUnitStore of this.workUnitStores) {\n      workUnitStore.phase = 'after'\n    }\n\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) {\n      throw new InvariantError('Missing workStore in AfterContext.runCallbacks')\n    }\n\n    return withExecuteRevalidates(workStore, () => {\n      this.callbackQueue.start()\n      return this.callbackQueue.onIdle()\n    })\n  }\n\n  private reportTaskError(taskKind: 'promise' | 'function', error: unknown) {\n    // TODO(after): this is fine for now, but will need better intergration with our error reporting.\n    // TODO(after): should we log this if we have a onTaskError callback?\n    console.error(\n      taskKind === 'promise'\n        ? `A promise passed to \\`after()\\` rejected:`\n        : `An error occurred in a function passed to \\`after()\\`:`,\n      error\n    )\n    if (this.onTaskError) {\n      // this is very defensive, but we really don't want anything to blow up in an error handler\n      try {\n        this.onTaskError?.(error)\n      } catch (handlerError) {\n        console.error(\n          new InvariantError(\n            '`onTaskError` threw while handling an error thrown from an `after` task',\n            {\n              cause: handlerError,\n            }\n          )\n        )\n      }\n    }\n  }\n}\n\nfunction errorWaitUntilNotAvailable(): never {\n  throw new Error(\n    '`after()` will not work correctly, because `waitUntil` is not available in the current environment.'\n  )\n}\n"], "names": ["PromiseQueue", "InvariantError", "isThenable", "workAsyncStorage", "withExecuteRevalidates", "bindSnapshot", "workUnitAsyncStorage", "afterTaskAsyncStorage", "AfterContext", "constructor", "waitUntil", "onClose", "onTaskError", "workUnitStores", "Set", "callback<PERSON><PERSON><PERSON>", "pause", "after", "task", "errorWaitUntilNotAvailable", "catch", "error", "reportTaskError", "addCallback", "Error", "callback", "workUnitStore", "getStore", "add", "afterTaskStore", "rootTaskSpawnPhase", "phase", "runCallbacksOnClosePromise", "runCallbacksOnClose", "wrappedCallback", "run", "Promise", "resolve", "runCallbacks", "size", "workStore", "start", "onIdle", "taskKind", "console", "handlerError", "cause"], "mappings": "AAAA,OAAOA,kBAAkB,6BAA4B;AAGrD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,UAAU,QAAQ,+BAA8B;AACzD,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,sBAAsB,QAAQ,wBAAuB;AAC9D,SAASC,YAAY,QAAQ,oCAAmC;AAChE,SACEC,oBAAoB,QAEf,iDAAgD;AACvD,SAASC,qBAAqB,QAAQ,kDAAiD;AAQvF,OAAO,MAAMC;IASXC,YAAY,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,EAAoB,CAAE;aAF3DC,iBAAiB,IAAIC;QAG3B,IAAI,CAACJ,SAAS,GAAGA;QACjB,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,WAAW,GAAGA;QAEnB,IAAI,CAACG,aAAa,GAAG,IAAIf;QACzB,IAAI,CAACe,aAAa,CAACC,KAAK;IAC1B;IAEOC,MAAMC,IAAe,EAAQ;QAClC,IAAIhB,WAAWgB,OAAO;YACpB,IAAI,CAAC,IAAI,CAACR,SAAS,EAAE;gBACnBS;YACF;YACA,IAAI,CAACT,SAAS,CACZQ,KAAKE,KAAK,CAAC,CAACC,QAAU,IAAI,CAACC,eAAe,CAAC,WAAWD;QAE1D,OAAO,IAAI,OAAOH,SAAS,YAAY;YACrC,iCAAiC;YACjC,IAAI,CAACK,WAAW,CAACL;QACnB,OAAO;YACL,MAAM,qBAAgE,CAAhE,IAAIM,MAAM,wDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE;IACF;IAEQD,YAAYE,QAAuB,EAAE;QAC3C,mFAAmF;QACnF,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;YACnBS;QACF;QAEA,MAAMO,gBAAgBpB,qBAAqBqB,QAAQ;QACnD,IAAID,eAAe;YACjB,IAAI,CAACb,cAAc,CAACe,GAAG,CAACF;QAC1B;QAEA,MAAMG,iBAAiBtB,sBAAsBoB,QAAQ;QAErD,0EAA0E;QAC1E,6GAA6G;QAC7G,6EAA6E;QAC7E,qGAAqG;QACrG,MAAMG,qBAAqBD,iBACvBA,eAAeC,kBAAkB,CAAC,eAAe;WACjDJ,iCAAAA,cAAeK,KAAK,CAAC,gBAAgB;;QAEzC,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAACC,0BAA0B,EAAE;YACpC,IAAI,CAACA,0BAA0B,GAAG,IAAI,CAACC,mBAAmB;YAC1D,IAAI,CAACvB,SAAS,CAAC,IAAI,CAACsB,0BAA0B;QAChD;QAEA,qGAAqG;QACrG,0FAA0F;QAC1F,qBAAqB;QACrB,eAAe;QACf,cAAc;QACd,MAAME,kBAAkB7B,aAAa;YACnC,IAAI;gBACF,MAAME,sBAAsB4B,GAAG,CAAC;oBAAEL;gBAAmB,GAAG,IACtDL;YAEJ,EAAE,OAAOJ,OAAO;gBACd,IAAI,CAACC,eAAe,CAAC,YAAYD;YACnC;QACF;QAEA,IAAI,CAACN,aAAa,CAACa,GAAG,CAACM;IACzB;IAEA,MAAcD,sBAAsB;QAClC,MAAM,IAAIG,QAAc,CAACC,UAAY,IAAI,CAAC1B,OAAO,CAAE0B;QACnD,OAAO,IAAI,CAACC,YAAY;IAC1B;IAEA,MAAcA,eAA8B;QAC1C,IAAI,IAAI,CAACvB,aAAa,CAACwB,IAAI,KAAK,GAAG;QAEnC,KAAK,MAAMb,iBAAiB,IAAI,CAACb,cAAc,CAAE;YAC/Ca,cAAcK,KAAK,GAAG;QACxB;QAEA,MAAMS,YAAYrC,iBAAiBwB,QAAQ;QAC3C,IAAI,CAACa,WAAW;YACd,MAAM,qBAAoE,CAApE,IAAIvC,eAAe,mDAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAAmE;QAC3E;QAEA,OAAOG,uBAAuBoC,WAAW;YACvC,IAAI,CAACzB,aAAa,CAAC0B,KAAK;YACxB,OAAO,IAAI,CAAC1B,aAAa,CAAC2B,MAAM;QAClC;IACF;IAEQpB,gBAAgBqB,QAAgC,EAAEtB,KAAc,EAAE;QACxE,iGAAiG;QACjG,qEAAqE;QACrEuB,QAAQvB,KAAK,CACXsB,aAAa,YACT,CAAC,yCAAyC,CAAC,GAC3C,CAAC,sDAAsD,CAAC,EAC5DtB;QAEF,IAAI,IAAI,CAACT,WAAW,EAAE;YACpB,2FAA2F;YAC3F,IAAI;gBACF,IAAI,CAACA,WAAW,oBAAhB,IAAI,CAACA,WAAW,MAAhB,IAAI,EAAeS;YACrB,EAAE,OAAOwB,cAAc;gBACrBD,QAAQvB,KAAK,CACX,qBAKC,CALD,IAAIpB,eACF,2EACA;oBACE6C,OAAOD;gBACT,IAJF,qBAAA;2BAAA;gCAAA;kCAAA;gBAKA;YAEJ;QACF;IACF;AACF;AAEA,SAAS1B;IACP,MAAM,qBAEL,CAFK,IAAIK,MACR,wGADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF"}