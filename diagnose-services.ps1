# 诊断服务状态

Write-Host "🔍 诊断服务状态..." -ForegroundColor Green

# 检查端口占用
Write-Host ""
Write-Host "📊 检查端口占用:" -ForegroundColor Blue

$ports = @(8001, 8002, 9001, 9002, 9003, 9004, 9005, 9006, 3000, 3001, 3002)
$activeServices = @()

foreach ($port in $ports) {
    $result = netstat -an | findstr ":$port"
    if ($result) {
        Write-Host "  ✅ 端口 $port 正在监听" -ForegroundColor Green
        $activeServices += $port
    } else {
        Write-Host "  ❌ 端口 $port 未监听" -ForegroundColor Red
    }
}

# 测试服务响应
Write-Host ""
Write-Host "🌐 测试服务响应:" -ForegroundColor Blue

foreach ($port in $activeServices) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$port" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
        Write-Host "  ✅ 端口 $port 响应正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
    }
    catch {
        Write-Host "  ❌ 端口 $port 无响应: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 检查健康检查端点
Write-Host ""
Write-Host "💊 测试健康检查端点:" -ForegroundColor Blue

$healthPorts = @(8001, 8002, 9001, 9002, 9003, 9004, 9005, 9006)
foreach ($port in $healthPorts) {
    if ($port -in $activeServices) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$port/health" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
            $content = $response.Content | ConvertFrom-Json
            Write-Host "  ✅ 端口 $port 健康检查正常: $($content.service)" -ForegroundColor Green
        }
        catch {
            Write-Host "  ❌ 端口 $port 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 检查进程
Write-Host ""
Write-Host "🔄 检查相关进程:" -ForegroundColor Blue

$processes = @("python", "go", "node", "npm")
foreach ($proc in $processes) {
    $running = Get-Process -Name $proc -ErrorAction SilentlyContinue
    if ($running) {
        Write-Host "  ✅ $proc 进程运行中 ($($running.Count) 个)" -ForegroundColor Green
        foreach ($p in $running) {
            Write-Host "    - PID: $($p.Id), CPU: $($p.CPU), 内存: $([math]::Round($p.WorkingSet/1MB, 2))MB" -ForegroundColor Gray
        }
    } else {
        Write-Host "  ❌ $proc 进程未运行" -ForegroundColor Red
    }
}

# 检查 PID 文件
Write-Host ""
Write-Host "📁 检查 PID 文件:" -ForegroundColor Blue

if (Test-Path ".pids") {
    $pidFiles = Get-ChildItem ".pids" -Filter "*.pid"
    if ($pidFiles) {
        Write-Host "  ✅ 找到 $($pidFiles.Count) 个 PID 文件" -ForegroundColor Green
        foreach ($file in $pidFiles) {
            $pid = Get-Content $file.FullName
            $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host "    ✅ $($file.BaseName): PID $pid (运行中)" -ForegroundColor Green
            } else {
                Write-Host "    ❌ $($file.BaseName): PID $pid (已停止)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "  ⚠️ .pids 目录存在但为空" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ❌ .pids 目录不存在" -ForegroundColor Red
}

# 总结
Write-Host ""
Write-Host "📋 诊断总结:" -ForegroundColor Yellow
Write-Host "  活跃端口: $($activeServices.Count)/$($ports.Count)" -ForegroundColor White
Write-Host "  建议操作:" -ForegroundColor White

if ($activeServices.Count -eq 0) {
    Write-Host "    - 所有服务都未启动，请运行: ./start-dev.sh" -ForegroundColor Cyan
} elseif ($activeServices.Count -lt $ports.Count) {
    Write-Host "    - 部分服务未启动，检查日志或重启: ./stop-dev.sh && ./start-dev.sh" -ForegroundColor Cyan
} else {
    Write-Host "    - 所有端口都在监听，但可能有响应问题" -ForegroundColor Cyan
    Write-Host "    - 检查服务日志或重启服务" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "🎯 下一步:" -ForegroundColor Green
Write-Host "  1. 如果服务无响应: ./stop-dev.sh && ./start-dev.sh" -ForegroundColor White
Write-Host "  2. 检查详细状态: ./status-dev.sh" -ForegroundColor White
Write-Host "  3. 访问服务: http://localhost:3000" -ForegroundColor White
