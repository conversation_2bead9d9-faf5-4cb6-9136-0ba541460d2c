#!/bin/bash

# 生科云码平台开发环境状态检查脚本
# 检查所有服务的运行状态

set -e

echo "🔍 检查生科云码平台服务状态..."

# 检测项目根目录
# 优先检查 biocloude 目录
if [ -f "/workspace/biocloude/docker-compose.yml" ] || [ -d "/workspace/biocloude/.devcontainer" ]; then
    PROJECT_ROOT="/workspace/biocloude"
    cd "$PROJECT_ROOT"
elif [ -f "docker-compose.yml" ] || [ -d ".devcontainer" ]; then
    PROJECT_ROOT=$(pwd)
else
    echo "❌ 无法找到项目根目录"
    echo "请确保在 /workspace/biocloude 目录中运行脚本"
    exit 1
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    local port="$1"
    local service_name="$2"
    
    if command -v lsof &> /dev/null; then
        if lsof -ti:$port >/dev/null 2>&1; then
            local pid=$(lsof -ti:$port)
            echo -e "  ✅ $service_name (端口 $port) - 运行中 (PID: $pid)"
            return 0
        else
            echo -e "  ❌ $service_name (端口 $port) - 未运行"
            return 1
        fi
    else
        # 使用netstat作为备选
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            echo -e "  ✅ $service_name (端口 $port) - 运行中"
            return 0
        else
            echo -e "  ❌ $service_name (端口 $port) - 未运行"
            return 1
        fi
    fi
}

# 检查HTTP服务
check_http_service() {
    local url="$1"
    local service_name="$2"
    
    if curl -f -s --max-time 5 "$url" > /dev/null 2>&1; then
        echo -e "  ✅ $service_name - HTTP响应正常"
        return 0
    else
        echo -e "  ❌ $service_name - HTTP无响应"
        return 1
    fi
}

# 检查基础服务
check_base_services() {
    echo ""
    log_info "🗄️ 基础服务状态:"
    
    local base_running=0
    
    # PostgreSQL
    if check_port "5432" "PostgreSQL"; then
        base_running=$((base_running + 1))
    fi
    
    # Redis
    if check_port "6379" "Redis"; then
        base_running=$((base_running + 1))
    fi
    
    # MailHog
    if check_port "1025" "MailHog SMTP"; then
        base_running=$((base_running + 1))
    fi
    
    if check_port "8025" "MailHog Web"; then
        base_running=$((base_running + 1))
    fi
    
    # MinIO
    if check_port "9000" "MinIO API"; then
        base_running=$((base_running + 1))
    fi
    
    if check_port "9090" "MinIO Console"; then
        base_running=$((base_running + 1))
    fi
    
    echo "  📊 基础服务: $base_running/6 运行中"
    return $base_running
}

# 检查后端服务
check_backend_services() {
    echo ""
    log_info "⚙️ 后端服务状态:"
    
    local backend_running=0
    
    # 认证服务
    if check_port "8001" "认证服务"; then
        backend_running=$((backend_running + 1))
        check_http_service "http://localhost:8001/health" "认证服务API"
    fi
    
    # 订阅服务
    if check_port "8002" "订阅服务"; then
        backend_running=$((backend_running + 1))
        check_http_service "http://localhost:8002/health" "订阅服务API"
    fi
    
    echo "  📊 后端服务: $backend_running/2 运行中"
    return $backend_running
}

# 检查AI服务
check_ai_services() {
    echo ""
    log_info "🤖 AI服务状态:"
    
    local ai_running=0
    
    # AI服务列表
    declare -A ai_services=(
        ["9001"]="资讯处理AI"
        ["9002"]="文献阅读AI"
        ["9003"]="引物AI"
        ["9004"]="蛋白质设计AI"
        ["9005"]="基因编辑AI"
        ["9006"]="代谢工程AI"
    )
    
    for port in "${!ai_services[@]}"; do
        if check_port "$port" "${ai_services[$port]}"; then
            ai_running=$((ai_running + 1))
            check_http_service "http://localhost:$port/health" "${ai_services[$port]} API"
        fi
    done
    
    echo "  📊 AI服务: $ai_running/6 运行中"
    return $ai_running
}

# 检查前端应用
check_frontend_apps() {
    echo ""
    log_info "🌐 前端应用状态:"
    
    local frontend_running=0
    
    # 主站门户
    if check_port "3000" "主站门户"; then
        frontend_running=$((frontend_running + 1))
        check_http_service "http://localhost:3000" "主站门户"
    fi
    
    # 资讯处理AI应用
    if check_port "3001" "资讯处理AI应用"; then
        frontend_running=$((frontend_running + 1))
        check_http_service "http://localhost:3001" "资讯处理AI应用"
    fi
    
    # 文献阅读AI应用
    if check_port "3002" "文献阅读AI应用"; then
        frontend_running=$((frontend_running + 1))
        check_http_service "http://localhost:3002" "文献阅读AI应用"
    fi
    
    echo "  📊 前端应用: $frontend_running/3 运行中"
    return $frontend_running
}

# 检查监控服务
check_monitoring_services() {
    echo ""
    log_info "📊 监控服务状态:"
    
    local monitoring_running=0
    
    # Prometheus
    if check_port "9091" "Prometheus"; then
        monitoring_running=$((monitoring_running + 1))
        check_http_service "http://localhost:9091" "Prometheus"
    fi
    
    # Grafana
    if check_port "3030" "Grafana"; then
        monitoring_running=$((monitoring_running + 1))
        check_http_service "http://localhost:3030" "Grafana"
    fi
    
    echo "  📊 监控服务: $monitoring_running/2 运行中"
    return $monitoring_running
}

# 检查PID文件
check_pid_files() {
    echo ""
    log_info "📁 PID文件状态:"
    
    if [ ! -d "$PROJECT_ROOT/.pids" ]; then
        echo "  ℹ️ PID目录不存在"
        return
    fi

    local pid_count=0
    local active_count=0

    for pid_file in "$PROJECT_ROOT/.pids"/*.pid; do
        if [ -f "$pid_file" ]; then
            pid_count=$((pid_count + 1))
            local service_name=$(basename "$pid_file" .pid)
            local pid=$(cat "$pid_file")
            
            if kill -0 "$pid" 2>/dev/null; then
                echo "  ✅ $service_name (PID: $pid) - 活跃"
                active_count=$((active_count + 1))
            else
                echo "  ❌ $service_name (PID: $pid) - 已停止"
            fi
        fi
    done
    
    if [ $pid_count -eq 0 ]; then
        echo "  ℹ️ 没有PID文件"
    else
        echo "  📊 PID文件: $active_count/$pid_count 活跃"
    fi
}

# 显示访问地址
show_access_urls() {
    echo ""
    log_info "🌐 访问地址:"
    
    echo "  📱 前端应用:"
    echo "    🏠 主站门户:     http://localhost:3000"
    echo "    📰 资讯处理AI:   http://localhost:3001"
    echo "    📚 文献阅读AI:   http://localhost:3002"
    echo ""
    echo "  🔧 API服务:"
    echo "    🔐 认证服务:     http://localhost:8001"
    echo "    💳 订阅服务:     http://localhost:8002"
    echo "    🤖 资讯AI:       http://localhost:9001"
    echo "    📖 文献AI:       http://localhost:9002"
    echo "    🧬 引物AI:       http://localhost:9003"
    echo "    🔬 蛋白质AI:     http://localhost:9004"
    echo "    ✂️ 基因编辑AI:   http://localhost:9005"
    echo "    ⚗️ 代谢工程AI:   http://localhost:9006"
    echo ""
    echo "  🛠️ 开发工具:"
    echo "    📧 MailHog:      http://localhost:8025"
    echo "    💾 MinIO:        http://localhost:9090"
    echo "    📊 Prometheus:   http://localhost:9091"
    echo "    📈 Grafana:      http://localhost:3030"
}

# 显示总结
show_summary() {
    local base_count=$1
    local backend_count=$2
    local ai_count=$3
    local frontend_count=$4
    local monitoring_count=$5
    
    local total_running=$((backend_count + ai_count + frontend_count))
    local total_services=11  # 2后端 + 6AI + 3前端
    
    echo ""
    echo "📋 状态总结:"
    echo "  🗄️ 基础服务: $base_count/6"
    echo "  ⚙️ 后端服务: $backend_count/2"
    echo "  🤖 AI服务: $ai_count/6"
    echo "  🌐 前端应用: $frontend_count/3"
    echo "  📊 监控服务: $monitoring_count/2"
    echo "  📊 应用服务总计: $total_running/$total_services"
    
    if [ $total_running -eq $total_services ]; then
        log_success "🎉 所有应用服务运行正常！"
    elif [ $total_running -gt 0 ]; then
        log_warning "⚠️ 部分服务未运行，请检查配置"
    else
        log_error "❌ 没有应用服务在运行"
        echo ""
        echo "💡 启动服务: ./start-dev.sh"
    fi
}

# 主函数
main() {
    echo "🔍 生科云码平台服务状态检查"
    echo "================================"
    
    # 检查各类服务
    check_base_services
    base_count=$?
    
    check_backend_services
    backend_count=$?
    
    check_ai_services
    ai_count=$?
    
    check_frontend_apps
    frontend_count=$?
    
    check_monitoring_services
    monitoring_count=$?
    
    check_pid_files
    show_access_urls
    show_summary $base_count $backend_count $ai_count $frontend_count $monitoring_count
    
    echo ""
    echo "⚡ 快捷命令:"
    echo "  启动服务: ./start-dev.sh"
    echo "  停止服务: ./stop-dev.sh"
    echo "  运行测试: ./test-dev.sh"
}

# 执行主函数
main "$@"
