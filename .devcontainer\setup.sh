#!/bin/bash

# 生科云码平台开发环境初始化脚本

set -e

echo "🚀 开始初始化生科云码平台开发环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_tools() {
    log_info "检查开发工具..."
    
    tools=("node" "npm" "go" "python3" "docker" "kubectl" "helm")
    
    for tool in "${tools[@]}"; do
        if command -v $tool &> /dev/null; then
            log_success "$tool 已安装"
        else
            log_error "$tool 未找到"
        fi
    done
}

# 安装前端依赖
install_frontend_deps() {
    log_info "安装前端依赖..."
    
    # 主站门户
    if [ -d "web-apps/main-portal" ]; then
        log_info "安装主站门户依赖..."
        cd web-apps/main-portal
        npm install
        cd ../..
        log_success "主站门户依赖安装完成"
    fi
    
    # 资讯处理AI应用
    if [ -d "web-apps/newsletter-app" ]; then
        log_info "安装资讯处理AI应用依赖..."
        cd web-apps/newsletter-app
        npm install
        cd ../..
        log_success "资讯处理AI应用依赖安装完成"
    fi
    
    # 文献阅读AI应用
    if [ -d "web-apps/scholar-app" ]; then
        log_info "安装文献阅读AI应用依赖..."
        cd web-apps/scholar-app
        npm install
        cd ../..
        log_success "文献阅读AI应用依赖安装完成"
    fi
}

# 安装后端依赖
install_backend_deps() {
    log_info "安装后端依赖..."
    
    # Go服务依赖
    go_services=("auth-service" "subscription-service")
    
    for service in "${go_services[@]}"; do
        if [ -d "services/$service" ]; then
            log_info "安装 $service 依赖..."
            cd "services/$service"
            go mod download
            go mod tidy
            cd ../..
            log_success "$service 依赖安装完成"
        fi
    done
    
    # Python AI服务依赖
    ai_services=("newsletter-ai" "scholar-ai" "primer-ai" "protein-ai" "gene-editing-ai" "metabolic-ai")
    
    for service in "${ai_services[@]}"; do
        if [ -d "services/ai-services/$service" ]; then
            log_info "安装 $service 依赖..."
            cd "services/ai-services/$service"
            if [ -f "requirements.txt" ]; then
                pip install -r requirements.txt
            fi
            cd ../../..
            log_success "$service 依赖安装完成"
        fi
    done
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    directories=(
        "logs"
        "data"
        "uploads"
        "backups"
        ".devcontainer/data"
        "services/ai-services/newsletter-ai/logs"
        "services/ai-services/scholar-ai/logs"
        "services/ai-services/primer-ai/logs"
        "services/ai-services/protein-ai/logs"
        "services/ai-services/gene-editing-ai/logs"
        "services/ai-services/metabolic-ai/logs"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_success "创建目录: $dir"
        fi
    done
}

# 设置环境变量文件
setup_env_files() {
    log_info "设置环境变量文件..."
    
    # 根目录环境变量
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# 生科云码平台环境变量

# 数据库配置
DATABASE_URL=*************************************************/biocloude
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=biocloude
POSTGRES_USER=biocloude
POSTGRES_PASSWORD=biocloude123

# Redis配置
REDIS_URL=redis://:biocloude123@redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=biocloude123

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# API配置
API_BASE_URL=http://localhost:8001
NEXT_PUBLIC_API_URL=http://localhost:8001

# AI服务配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# 邮件配置 (开发环境使用MailHog)
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# 存储配置 (开发环境使用MinIO)
S3_ENDPOINT=http://minio:9000
S3_ACCESS_KEY=biocloude
S3_SECRET_KEY=biocloude123
S3_BUCKET=biocloude-dev

# 环境配置
NODE_ENV=development
ENVIRONMENT=development
LOG_LEVEL=debug

# 监控配置
PROMETHEUS_URL=http://prometheus:9090
GRAFANA_URL=http://grafana:3000
EOF
        log_success "创建 .env 文件"
    fi
    
    # 为各个服务创建环境变量文件
    services=("auth-service" "subscription-service")
    for service in "${services[@]}"; do
        if [ -d "services/$service" ] && [ ! -f "services/$service/.env" ]; then
            cp .env "services/$service/.env"
            log_success "创建 services/$service/.env 文件"
        fi
    done
    
    ai_services=("newsletter-ai" "scholar-ai" "primer-ai" "protein-ai" "gene-editing-ai" "metabolic-ai")
    for service in "${ai_services[@]}"; do
        if [ -d "services/ai-services/$service" ] && [ ! -f "services/ai-services/$service/.env" ]; then
            cp .env "services/ai-services/$service/.env"
            log_success "创建 services/ai-services/$service/.env 文件"
        fi
    done
}

# 设置Git hooks
setup_git_hooks() {
    log_info "设置Git hooks..."
    
    if [ -d ".git" ]; then
        # Pre-commit hook
        cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# 生科云码平台 pre-commit hook

echo "🔍 运行代码检查..."

# 检查Go代码
if find . -name "*.go" -not -path "./vendor/*" | grep -q .; then
    echo "检查Go代码格式..."
    if ! gofmt -l $(find . -name "*.go" -not -path "./vendor/*") | grep -q .; then
        echo "✅ Go代码格式正确"
    else
        echo "❌ Go代码格式不正确，请运行 gofmt -w ."
        exit 1
    fi
fi

# 检查Python代码
if find . -name "*.py" -not -path "./venv/*" -not -path "./.venv/*" | grep -q .; then
    echo "检查Python代码格式..."
    if command -v black &> /dev/null; then
        black --check --diff $(find . -name "*.py" -not -path "./venv/*" -not -path "./.venv/*")
    fi
fi

# 检查TypeScript/JavaScript代码
if find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | grep -q .; then
    echo "检查TypeScript/JavaScript代码..."
    if command -v eslint &> /dev/null; then
        npx eslint --ext .ts,.tsx,.js,.jsx .
    fi
fi

echo "✅ 代码检查完成"
EOF
        chmod +x .git/hooks/pre-commit
        log_success "设置 pre-commit hook"
    fi
}

# 初始化数据库
init_database() {
    log_info "等待数据库启动..."
    
    # 等待PostgreSQL启动
    max_attempts=30
    attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if pg_isready -h postgres -p 5432 -U biocloude &> /dev/null; then
            log_success "PostgreSQL 已启动"
            break
        fi
        
        attempt=$((attempt + 1))
        log_info "等待PostgreSQL启动... ($attempt/$max_attempts)"
        sleep 2
    done
    
    if [ $attempt -eq $max_attempts ]; then
        log_warning "PostgreSQL 启动超时，请手动检查"
    fi
}

# 创建开发脚本
create_dev_scripts() {
    log_info "创建开发脚本..."

    # 检查根目录脚本是否存在
    if [ ! -f "../start-dev.sh" ]; then
        log_info "根目录启动脚本不存在，将在setup完成后提醒用户"
    fi

    if [ ! -f "../stop-dev.sh" ]; then
        log_info "根目录停止脚本不存在，将在setup完成后提醒用户"
    fi

    if [ ! -f "../test-dev.sh" ]; then
        log_info "根目录测试脚本不存在，将在setup完成后提醒用户"
    fi

    if [ ! -f "../status-dev.sh" ]; then
        log_info "根目录状态脚本不存在，将在setup完成后提醒用户"
    fi

    log_success "开发脚本检查完成"
}

# 主函数
main() {
    log_info "🧬 生科云码平台开发环境初始化开始..."
    
    check_tools
    create_directories
    setup_env_files
    install_frontend_deps
    install_backend_deps
    setup_git_hooks
    init_database
    create_dev_scripts
    
    log_success "🎉 开发环境初始化完成！"
    
    echo ""
    echo "📋 下一步操作："
    echo "1. 启动开发环境: ./start-dev.sh"
    echo "2. 运行测试: ./test-dev.sh"
    echo "3. 访问应用:"
    echo "   - 主站门户: http://localhost:3000"
    echo "   - 资讯处理AI: http://localhost:3001"
    echo "   - 文献阅读AI: http://localhost:3002"
    echo "   - MailHog (邮件): http://localhost:8025"
    echo "   - MinIO (存储): http://localhost:9090"
    echo "   - Grafana (监控): http://localhost:3030"
    echo ""
    echo "🔧 开发工具："
    echo "   - PostgreSQL: localhost:5432"
    echo "   - Redis: localhost:6379"
    echo "   - Prometheus: localhost:9091"
    echo ""
}

# 执行主函数
main "$@"
