#!/bin/bash

# 生科云码平台服务启动脚本

set -e

echo "🚀 启动生科云码平台服务..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 等待服务启动
wait_for_service() {
    local service_name=$1
    local host=$2
    local port=$3
    local max_attempts=60  # 增加到60次尝试 (2分钟)
    local attempt=0

    log_info "等待 $service_name 启动..."

    while [ $attempt -lt $max_attempts ]; do
        if nc -z $host $port &> /dev/null; then
            log_success "$service_name 已启动 ($host:$port)"
            return 0
        fi

        attempt=$((attempt + 1))
        log_info "等待 $service_name 启动... ($attempt/$max_attempts)"
        sleep 2
    done

    log_warning "$service_name 启动超时"
    return 1
}

# 等待PostgreSQL专用函数
wait_for_postgresql() {
    local max_attempts=90  # 3分钟
    local attempt=0

    log_info "等待 PostgreSQL 启动..."

    while [ $attempt -lt $max_attempts ]; do
        # 首先检查端口是否开放
        if nc -z localhost 5432 &> /dev/null; then
            # 然后使用 pg_isready 检查是否真正准备好
            if command -v pg_isready &> /dev/null; then
                if pg_isready -h localhost -p 5432 -U biocloude &> /dev/null; then
                    log_success "PostgreSQL 已启动并准备就绪 (localhost:5432)"
                    return 0
                fi
            else
                # 如果没有 pg_isready，尝试简单连接测试
                if psql -h localhost -p 5432 -U biocloude -d biocloude -c "SELECT 1;" &> /dev/null; then
                    log_success "PostgreSQL 已启动并准备就绪 (localhost:5432)"
                    return 0
                fi
            fi
        fi

        attempt=$((attempt + 1))
        if [ $((attempt % 10)) -eq 0 ]; then
            log_info "等待 PostgreSQL 启动... ($attempt/$max_attempts) - 数据库初始化需要时间"
        fi
        sleep 2
    done

    log_error "PostgreSQL 启动超时"
    log_info "尝试手动检查："
    log_info "  docker ps | grep postgres"
    log_info "  docker logs <postgres-container>"
    return 1
}

# 检查HTTP服务
check_http_service() {
    local service_name=$1
    local url=$2
    local max_attempts=15
    local attempt=0
    
    log_info "检查 $service_name HTTP服务..."
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -f -s $url > /dev/null 2>&1; then
            log_success "$service_name HTTP服务正常 ($url)"
            return 0
        fi
        
        attempt=$((attempt + 1))
        log_info "检查 $service_name HTTP服务... ($attempt/$max_attempts)"
        sleep 2
    done
    
    log_warning "$service_name HTTP服务检查超时"
    return 1
}

# 主函数
main() {
    log_info "🔧 检查基础服务状态..."
    
    # 等待PostgreSQL (使用专用函数)
    wait_for_postgresql
    
    # 等待Redis
    wait_for_service "Redis" "localhost" "6379"

    # 等待MailHog
    wait_for_service "MailHog" "localhost" "1025"

    # 等待MinIO
    wait_for_service "MinIO" "localhost" "9000"
    
    log_info "📊 检查监控服务..."
    
    # 等待Prometheus
    wait_for_service "Prometheus" "localhost" "9091"

    # 等待Grafana
    wait_for_service "Grafana" "localhost" "3030"
    
    log_info "🌐 检查HTTP服务..."
    
    # 检查MailHog Web界面
    check_http_service "MailHog Web" "http://localhost:8025"

    # 检查MinIO Console
    check_http_service "MinIO Console" "http://localhost:9090"

    # 检查Prometheus
    check_http_service "Prometheus" "http://localhost:9091"

    # 检查Grafana
    check_http_service "Grafana" "http://localhost:3030"
    
    log_success "✅ 所有基础服务已启动完成！"
    
    echo ""
    echo "📋 服务访问地址："
    echo "🗄️  PostgreSQL: localhost:5432 (用户: biocloude, 密码: biocloude123)"
    echo "🔴 Redis: localhost:6379 (密码: biocloude123)"
    echo "📧 MailHog: http://localhost:8025 (SMTP: localhost:1025)"
    echo "💾 MinIO: http://localhost:9090 (用户: biocloude, 密码: biocloude123)"
    echo "📊 Prometheus: http://localhost:9091"
    echo "📈 Grafana: http://localhost:3030 (用户: admin, 密码: biocloude123)"
    echo ""
    echo "🚀 现在可以启动应用服务了："
    echo "   ./start-dev.sh"
    echo ""
}

# 执行主函数
main "$@"
