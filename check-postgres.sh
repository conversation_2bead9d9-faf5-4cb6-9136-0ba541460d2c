#!/bin/bash

# PostgreSQL 状态检查和诊断脚本

echo "🔍 PostgreSQL 状态检查和诊断..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口
check_port() {
    log_info "检查端口 5432..."
    
    if command -v nc &> /dev/null; then
        if nc -z localhost 5432 2>/dev/null; then
            log_success "端口 5432 已开放"
            return 0
        else
            log_error "端口 5432 未开放"
            return 1
        fi
    else
        log_warning "nc 命令不可用，跳过端口检查"
        return 1
    fi
}

# 检查进程
check_process() {
    log_info "检查 PostgreSQL 进程..."
    
    if pgrep -f postgres > /dev/null; then
        log_success "PostgreSQL 进程正在运行"
        echo "进程信息："
        pgrep -f postgres | head -5 | while read pid; do
            echo "  PID: $pid - $(ps -p $pid -o comm= 2>/dev/null || echo 'unknown')"
        done
        return 0
    else
        log_error "PostgreSQL 进程未运行"
        return 1
    fi
}

# 检查 Docker 容器
check_docker() {
    log_info "检查 Docker 容器..."
    
    if command -v docker &> /dev/null; then
        local postgres_containers=$(docker ps --filter "name=postgres" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}")
        
        if [ -n "$postgres_containers" ] && [ "$postgres_containers" != "NAMES	STATUS	PORTS" ]; then
            log_success "找到 PostgreSQL Docker 容器："
            echo "$postgres_containers"
            
            # 检查容器日志
            local container_name=$(docker ps --filter "name=postgres" --format "{{.Names}}" | head -1)
            if [ -n "$container_name" ]; then
                log_info "最近的容器日志 ($container_name)："
                docker logs --tail 10 "$container_name" 2>&1 | sed 's/^/  /'
            fi
            return 0
        else
            log_error "未找到运行中的 PostgreSQL Docker 容器"
            
            # 检查所有容器（包括停止的）
            local all_postgres=$(docker ps -a --filter "name=postgres" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}")
            if [ -n "$all_postgres" ] && [ "$all_postgres" != "NAMES	STATUS	PORTS" ]; then
                log_info "找到停止的 PostgreSQL 容器："
                echo "$all_postgres"
            fi
            return 1
        fi
    else
        log_warning "Docker 命令不可用"
        return 1
    fi
}

# 检查 pg_isready
check_pg_isready() {
    log_info "使用 pg_isready 检查..."
    
    if command -v pg_isready &> /dev/null; then
        if pg_isready -h localhost -p 5432 -U biocloude; then
            log_success "PostgreSQL 已准备就绪"
            return 0
        else
            log_error "PostgreSQL 未准备就绪"
            return 1
        fi
    else
        log_warning "pg_isready 命令不可用"
        return 1
    fi
}

# 尝试连接
test_connection() {
    log_info "尝试连接数据库..."
    
    if command -v psql &> /dev/null; then
        if PGPASSWORD=biocloude123 psql -h localhost -p 5432 -U biocloude -d biocloude -c "SELECT version();" 2>/dev/null; then
            log_success "数据库连接成功"
            return 0
        else
            log_error "数据库连接失败"
            log_info "尝试连接详细信息："
            PGPASSWORD=biocloude123 psql -h localhost -p 5432 -U biocloude -d biocloude -c "SELECT version();" 2>&1 | sed 's/^/  /'
            return 1
        fi
    else
        log_warning "psql 命令不可用"
        return 1
    fi
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量..."
    
    echo "相关环境变量："
    env | grep -E "(POSTGRES|DATABASE)" | sed 's/^/  /' || echo "  未找到相关环境变量"
    
    if [ -f ".env" ]; then
        log_info "检查 .env 文件："
        grep -E "(POSTGRES|DATABASE)" .env | sed 's/^/  /' || echo "  .env 文件中未找到相关配置"
    fi
}

# 检查网络
check_network() {
    log_info "检查网络连接..."
    
    # 检查本地回环
    if ping -c 1 localhost > /dev/null 2>&1; then
        log_success "localhost 可达"
    else
        log_error "localhost 不可达"
    fi
    
    # 检查端口监听
    if command -v netstat &> /dev/null; then
        log_info "端口监听状态："
        netstat -tlnp 2>/dev/null | grep :5432 | sed 's/^/  /' || echo "  端口 5432 未监听"
    elif command -v ss &> /dev/null; then
        log_info "端口监听状态："
        ss -tlnp | grep :5432 | sed 's/^/  /' || echo "  端口 5432 未监听"
    fi
}

# 提供解决建议
suggest_solutions() {
    echo ""
    log_info "🔧 可能的解决方案："
    echo ""
    echo "1. 🐳 如果使用 Docker Compose："
    echo "   docker-compose up -d postgres"
    echo "   docker-compose logs postgres"
    echo ""
    echo "2. 🔄 重启服务："
    echo "   .devcontainer/start-services.sh"
    echo ""
    echo "3. 📋 检查 Docker 状态："
    echo "   docker ps -a"
    echo "   docker-compose ps"
    echo ""
    echo "4. 🗄️ 手动启动 PostgreSQL："
    echo "   sudo service postgresql start"
    echo "   或"
    echo "   pg_ctl start -D /var/lib/postgresql/data"
    echo ""
    echo "5. 🔍 查看详细日志："
    echo "   docker logs <postgres-container-name>"
    echo "   journalctl -u postgresql"
    echo ""
    echo "6. ⏰ 等待更长时间："
    echo "   PostgreSQL 初始化可能需要 2-5 分钟"
    echo ""
    echo "7. 🌐 在 Codespaces 中："
    echo "   基础服务应该自动启动"
    echo "   如果没有，请检查 .devcontainer/docker-compose.yml"
}

# 主函数
main() {
    echo "🔍 PostgreSQL 诊断开始..."
    echo "================================"
    
    local checks_passed=0
    local total_checks=6
    
    # 运行所有检查
    check_port && checks_passed=$((checks_passed + 1))
    check_process && checks_passed=$((checks_passed + 1))
    check_docker && checks_passed=$((checks_passed + 1))
    check_pg_isready && checks_passed=$((checks_passed + 1))
    test_connection && checks_passed=$((checks_passed + 1))
    check_network && checks_passed=$((checks_passed + 1))
    
    echo ""
    echo "📊 检查结果: $checks_passed/$total_checks 通过"
    
    check_environment
    
    if [ $checks_passed -eq $total_checks ]; then
        echo ""
        log_success "🎉 PostgreSQL 运行正常！"
    elif [ $checks_passed -gt 0 ]; then
        echo ""
        log_warning "⚠️ PostgreSQL 部分功能正常，但存在问题"
        suggest_solutions
    else
        echo ""
        log_error "❌ PostgreSQL 未正常运行"
        suggest_solutions
    fi
    
    echo ""
    echo "💡 如果问题持续存在，请："
    echo "   1. 等待更长时间（PostgreSQL 初始化需要时间）"
    echo "   2. 检查 Docker 容器状态"
    echo "   3. 查看容器日志"
    echo "   4. 重启开发环境"
}

# 执行主函数
main "$@"
