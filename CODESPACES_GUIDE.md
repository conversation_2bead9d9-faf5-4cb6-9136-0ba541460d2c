# 🌐 GitHub Codespaces 使用指南

## 🚀 快速开始

### 1. 创建 Codespace
1. 访问 GitHub 仓库
2. 点击 **Code** → **Codespaces** → **Create codespace on main**
3. 等待环境初始化（约 3-5 分钟）

### 2. 启动服务
```bash
# 如果遇到权限问题，先修复
chmod +x *.sh

# 启动所有服务
./start-dev.sh
```

### 3. 检查状态
```bash
# 检查所有服务状态
./status-dev.sh

# 专门检查 PostgreSQL
./check-postgres.sh
```

## 🔧 常见问题解决

### ❌ 脚本无法执行
```bash
# 问题：./start-dev.sh: No such file or directory
# 解决：
chmod +x *.sh && ./start-dev.sh

# 或使用 bash 运行
bash start-dev.sh
```

### ⏰ PostgreSQL 启动超时
```bash
# 问题：PostgreSQL 启动超时，请先启动基础服务
# 解决：

# 方案1: 等待更长时间（推荐）
echo "等待 PostgreSQL 启动..." && while ! pg_isready -h localhost -p 5432 -U biocloude 2>/dev/null; do echo "仍在等待..."; sleep 5; done && echo "PostgreSQL 已就绪！"

# 方案2: 运行诊断
./check-postgres.sh

# 方案3: 重启基础服务
.devcontainer/start-services.sh
```

### 🔌 端口冲突
```bash
# 问题：端口被占用
# 解决：
./stop-dev.sh
sleep 5
./start-dev.sh
```

## 📋 服务启动顺序

### 自动启动（Codespaces 创建时）
1. **环境初始化** - `.devcontainer/setup.sh`
2. **基础服务启动** - `.devcontainer/start-services.sh`
   - PostgreSQL (localhost:5432)
   - Redis (localhost:6379)
   - MailHog (localhost:8025)
   - MinIO (localhost:9090)
   - Prometheus (localhost:9091)
   - Grafana (localhost:3030)

### 手动启动（用户操作）
3. **应用服务启动** - `./start-dev.sh`
   - 后端服务 (localhost:8001-8002)
   - AI 服务 (localhost:9001-9006)
   - 前端应用 (localhost:3000-3002)

## ⏱️ 预期启动时间

| 服务类型 | 启动时间 | 说明 |
|---------|---------|------|
| PostgreSQL | 2-5 分钟 | 数据库初始化需要时间 |
| Redis | 10-30 秒 | 通常很快 |
| MailHog | 5-15 秒 | 轻量级服务 |
| MinIO | 15-30 秒 | 对象存储服务 |
| Go 服务 | 30-60 秒 | 编译和启动 |
| Python AI 服务 | 1-2 分钟 | 依赖加载 |
| Node.js 应用 | 1-3 分钟 | 依赖安装和构建 |

## 🎯 最佳实践

### ✅ 推荐做法
1. **耐心等待** - PostgreSQL 初始化需要时间
2. **使用诊断工具** - `./check-postgres.sh`, `./status-dev.sh`
3. **按顺序启动** - 先等基础服务，再启动应用
4. **检查日志** - 如果有问题，查看具体错误信息

### ❌ 避免做法
1. **不要急于重启** - 给服务足够的启动时间
2. **不要忽略错误** - 仔细阅读错误信息
3. **不要手动杀进程** - 使用 `./stop-dev.sh`

## 🔍 诊断工具

### 快速检查
```bash
# 检查所有服务
./status-dev.sh

# 检查 PostgreSQL
./check-postgres.sh

# 检查端口
netstat -tulpn | grep LISTEN
```

### 详细诊断
```bash
# 查看 Docker 容器
docker ps -a

# 查看容器日志
docker logs <container-name>

# 查看系统资源
htop
df -h
```

## 🌐 访问地址

### 前端应用
- 🏠 **主站门户**: http://localhost:3000
- 📰 **资讯处理AI**: http://localhost:3001
- 📚 **文献阅读AI**: http://localhost:3002

### API 服务
- 🔐 **认证服务**: http://localhost:8001
- 💳 **订阅服务**: http://localhost:8002
- 🤖 **AI 服务**: http://localhost:9001-9006

### 开发工具
- 📧 **MailHog**: http://localhost:8025
- 💾 **MinIO**: http://localhost:9090
- 📊 **Prometheus**: http://localhost:9091
- 📈 **Grafana**: http://localhost:3030

## 🆘 故障排除流程

### 1. 基础检查
```bash
# 检查脚本权限
ls -la *.sh

# 修复权限（如果需要）
chmod +x *.sh
```

### 2. 服务状态检查
```bash
# 全面状态检查
./status-dev.sh

# PostgreSQL 专项检查
./check-postgres.sh
```

### 3. 重启服务
```bash
# 停止所有服务
./stop-dev.sh

# 等待几秒
sleep 5

# 重新启动
./start-dev.sh
```

### 4. 查看日志
```bash
# Docker 容器日志
docker logs <container-name>

# 应用日志
tail -f logs/*.log
```

### 5. 完全重置
```bash
# 重新创建 Codespace
# 或重新运行初始化
.devcontainer/setup.sh
```

## 📞 获取帮助

### 自助资源
- 📖 [QUICK_FIX.md](QUICK_FIX.md) - 快速修复指南
- 📖 [TROUBLESHOOTING.md](TROUBLESHOOTING.md) - 详细故障排除
- 📖 [SCRIPTS_GUIDE.md](SCRIPTS_GUIDE.md) - 脚本使用指南

### 寻求帮助
1. 🔍 **运行诊断**: `./status-dev.sh` 和 `./check-postgres.sh`
2. 📋 **收集信息**: 错误信息、日志、环境详情
3. 🐛 **创建 Issue**: 在 GitHub 上报告问题
4. 💬 **团队支持**: 联系开发团队

## 💡 提示

- 🕐 **PostgreSQL 启动慢是正常的** - 数据库初始化需要时间
- 🔄 **重启通常能解决问题** - 大多数问题可以通过重启解决
- 📊 **使用状态检查工具** - 定期运行 `./status-dev.sh`
- 🌐 **Codespaces 提供最佳体验** - 避免本地环境问题

---

🎉 **现在您已经掌握了在 Codespaces 中使用生科云码平台的所有技巧！**
