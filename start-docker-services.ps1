# Windows 环境下启动 Docker 基础服务脚本

param(
    [switch]$Help,
    [switch]$Force
)

if ($Help) {
    Write-Host "Windows 环境下启动 Docker 基础服务" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法:"
    Write-Host "  .\start-docker-services.ps1        启动基础服务"
    Write-Host "  .\start-docker-services.ps1 -Force 强制重启服务"
    Write-Host "  .\start-docker-services.ps1 -Help  显示帮助"
    Write-Host ""
    Write-Host "启动的服务:"
    Write-Host "  - PostgreSQL (端口 5432)"
    Write-Host "  - <PERSON>is (端口 6379)"
    Write-Host "  - <PERSON><PERSON><PERSON> (端口 1025, 8025)"
    Write-Host "  - <PERSON><PERSON> (端口 9000, 9090)"
    Write-Host "  - Prometheus (端口 9091)"
    Write-Host "  - <PERSON><PERSON> (端口 3030)"
    exit 0
}

Write-Host "🐳 启动 Docker 基础服务..." -ForegroundColor Green

# 检查 Docker 是否安装
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not found"
    }
    Write-Host "✅ Docker 已安装: $dockerVersion" -ForegroundColor Green
}
catch {
    Write-Host "❌ Docker 未安装或未运行" -ForegroundColor Red
    Write-Host ""
    Write-Host "请先安装 Docker Desktop:" -ForegroundColor Yellow
    Write-Host "1. 访问 https://www.docker.com/products/docker-desktop/"
    Write-Host "2. 下载并安装 Docker Desktop for Windows"
    Write-Host "3. 启动 Docker Desktop"
    Write-Host "4. 等待 Docker 完全启动"
    Write-Host ""
    Write-Host "或者使用 GitHub Codespaces (推荐):" -ForegroundColor Cyan
    Write-Host "1. 访问 GitHub 仓库"
    Write-Host "2. 点击 Code → Codespaces → Create codespace"
    Write-Host ""
    Read-Host "按回车键退出"
    exit 1
}

# 检查 Docker 是否运行
try {
    docker ps >$null 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not running"
    }
    Write-Host "✅ Docker 正在运行" -ForegroundColor Green
}
catch {
    Write-Host "❌ Docker 未运行" -ForegroundColor Red
    Write-Host "请启动 Docker Desktop，然后重新运行此脚本" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 检查 docker-compose 文件
if (-not (Test-Path ".devcontainer/docker-compose.yml")) {
    Write-Host "❌ 未找到 docker-compose.yml 文件" -ForegroundColor Red
    Write-Host "请确保在项目根目录运行此脚本" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "📋 检查现有容器..." -ForegroundColor Blue

# 如果使用 -Force 参数，先停止现有容器
if ($Force) {
    Write-Host "🛑 停止现有容器..." -ForegroundColor Yellow
    docker-compose -f .devcontainer/docker-compose.yml down
    Start-Sleep -Seconds 5
}

# 启动服务
Write-Host "🚀 启动基础服务..." -ForegroundColor Blue
Write-Host "这可能需要几分钟时间，请耐心等待..." -ForegroundColor Yellow

try {
    # 启动服务
    docker-compose -f .devcontainer/docker-compose.yml up -d
    
    if ($LASTEXITCODE -ne 0) {
        throw "Docker compose failed"
    }
    
    Write-Host "✅ Docker 容器已启动" -ForegroundColor Green
}
catch {
    Write-Host "❌ 启动 Docker 服务失败" -ForegroundColor Red
    Write-Host "请检查 Docker Desktop 是否正常运行" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 等待服务启动
Write-Host ""
Write-Host "⏰ 等待服务启动..." -ForegroundColor Blue

# 检查容器状态
Write-Host "📊 检查容器状态..." -ForegroundColor Cyan
docker-compose -f .devcontainer/docker-compose.yml ps

# 等待 PostgreSQL 启动
Write-Host ""
Write-Host "🗄️ 等待 PostgreSQL 启动..." -ForegroundColor Blue
$maxAttempts = 60  # 2分钟
$attempt = 0

do {
    Start-Sleep -Seconds 2
    $attempt++
    
    # 检查 PostgreSQL 容器是否运行
    $postgresContainer = docker ps --filter "name=postgres" --format "{{.Names}}" | Select-Object -First 1
    
    if ($postgresContainer) {
        # 检查 PostgreSQL 是否准备就绪
        $result = docker exec $postgresContainer pg_isready -U biocloude 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PostgreSQL 已启动并准备就绪" -ForegroundColor Green
            break
        }
    }
    
    if ($attempt % 10 -eq 0) {
        Write-Host "   仍在等待 PostgreSQL... ($attempt/$maxAttempts)" -ForegroundColor Yellow
    }
    
} while ($attempt -lt $maxAttempts)

if ($attempt -eq $maxAttempts) {
    Write-Host "⚠️ PostgreSQL 启动超时，但容器可能仍在初始化" -ForegroundColor Yellow
    Write-Host "   数据库初始化可能需要更多时间" -ForegroundColor Yellow
} else {
    Write-Host "✅ PostgreSQL 启动完成" -ForegroundColor Green
}

# 检查其他服务
Write-Host ""
Write-Host "🔍 检查其他服务..." -ForegroundColor Blue

$services = @(
    @{Name="Redis"; Port=6379},
    @{Name="MailHog SMTP"; Port=1025},
    @{Name="MailHog Web"; Port=8025},
    @{Name="MinIO API"; Port=9000},
    @{Name="MinIO Console"; Port=9090}
)

foreach ($service in $services) {
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $service.Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✅ $($service.Name) (端口 $($service.Port)) - 运行中" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $($service.Name) (端口 $($service.Port)) - 未就绪" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "❌ $($service.Name) (端口 $($service.Port)) - 检查失败" -ForegroundColor Red
    }
}

# 显示访问信息
Write-Host ""
Write-Host "🎉 基础服务启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 服务访问地址:" -ForegroundColor Yellow
Write-Host "  🗄️ PostgreSQL:    localhost:5432 (用户: biocloude, 密码: biocloude123)"
Write-Host "  🔴 Redis:          localhost:6379 (密码: biocloude123)"
Write-Host "  📧 MailHog:        http://localhost:8025 (SMTP: localhost:1025)"
Write-Host "  💾 MinIO:          http://localhost:9090 (用户: biocloude, 密码: biocloude123)"
Write-Host "  📊 Prometheus:     http://localhost:9091"
Write-Host "  📈 Grafana:        http://localhost:3030 (用户: admin, 密码: biocloude123)"
Write-Host ""
Write-Host "⚡ 下一步:" -ForegroundColor Cyan
Write-Host "  启动应用服务:      .\start-dev.ps1"
Write-Host "  检查服务状态:      .\status-dev.ps1"
Write-Host "  查看容器日志:      docker-compose -f .devcontainer/docker-compose.yml logs"
Write-Host ""
Write-Host "💡 提示:" -ForegroundColor Cyan
Write-Host "  - PostgreSQL 初始化可能需要几分钟"
Write-Host "  - 如果服务未就绪，请等待更长时间"
Write-Host "  - 使用 GitHub Codespaces 可以避免本地环境问题"
Write-Host ""

Read-Host "按回车键关闭"
