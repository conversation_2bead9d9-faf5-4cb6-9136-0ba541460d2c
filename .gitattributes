# 生科云码平台 Git 属性配置
# 确保跨平台的行结束符一致性

# 默认行为：自动检测文本文件并规范化行结束符
* text=auto

# 源代码文件 - 强制使用 LF
*.go text eol=lf
*.py text eol=lf
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf
*.yaml text eol=lf
*.yml text eol=lf
*.toml text eol=lf
*.xml text eol=lf
*.html text eol=lf
*.css text eol=lf
*.scss text eol=lf
*.sass text eol=lf
*.less text eol=lf
*.sql text eol=lf

# 配置文件 - 强制使用 LF
*.conf text eol=lf
*.config text eol=lf
*.ini text eol=lf
*.env text eol=lf
*.env.* text eol=lf
.env* text eol=lf
.gitignore text eol=lf
.gitattributes text eol=lf
.editorconfig text eol=lf
.eslintrc* text eol=lf
.prettierrc* text eol=lf

# Docker 相关文件 - 强制使用 LF
Dockerfile* text eol=lf
docker-compose*.yml text eol=lf
docker-compose*.yaml text eol=lf
.dockerignore text eol=lf

# Kubernetes 配置 - 强制使用 LF
k8s/**/*.yml text eol=lf
k8s/**/*.yaml text eol=lf

# DevContainer 配置 - 强制使用 LF
.devcontainer/**/* text eol=lf

# Shell 脚本 - 强制使用 LF
*.sh text eol=lf
*.bash text eol=lf
*.zsh text eol=lf

# 批处理文件 - 使用 CRLF (Windows)
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=crlf

# Makefile - 强制使用 LF
Makefile text eol=lf
makefile text eol=lf
*.mk text eol=lf

# 文档文件 - 强制使用 LF
*.md text eol=lf
*.txt text eol=lf
*.rst text eol=lf
*.adoc text eol=lf

# 许可证文件 - 强制使用 LF
LICENSE* text eol=lf
COPYING* text eol=lf

# 包管理文件 - 强制使用 LF
package.json text eol=lf
package-lock.json text eol=lf
yarn.lock text eol=lf
pnpm-lock.yaml text eol=lf
go.mod text eol=lf
go.sum text eol=lf
requirements.txt text eol=lf
requirements-*.txt text eol=lf
Pipfile text eol=lf
Pipfile.lock text eol=lf
pyproject.toml text eol=lf
poetry.lock text eol=lf
Cargo.toml text eol=lf
Cargo.lock text eol=lf

# CI/CD 配置 - 强制使用 LF
.github/**/* text eol=lf
.gitlab-ci.yml text eol=lf
.travis.yml text eol=lf
.circleci/**/* text eol=lf
azure-pipelines.yml text eol=lf
Jenkinsfile text eol=lf

# 二进制文件 - 不进行行结束符转换
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg binary
*.webp binary
*.pdf binary
*.zip binary
*.tar binary
*.gz binary
*.7z binary
*.rar binary
*.exe binary
*.dll binary
*.so binary
*.dylib binary
*.woff binary
*.woff2 binary
*.ttf binary
*.otf binary
*.eot binary

# 特殊处理的文件
*.min.js text eol=lf
*.min.css text eol=lf

# 忽略生成的文件
dist/* linguist-generated=true
build/* linguist-generated=true
coverage/* linguist-generated=true
node_modules/* linguist-generated=true
vendor/* linguist-generated=true
*.generated.* linguist-generated=true

# 语言检测
*.go linguist-language=Go
*.py linguist-language=Python
*.js linguist-language=JavaScript
*.ts linguist-language=TypeScript
*.jsx linguist-language=JavaScript
*.tsx linguist-language=TypeScript
*.yml linguist-language=YAML
*.yaml linguist-language=YAML

# 排除某些文件不被语言统计
*.min.js linguist-vendored=true
*.min.css linguist-vendored=true
vendor/** linguist-vendored=true
node_modules/** linguist-vendored=true
public/assets/** linguist-vendored=true
