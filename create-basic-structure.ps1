# 创建基础项目结构

Write-Host "🏗️ 创建基础项目结构..." -ForegroundColor Green

# 创建目录
Write-Host "📁 创建目录..." -ForegroundColor Blue
New-Item -ItemType Directory -Path "services/ai-services/newsletter-ai" -Force | Out-Null
New-Item -ItemType Directory -Path "services/ai-services/scholar-ai" -Force | Out-Null
New-Item -ItemType Directory -Path "services/ai-services/primer-ai" -Force | Out-Null
New-Item -ItemType Directory -Path "services/ai-services/protein-ai" -Force | Out-Null
New-Item -ItemType Directory -Path "services/ai-services/gene-editing-ai" -Force | Out-Null
New-Item -ItemType Directory -Path "services/ai-services/metabolic-ai" -Force | Out-Null

# 创建基础的 main.py 文件
Write-Host "🤖 创建 AI 服务文件..." -ForegroundColor Blue

$services = @(
    @{name="newsletter-ai"; port=9001; display="资讯处理AI"},
    @{name="scholar-ai"; port=9002; display="文献阅读AI"},
    @{name="primer-ai"; port=9003; display="引物设计AI"},
    @{name="protein-ai"; port=9004; display="蛋白质设计AI"},
    @{name="gene-editing-ai"; port=9005; display="基因编辑AI"},
    @{name="metabolic-ai"; port=9006; display="代谢工程AI"}
)

foreach ($service in $services) {
    $content = @"
#!/usr/bin/env python3
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="$($service.display)")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"message": "欢迎使用 $($service.display)", "service": "$($service.name)"}

@app.get("/health")
def health():
    return {"status": "healthy", "service": "$($service.name)"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=$($service.port))
"@
    
    Set-Content -Path "services/ai-services/$($service.name)/main.py" -Value $content -Encoding UTF8
    
    $requirements = @"
fastapi==0.104.1
uvicorn[standard]==0.24.0
"@
    
    Set-Content -Path "services/ai-services/$($service.name)/requirements.txt" -Value $requirements -Encoding UTF8
    
    Write-Host "  ✅ $($service.name) 创建完成" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 基础结构创建完成！" -ForegroundColor Green
Write-Host "🚀 现在可以在 Codespaces 中运行: ./start-dev.sh" -ForegroundColor Cyan
