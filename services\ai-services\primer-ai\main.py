"""
生科云码引物AI综合体
引物查找、设计、验证及计算机模拟评估系统
"""

import asyncio
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from fastapi import FastAPI, HTTPException, Depends, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
import asyncpg
import redis.asyncio as redis
from loguru import logger
import numpy as np
from Bio.Seq import Seq
from Bio.SeqUtils import MeltingTemp as mt
from Bio.SeqUtils import GC

# 配置日志
logger.add("logs/primer_ai.log", rotation="1 day", retention="30 days")

app = FastAPI(
    title="生科云码引物AI综合体",
    description="引物查找、设计、验证及计算机模拟评估系统",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置
REDIS_URL = "redis://redis:6379/2"
DATABASE_URL = "*************************************************/biocloude"

# 全局变量
redis_client: Optional[redis.Redis] = None
db_pool: Optional[asyncpg.Pool] = None

# 数据模型
class PrimerDesignRequest(BaseModel):
    target_sequence: str = Field(..., description="目标序列")
    primer_length_min: int = Field(default=18, ge=15, le=30, description="引物最小长度")
    primer_length_max: int = Field(default=25, ge=15, le=30, description="引物最大长度")
    tm_min: float = Field(default=55.0, ge=45.0, le=70.0, description="最小熔解温度")
    tm_max: float = Field(default=65.0, ge=45.0, le=70.0, description="最大熔解温度")
    gc_min: float = Field(default=40.0, ge=20.0, le=80.0, description="最小GC含量")
    gc_max: float = Field(default=60.0, ge=20.0, le=80.0, description="最大GC含量")
    avoid_secondary_structure: bool = Field(default=True, description="避免二级结构")
    primer_type: str = Field(default="pcr", description="引物类型：pcr, qpcr, sequencing")
    
    @validator('target_sequence')
    def validate_sequence(cls, v):
        if not re.match(r'^[ATCG]+$', v.upper()):
            raise ValueError('序列只能包含A、T、C、G字符')
        return v.upper()

class PrimerValidationRequest(BaseModel):
    forward_primer: str = Field(..., description="正向引物序列")
    reverse_primer: str = Field(..., description="反向引物序列")
    target_sequence: Optional[str] = Field(None, description="目标序列")
    
    @validator('forward_primer', 'reverse_primer')
    def validate_primer(cls, v):
        if not re.match(r'^[ATCG]+$', v.upper()):
            raise ValueError('引物序列只能包含A、T、C、G字符')
        return v.upper()

class PrimerSearchRequest(BaseModel):
    gene_name: str = Field(..., description="基因名称")
    organism: str = Field(default="human", description="物种")
    primer_type: str = Field(default="pcr", description="引物类型")
    database: str = Field(default="primerbank", description="数据库来源")

class PrimerOptimizationRequest(BaseModel):
    primers: List[Dict[str, str]] = Field(..., description="引物列表")
    optimization_target: str = Field(default="specificity", description="优化目标：specificity, efficiency, stability")
    constraints: Dict[str, Any] = Field(default={}, description="约束条件")

class PCRSimulationRequest(BaseModel):
    forward_primer: str = Field(..., description="正向引物")
    reverse_primer: str = Field(..., description="反向引物")
    template_sequence: str = Field(..., description="模板序列")
    annealing_temp: float = Field(default=60.0, description="退火温度")
    cycles: int = Field(default=30, description="PCR循环数")

# 引物设计服务
class PrimerDesignService:
    """引物设计服务"""
    
    def __init__(self):
        self.min_primer_length = 15
        self.max_primer_length = 30
        
    async def design_primers(self, request: PrimerDesignRequest) -> Dict[str, Any]:
        """设计引物"""
        try:
            target_seq = request.target_sequence
            
            # 生成候选引物
            forward_candidates = self._generate_forward_primers(target_seq, request)
            reverse_candidates = self._generate_reverse_primers(target_seq, request)
            
            # 评估引物对
            primer_pairs = self._evaluate_primer_pairs(
                forward_candidates, reverse_candidates, request
            )
            
            # 排序并返回最佳引物对
            best_pairs = sorted(primer_pairs, key=lambda x: x['score'], reverse=True)[:10]
            
            return {
                "primer_pairs": best_pairs,
                "design_parameters": request.dict(),
                "total_candidates": len(primer_pairs),
                "design_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"引物设计失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _generate_forward_primers(self, sequence: str, request: PrimerDesignRequest) -> List[Dict[str, Any]]:
        """生成正向引物候选"""
        candidates = []
        
        for length in range(request.primer_length_min, request.primer_length_max + 1):
            for start in range(len(sequence) - length + 1):
                primer_seq = sequence[start:start + length]
                
                # 计算引物特性
                tm = self._calculate_tm(primer_seq)
                gc_content = self._calculate_gc_content(primer_seq)
                
                # 检查是否满足条件
                if (request.tm_min <= tm <= request.tm_max and 
                    request.gc_min <= gc_content <= request.gc_max):
                    
                    candidates.append({
                        "sequence": primer_seq,
                        "start": start,
                        "length": length,
                        "tm": tm,
                        "gc_content": gc_content,
                        "direction": "forward"
                    })
        
        return candidates
    
    def _generate_reverse_primers(self, sequence: str, request: PrimerDesignRequest) -> List[Dict[str, Any]]:
        """生成反向引物候选"""
        candidates = []
        
        for length in range(request.primer_length_min, request.primer_length_max + 1):
            for start in range(len(sequence) - length + 1):
                # 反向互补序列
                primer_seq = str(Seq(sequence[start:start + length]).reverse_complement())
                
                # 计算引物特性
                tm = self._calculate_tm(primer_seq)
                gc_content = self._calculate_gc_content(primer_seq)
                
                # 检查是否满足条件
                if (request.tm_min <= tm <= request.tm_max and 
                    request.gc_min <= gc_content <= request.gc_max):
                    
                    candidates.append({
                        "sequence": primer_seq,
                        "start": start,
                        "length": length,
                        "tm": tm,
                        "gc_content": gc_content,
                        "direction": "reverse"
                    })
        
        return candidates
    
    def _evaluate_primer_pairs(self, forward_primers: List[Dict], reverse_primers: List[Dict], 
                             request: PrimerDesignRequest) -> List[Dict[str, Any]]:
        """评估引物对"""
        primer_pairs = []
        
        for forward in forward_primers:
            for reverse in reverse_primers:
                # 检查引物对兼容性
                if self._check_primer_compatibility(forward, reverse):
                    score = self._calculate_primer_pair_score(forward, reverse, request)
                    
                    primer_pairs.append({
                        "forward_primer": forward,
                        "reverse_primer": reverse,
                        "score": score,
                        "product_length": abs(reverse["start"] - forward["start"]) + forward["length"],
                        "tm_difference": abs(forward["tm"] - reverse["tm"]),
                        "gc_difference": abs(forward["gc_content"] - reverse["gc_content"])
                    })
        
        return primer_pairs
    
    def _check_primer_compatibility(self, forward: Dict, reverse: Dict) -> bool:
        """检查引物对兼容性"""
        # 检查Tm差异
        tm_diff = abs(forward["tm"] - reverse["tm"])
        if tm_diff > 5.0:
            return False
        
        # 检查GC含量差异
        gc_diff = abs(forward["gc_content"] - reverse["gc_content"])
        if gc_diff > 20.0:
            return False
        
        # 检查引物二聚体
        if self._check_primer_dimer(forward["sequence"], reverse["sequence"]):
            return False
        
        return True
    
    def _calculate_primer_pair_score(self, forward: Dict, reverse: Dict, request: PrimerDesignRequest) -> float:
        """计算引物对评分"""
        score = 100.0
        
        # Tm评分
        tm_target = (request.tm_min + request.tm_max) / 2
        tm_penalty = abs(forward["tm"] - tm_target) + abs(reverse["tm"] - tm_target)
        score -= tm_penalty * 2
        
        # GC含量评分
        gc_target = (request.gc_min + request.gc_max) / 2
        gc_penalty = abs(forward["gc_content"] - gc_target) + abs(reverse["gc_content"] - gc_target)
        score -= gc_penalty * 0.5
        
        # 引物对Tm差异评分
        tm_diff_penalty = abs(forward["tm"] - reverse["tm"]) * 3
        score -= tm_diff_penalty
        
        # 长度评分
        length_target = (request.primer_length_min + request.primer_length_max) / 2
        length_penalty = abs(forward["length"] - length_target) + abs(reverse["length"] - length_target)
        score -= length_penalty * 1
        
        return max(score, 0.0)
    
    def _calculate_tm(self, sequence: str) -> float:
        """计算熔解温度"""
        try:
            return mt.Tm_NN(sequence)
        except:
            # 简单计算方法作为备选
            return (sequence.count('A') + sequence.count('T')) * 2 + (sequence.count('G') + sequence.count('C')) * 4
    
    def _calculate_gc_content(self, sequence: str) -> float:
        """计算GC含量"""
        return GC(sequence)
    
    def _check_primer_dimer(self, primer1: str, primer2: str) -> bool:
        """检查引物二聚体"""
        # 简化的引物二聚体检查
        min_match_length = 4
        
        for i in range(len(primer1) - min_match_length + 1):
            for j in range(len(primer2) - min_match_length + 1):
                if primer1[i:i+min_match_length] == str(Seq(primer2[j:j+min_match_length]).reverse_complement()):
                    return True
        
        return False

class PrimerValidationService:
    """引物验证服务"""
    
    async def validate_primers(self, request: PrimerValidationRequest) -> Dict[str, Any]:
        """验证引物"""
        try:
            forward_analysis = self._analyze_primer(request.forward_primer)
            reverse_analysis = self._analyze_primer(request.reverse_primer)
            
            # 引物对分析
            pair_analysis = self._analyze_primer_pair(
                request.forward_primer, 
                request.reverse_primer,
                request.target_sequence
            )
            
            return {
                "forward_primer": forward_analysis,
                "reverse_primer": reverse_analysis,
                "primer_pair": pair_analysis,
                "validation_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"引物验证失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _analyze_primer(self, primer_seq: str) -> Dict[str, Any]:
        """分析单个引物"""
        return {
            "sequence": primer_seq,
            "length": len(primer_seq),
            "tm": self._calculate_tm(primer_seq),
            "gc_content": self._calculate_gc_content(primer_seq),
            "secondary_structure": self._check_secondary_structure(primer_seq),
            "hairpin_risk": self._check_hairpin(primer_seq),
            "self_dimer_risk": self._check_self_dimer(primer_seq)
        }
    
    def _analyze_primer_pair(self, forward: str, reverse: str, target: Optional[str] = None) -> Dict[str, Any]:
        """分析引物对"""
        analysis = {
            "tm_difference": abs(self._calculate_tm(forward) - self._calculate_tm(reverse)),
            "gc_difference": abs(self._calculate_gc_content(forward) - self._calculate_gc_content(reverse)),
            "primer_dimer_risk": self._check_primer_dimer(forward, reverse),
            "cross_dimer_risk": self._check_cross_dimer(forward, reverse)
        }
        
        if target:
            analysis.update({
                "forward_binding": self._check_binding(forward, target),
                "reverse_binding": self._check_binding(str(Seq(reverse).reverse_complement()), target),
                "product_length": self._calculate_product_length(forward, reverse, target)
            })
        
        return analysis
    
    def _calculate_tm(self, sequence: str) -> float:
        """计算熔解温度"""
        try:
            return mt.Tm_NN(sequence)
        except:
            return (sequence.count('A') + sequence.count('T')) * 2 + (sequence.count('G') + sequence.count('C')) * 4
    
    def _calculate_gc_content(self, sequence: str) -> float:
        """计算GC含量"""
        return GC(sequence)
    
    def _check_secondary_structure(self, sequence: str) -> Dict[str, Any]:
        """检查二级结构"""
        # 简化的二级结构检查
        return {
            "has_structure": False,
            "structure_type": None,
            "stability": "stable"
        }
    
    def _check_hairpin(self, sequence: str) -> Dict[str, Any]:
        """检查发夹结构"""
        # 简化的发夹结构检查
        return {
            "has_hairpin": False,
            "hairpin_tm": None,
            "risk_level": "low"
        }
    
    def _check_self_dimer(self, sequence: str) -> Dict[str, Any]:
        """检查自二聚体"""
        return {
            "has_self_dimer": False,
            "dimer_tm": None,
            "risk_level": "low"
        }
    
    def _check_primer_dimer(self, primer1: str, primer2: str) -> Dict[str, Any]:
        """检查引物二聚体"""
        return {
            "has_dimer": False,
            "dimer_tm": None,
            "risk_level": "low"
        }
    
    def _check_cross_dimer(self, primer1: str, primer2: str) -> Dict[str, Any]:
        """检查交叉二聚体"""
        return {
            "has_cross_dimer": False,
            "dimer_tm": None,
            "risk_level": "low"
        }
    
    def _check_binding(self, primer: str, target: str) -> Dict[str, Any]:
        """检查引物结合"""
        # 简单的结合检查
        if primer in target:
            return {
                "binds": True,
                "position": target.find(primer),
                "mismatches": 0
            }
        else:
            return {
                "binds": False,
                "position": -1,
                "mismatches": None
            }
    
    def _calculate_product_length(self, forward: str, reverse: str, target: str) -> Optional[int]:
        """计算PCR产物长度"""
        forward_pos = target.find(forward)
        reverse_comp = str(Seq(reverse).reverse_complement())
        reverse_pos = target.find(reverse_comp)
        
        if forward_pos != -1 and reverse_pos != -1 and reverse_pos > forward_pos:
            return reverse_pos - forward_pos + len(reverse)
        
        return None

class PCRSimulationService:
    """PCR模拟服务"""
    
    async def simulate_pcr(self, request: PCRSimulationRequest) -> Dict[str, Any]:
        """模拟PCR反应"""
        try:
            # 检查引物结合
            forward_binding = self._check_primer_binding(
                request.forward_primer, 
                request.template_sequence,
                request.annealing_temp
            )
            
            reverse_binding = self._check_primer_binding(
                str(Seq(request.reverse_primer).reverse_complement()),
                request.template_sequence,
                request.annealing_temp
            )
            
            # 计算PCR效率
            efficiency = self._calculate_pcr_efficiency(
                request.forward_primer,
                request.reverse_primer,
                request.annealing_temp
            )
            
            # 模拟PCR循环
            amplification_curve = self._simulate_amplification(
                efficiency,
                request.cycles
            )
            
            return {
                "forward_binding": forward_binding,
                "reverse_binding": reverse_binding,
                "pcr_efficiency": efficiency,
                "amplification_curve": amplification_curve,
                "final_yield": amplification_curve[-1] if amplification_curve else 0,
                "simulation_parameters": request.dict()
            }
            
        except Exception as e:
            logger.error(f"PCR模拟失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _check_primer_binding(self, primer: str, template: str, temp: float) -> Dict[str, Any]:
        """检查引物结合"""
        primer_tm = self._calculate_tm(primer)
        
        return {
            "primer_tm": primer_tm,
            "annealing_temp": temp,
            "binding_efficiency": min(1.0, temp / primer_tm) if primer_tm > 0 else 0,
            "will_bind": temp <= primer_tm + 5  # 允许5度误差
        }
    
    def _calculate_pcr_efficiency(self, forward: str, reverse: str, temp: float) -> float:
        """计算PCR效率"""
        forward_tm = self._calculate_tm(forward)
        reverse_tm = self._calculate_tm(reverse)
        
        # 简化的效率计算
        tm_diff = abs(forward_tm - reverse_tm)
        temp_penalty = abs((forward_tm + reverse_tm) / 2 - temp)
        
        base_efficiency = 0.95  # 基础效率
        efficiency = base_efficiency * (1 - tm_diff * 0.01) * (1 - temp_penalty * 0.005)
        
        return max(0.5, min(1.0, efficiency))
    
    def _simulate_amplification(self, efficiency: float, cycles: int) -> List[float]:
        """模拟扩增曲线"""
        curve = [1.0]  # 初始模板量
        
        for cycle in range(cycles):
            # 每个循环的扩增
            current_amount = curve[-1]
            new_amount = current_amount * (1 + efficiency)
            curve.append(new_amount)
        
        return curve
    
    def _calculate_tm(self, sequence: str) -> float:
        """计算熔解温度"""
        try:
            return mt.Tm_NN(sequence)
        except:
            return (sequence.count('A') + sequence.count('T')) * 2 + (sequence.count('G') + sequence.count('C')) * 4

# 初始化服务
primer_design_service = PrimerDesignService()
primer_validation_service = PrimerValidationService()
pcr_simulation_service = PCRSimulationService()

# 启动事件
@app.on_event("startup")
async def startup_event():
    global redis_client, db_pool
    
    # 初始化Redis
    redis_client = redis.from_url(REDIS_URL)
    
    # 初始化数据库连接池
    db_pool = await asyncpg.create_pool(DATABASE_URL)
    
    logger.info("🚀 Primer AI服务启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    global redis_client, db_pool
    
    if redis_client:
        await redis_client.close()
    
    if db_pool:
        await db_pool.close()
    
    logger.info("👋 Primer AI服务关闭")

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "primer-ai",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0"
    }

@app.post("/api/v1/design")
async def design_primers(request: PrimerDesignRequest):
    """设计引物"""
    result = await primer_design_service.design_primers(request)
    return JSONResponse(content=result)

@app.post("/api/v1/validate")
async def validate_primers(request: PrimerValidationRequest):
    """验证引物"""
    result = await primer_validation_service.validate_primers(request)
    return JSONResponse(content=result)

@app.post("/api/v1/simulate")
async def simulate_pcr(request: PCRSimulationRequest):
    """模拟PCR"""
    result = await pcr_simulation_service.simulate_pcr(request)
    return JSONResponse(content=result)

@app.get("/api/v1/stats")
async def get_stats():
    """获取统计信息"""
    try:
        stats = {
            "total_designs": await redis_client.get("stats:total_designs") or 0,
            "total_validations": await redis_client.get("stats:total_validations") or 0,
            "total_simulations": await redis_client.get("stats:total_simulations") or 0,
            "success_rate": 0.92,  # 示例数据
            "avg_design_time": 1.5  # 示例数据
        }
        
        return JSONResponse(content={"stats": stats})
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9003)
