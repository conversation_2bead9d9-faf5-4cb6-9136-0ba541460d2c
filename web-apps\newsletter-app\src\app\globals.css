@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 199 89% 48%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 199 89% 48%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 199 89% 48%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 199 89% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

::-webkit-scrollbar-thumb {
  @apply bg-bio-400 rounded-full hover:bg-bio-500;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-bio-600;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 选中文本样式 */
::selection {
  @apply bg-bio-200 text-bio-900;
}

::-moz-selection {
  @apply bg-bio-200 text-bio-900;
}

/* 焦点样式 */
.focus-visible {
  @apply outline-none ring-2 ring-bio-500 ring-offset-2;
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 渐变文本 */
.gradient-text {
  @apply bg-gradient-to-r from-bio-600 via-medical-600 to-science-600 bg-clip-text text-transparent;
}

/* 玻璃态效果 */
.glass-effect {
  @apply bg-white/80 backdrop-blur-md border border-white/20 shadow-lg;
}

/* 卡片悬停效果 */
.card-hover {
  @apply transition-all duration-300 hover:shadow-xl hover:scale-105 hover:-translate-y-1;
}

/* 按钮样式 */
.btn-primary {
  @apply bg-gradient-to-r from-bio-500 to-bio-600 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:from-bio-600 hover:to-bio-700 hover:shadow-xl transition-all duration-300 transform hover:scale-105;
}

.btn-secondary {
  @apply bg-white text-bio-600 border-2 border-bio-200 px-6 py-3 rounded-lg font-semibold hover:bg-bio-50 hover:border-bio-300 transition-all duration-300;
}

/* 标签样式 */
.tag {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-bio-100 text-bio-800 hover:bg-bio-200 transition-colors duration-200;
}

.tag-medical {
  @apply bg-medical-100 text-medical-800 hover:bg-medical-200;
}

.tag-science {
  @apply bg-science-100 text-science-800 hover:bg-science-200;
}

/* 加载动画 */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-bio-600;
}

/* 响应式文本 */
.text-responsive {
  @apply text-sm sm:text-base md:text-lg lg:text-xl;
}

/* 新闻卡片样式 */
.news-card {
  @apply bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-bio-200 overflow-hidden;
}

.news-card:hover {
  @apply transform hover:scale-[1.02];
}

/* 分类标签 */
.category-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.category-medical {
  @apply bg-red-100 text-red-800;
}

.category-biotech {
  @apply bg-blue-100 text-blue-800;
}

.category-pharma {
  @apply bg-green-100 text-green-800;
}

.category-research {
  @apply bg-purple-100 text-purple-800;
}

.category-device {
  @apply bg-orange-100 text-orange-800;
}

/* 优先级指示器 */
.priority-high {
  @apply border-l-4 border-red-500;
}

.priority-medium {
  @apply border-l-4 border-yellow-500;
}

.priority-low {
  @apply border-l-4 border-green-500;
}

/* 时间线样式 */
.timeline-item {
  @apply relative pl-8 pb-8;
}

.timeline-item::before {
  @apply absolute left-0 top-2 w-3 h-3 bg-bio-500 rounded-full;
  content: '';
}

.timeline-item::after {
  @apply absolute left-1.5 top-5 w-0.5 h-full bg-bio-200;
  content: '';
}

.timeline-item:last-child::after {
  @apply hidden;
}

/* 统计卡片 */
.stat-card {
  @apply bg-gradient-to-br from-white to-gray-50 p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300;
}

/* 搜索框样式 */
.search-input {
  @apply w-full px-4 py-3 pl-12 pr-4 text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-bio-500 focus:border-transparent placeholder-gray-500;
}

/* 过滤器样式 */
.filter-chip {
  @apply inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-bio-100 hover:text-bio-800 cursor-pointer transition-colors duration-200;
}

.filter-chip.active {
  @apply bg-bio-500 text-white hover:bg-bio-600;
}
