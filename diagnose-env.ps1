# Environment Diagnosis Script

Write-Host "Environment Diagnosis..." -ForegroundColor Green

# Check OS
Write-Host ""
Write-Host "OS Information:" -ForegroundColor Blue
Write-Host "  OS: $($env:OS)"
Write-Host "  Computer: $($env:COMPUTERNAME)"
Write-Host "  User: $($env:USERNAME)"
Write-Host "  PowerShell: $($PSVersionTable.PSVersion)"

# Check paths
Write-Host ""
Write-Host "Path Information:" -ForegroundColor Blue
$currentPath = Get-Location
Write-Host "  Current Dir: $currentPath"
Write-Host "  Services Dir Exists: $(Test-Path 'services')"
Write-Host "  .devcontainer Exists: $(Test-Path '.devcontainer')"

# Check environment variables
Write-Host ""
Write-Host "Environment Variables:" -ForegroundColor Blue
Write-Host "  CODESPACES: $($env:CODESPACES)"
Write-Host "  GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN: $($env:GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN)"

# Check development tools
Write-Host ""
Write-Host "Development Tools:" -ForegroundColor Blue

# Python
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  Python: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    try {
        $python3Version = python3 --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  Python3: $python3Version" -ForegroundColor Green
        } else {
            throw "Python3 not found"
        }
    } catch {
        Write-Host "  Python: NOT INSTALLED" -ForegroundColor Red
    }
}

# Go
try {
    $goVersion = go version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  Go: $goVersion" -ForegroundColor Green
    } else {
        throw "Go not found"
    }
} catch {
    Write-Host "  Go: NOT INSTALLED" -ForegroundColor Red
}

# Node.js
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  Node.js: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Host "  Node.js: NOT INSTALLED" -ForegroundColor Red
}

# Check port usage
Write-Host ""
Write-Host "Port Usage:" -ForegroundColor Blue
$ports = @(3000, 3001, 3002, 8001, 8002, 9001, 9002, 9003, 9004, 9005, 9006)
$occupiedPorts = @()

foreach ($port in $ports) {
    $result = netstat -an | findstr ":$port"
    if ($result) {
        Write-Host "  Port ${port}: OCCUPIED" -ForegroundColor Yellow
        $occupiedPorts += $port
    } else {
        Write-Host "  Port ${port}: AVAILABLE" -ForegroundColor Green
    }
}

# Check processes
Write-Host ""
Write-Host "Related Processes:" -ForegroundColor Blue
$processes = @("python", "go", "node", "npm")
foreach ($proc in $processes) {
    $running = Get-Process -Name $proc -ErrorAction SilentlyContinue
    if ($running) {
        Write-Host "  ${proc}: $($running.Count) processes running" -ForegroundColor Green
    } else {
        Write-Host "  ${proc}: No processes running" -ForegroundColor Red
    }
}

# Environment determination
Write-Host ""
Write-Host "Environment Analysis:" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

if ($env:CODESPACES -eq "true" -or $env:GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN) {
    Write-Host "  Current Environment: GitHub Codespaces" -ForegroundColor Green
    Write-Host "  Recommendation: Run ./start-dev.sh in Codespaces terminal" -ForegroundColor Yellow
} elseif ($currentPath -like "*workspace*" -and $currentPath -like "*biocloude*") {
    Write-Host "  Current Environment: Possibly Codespaces (path pattern)" -ForegroundColor Yellow
    Write-Host "  Recommendation: Confirm if in Codespaces, run in Codespaces terminal" -ForegroundColor Yellow
} else {
    Write-Host "  Current Environment: Windows Local Environment" -ForegroundColor Blue
    Write-Host "  Recommendation: Use GitHub Codespaces or install full dev environment" -ForegroundColor Yellow
}

# Recommendations
Write-Host ""
Write-Host "Recommendations:" -ForegroundColor Magenta
Write-Host "===============" -ForegroundColor Magenta

if ($occupiedPorts.Count -gt 0) {
    Write-Host "  Port Conflicts: $($occupiedPorts.Count) ports occupied" -ForegroundColor Red
    Write-Host "    - Services may already be running" -ForegroundColor Yellow
    Write-Host "    - Suggestion: Stop existing services or use different ports" -ForegroundColor Yellow
}

$missingTools = @()
if (-not (Get-Command python -ErrorAction SilentlyContinue) -and -not (Get-Command python3 -ErrorAction SilentlyContinue)) {
    $missingTools += "Python"
}
if (-not (Get-Command go -ErrorAction SilentlyContinue)) {
    $missingTools += "Go"
}
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    $missingTools += "Node.js"
}

if ($missingTools.Count -gt 0) {
    Write-Host "  Missing Tools: $($missingTools -join ', ')" -ForegroundColor Red
    Write-Host "    - Recommendation: Use GitHub Codespaces (recommended)" -ForegroundColor Green
    Write-Host "    - Alternative: Install missing development tools" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Recommended Solutions:" -ForegroundColor Green
Write-Host "1. Use GitHub Codespaces (Zero Configuration)" -ForegroundColor White
Write-Host "   - Visit GitHub repository" -ForegroundColor Gray
Write-Host "   - Click Code -> Codespaces -> Create codespace" -ForegroundColor Gray
Write-Host "   - Run in Codespaces terminal: ./start-dev.sh" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Local Development Environment Setup" -ForegroundColor White
Write-Host "   - Install Python 3.9+" -ForegroundColor Gray
Write-Host "   - Install Go 1.21+" -ForegroundColor Gray
Write-Host "   - Install Node.js 18+" -ForegroundColor Gray
Write-Host "   - Run: .\start-dev.ps1" -ForegroundColor Gray
