apiVersion: v1
kind: Namespace
metadata:
  name: biocloude
  labels:
    name: biocloude
    environment: production

---
apiVersion: v1
kind: Secret
metadata:
  name: biocloude-secrets
  namespace: biocloude
type: Opaque
data:
  # 数据库连接字符串 (base64编码)
  database-url: ********************************************************************************
  
  # Redis连接字符串 (base64编码)
  redis-url: cmVkaXM6Ly9yZWRpczozNjM3OS8w
  
  # JWT密钥 (base64编码)
  jwt-secret: eW91ci1zdXBlci1zZWNyZXQtand0LWtleS1jaGFuZ2UtaW4tcHJvZHVjdGlvbg==
  
  # OpenAI API密钥 (base64编码)
  openai-api-key: c2stWU9VUl9PUEVOQUlfQVBJX0tFWV9IRVJF
  
  # 腾讯云CIAM配置 (base64编码)
  ciam-client-id: WU9VUl9DSUFNX0NMSUVOVF9JRA==
  ciam-client-secret: WU9VUl9DSUFNX0NMSUVOVF9TRUNSRVQ=
  
  # SMTP配置 (base64编码)
  smtp-host: c210cC5xcS5jb20=
  smtp-port: NTg3
  smtp-user: WU9VUl9TTVRQX1VTRVI=
  smtp-password: WU9VUl9TTVRQX1BBU1NXT1JE

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: biocloude-config
  namespace: biocloude
data:
  # 应用配置
  environment: "production"
  log-level: "INFO"
  
  # 服务端口配置
  auth-service-port: "8001"
  subscription-service-port: "8002"
  newsletter-ai-port: "9001"
  
  # 前端配置
  main-portal-url: "https://www.biocloude.cn"
  newsletter-app-url: "https://ivdnewsletter.biocloude.cn"
  api-base-url: "https://api.biocloude.cn"
  
  # 功能开关
  ai-classification-enabled: "true"
  ai-summary-enabled: "true"
  rate-limit-enabled: "true"
  prometheus-enabled: "true"
