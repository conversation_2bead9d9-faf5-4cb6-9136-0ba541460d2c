# 修复 Codespaces 连接问题的脚本

Write-Host "🔧 修复 Codespaces 连接问题..." -ForegroundColor Green

# 检查当前环境
Write-Host "📋 检查当前环境..." -ForegroundColor Blue

$currentPath = Get-Location
Write-Host "当前路径: $currentPath" -ForegroundColor Yellow

# 检查是否在 Codespaces 中
if ($env:CODESPACES -eq "true") {
    Write-Host "✅ 检测到 Codespaces 环境" -ForegroundColor Green
    
    # 在 Codespaces 中，直接使用 bash 脚本
    Write-Host "🚀 在 Codespaces 中启动服务..." -ForegroundColor Blue
    
    # 修复脚本权限
    bash -c "chmod +x *.sh"
    
    # 运行启动脚本
    bash -c "./start-dev.sh"
    
} elseif ($env:GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN) {
    Write-Host "✅ 检测到 Codespaces 环境（通过端口转发域名）" -ForegroundColor Green
    
    # 修复脚本权限
    bash -c "chmod +x *.sh"
    
    # 运行启动脚本
    bash -c "./start-dev.sh"
    
} else {
    Write-Host "⚠️ 未检测到 Codespaces 环境" -ForegroundColor Yellow
    Write-Host "当前似乎在 Windows 本地环境中" -ForegroundColor Yellow
    
    # 检查是否有 WSL
    try {
        $wslVersion = wsl --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 检测到 WSL，尝试在 WSL 中运行..." -ForegroundColor Green
            
            # 获取 WSL 路径
            $wslPath = wsl pwd
            Write-Host "WSL 路径: $wslPath" -ForegroundColor Yellow
            
            # 在 WSL 中运行
            wsl bash -c "cd /workspace && chmod +x *.sh && ./start-dev.sh"
        } else {
            throw "WSL not available"
        }
    }
    catch {
        Write-Host "❌ WSL 不可用" -ForegroundColor Red
        Write-Host ""
        Write-Host "🎯 解决方案:" -ForegroundColor Cyan
        Write-Host "1. 如果您想在 Codespaces 中开发:" -ForegroundColor Yellow
        Write-Host "   - 访问 GitHub 仓库"
        Write-Host "   - 点击 Code → Codespaces → Create codespace"
        Write-Host "   - 在 Codespaces 终端中运行: ./start-dev.sh"
        Write-Host ""
        Write-Host "2. 如果您想在 Windows 本地开发:" -ForegroundColor Yellow
        Write-Host "   - 安装 Docker Desktop"
        Write-Host "   - 运行: .\start-docker-services.ps1"
        Write-Host "   - 然后运行: .\start-dev.ps1"
        Write-Host ""
        Write-Host "3. 如果您想使用 WSL:" -ForegroundColor Yellow
        Write-Host "   - 安装 WSL2: wsl --install"
        Write-Host "   - 重启计算机"
        Write-Host "   - 在 WSL 中运行项目"
    }
}

Write-Host ""
Write-Host "💡 提示:" -ForegroundColor Cyan
Write-Host "- 如果您在 Codespaces 中，请确保在 Codespaces 终端中运行脚本"
Write-Host "- 如果您在本地环境，请使用 PowerShell 脚本 (.\start-dev.ps1)"
Write-Host "- 推荐使用 GitHub Codespaces 以获得最佳体验"

Read-Host "按回车键关闭"
