# 🪟 Windows 环境开发指南

## 🎯 问题解决

如果您在 Windows 环境下遇到 `./start-dev.sh: No such file or directory` 错误，这是因为 Windows 默认不支持 bash 脚本。我们为您提供了多种解决方案。

## 🚀 解决方案

### 方案1: 使用 PowerShell 脚本 (推荐)

我们提供了完整的 PowerShell 版本脚本：

```powershell
# 启动所有服务
.\start-dev.ps1

# 检查服务状态
.\status-dev.ps1

# 停止所有服务
.\stop-dev.ps1
```

### 方案2: 使用批处理文件

```cmd
# 启动所有服务
start-dev.bat

# 其他操作仍使用 PowerShell
.\status-dev.ps1
.\stop-dev.ps1
```

### 方案3: 使用 Git Bash

如果您安装了 Git for Windows，可以使用 Git Bash：

```bash
# 在 Git Bash 中运行
bash start-dev.sh
bash status-dev.sh
bash stop-dev.sh
bash test-dev.sh
```

### 方案4: 使用 WSL (Windows Subsystem for Linux)

如果您启用了 WSL：

```bash
# 在 WSL 中运行
wsl bash start-dev.sh
wsl bash status-dev.sh
wsl bash stop-dev.sh
wsl bash test-dev.sh
```

## 📋 脚本对比

| 功能 | Linux/macOS | Windows PowerShell | Windows 批处理 |
|------|-------------|-------------------|----------------|
| 启动服务 | `./start-dev.sh` | `.\start-dev.ps1` | `start-dev.bat` |
| 停止服务 | `./stop-dev.sh` | `.\stop-dev.ps1` | ❌ |
| 检查状态 | `./status-dev.sh` | `.\status-dev.ps1` | ❌ |
| 运行测试 | `./test-dev.sh` | `.\test-dev.ps1` | ❌ |

## 🔧 PowerShell 执行策略

如果遇到 PowerShell 执行策略错误，请运行：

```powershell
# 临时允许脚本执行
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或者直接运行脚本
PowerShell -ExecutionPolicy Bypass -File .\start-dev.ps1
```

## 🎯 推荐工作流程

### 🌅 开始开发 (Windows)

```powershell
# 1. 启动所有服务
.\start-dev.ps1

# 2. 检查服务状态
.\status-dev.ps1

# 3. 开始编码...
```

### 🌙 结束开发 (Windows)

```powershell
# 停止所有服务
.\stop-dev.ps1
```

## 🌐 GitHub Codespaces (推荐)

为了获得最佳的开发体验，我们强烈推荐使用 GitHub Codespaces：

1. **无需本地配置** - 一键启动完整环境
2. **跨平台一致** - Linux 环境，支持所有脚本
3. **云端资源** - 强大的计算能力
4. **团队协作** - 统一的开发环境

### 启动 Codespaces

1. 访问 GitHub 仓库
2. 点击 **Code** → **Codespaces** → **Create codespace**
3. 等待环境初始化完成
4. 运行 `./start-dev.sh` 启动服务

## 🐛 常见问题

### Q: PowerShell 脚本无法执行
**A:** 运行以下命令设置执行策略：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Q: 批处理文件中文显示乱码
**A:** 在命令提示符中运行：
```cmd
chcp 65001
```

### Q: 端口被占用
**A:** 使用 PowerShell 检查并停止：
```powershell
# 查看端口占用
Get-NetTCPConnection -LocalPort 3000

# 停止所有服务
.\stop-dev.ps1
```

### Q: Node.js 或 Go 命令不存在
**A:** 确保已安装并添加到 PATH：
- Node.js: https://nodejs.org/
- Go: https://golang.org/dl/

### Q: 想要 Linux 环境体验
**A:** 使用以下选项之一：
- GitHub Codespaces (推荐)
- WSL2 + Docker Desktop
- VirtualBox + Ubuntu

## 💡 最佳实践

### ✅ 推荐做法
- 🌐 **优先使用 Codespaces** - 最佳开发体验
- 💻 **本地开发使用 PowerShell** - 功能最完整
- 🔄 **定期检查服务状态** - 及时发现问题
- 🛑 **开发结束后停止服务** - 释放系统资源

### ❌ 避免做法
- 🚫 **不要混用不同的脚本类型** - 保持一致性
- 🚫 **不要忽略执行策略错误** - 正确配置 PowerShell
- 🚫 **不要手动杀死进程** - 使用停止脚本

## 📞 获取帮助

如果遇到 Windows 环境相关问题：

1. 📖 **查看此文档**
2. 🔍 **运行状态检查**: `.\status-dev.ps1`
3. 🌐 **尝试 Codespaces** - 避免本地环境问题
4. 🐛 **创建 GitHub Issue** - 描述具体错误信息
5. 💬 **在团队群组询问**

## 🎉 总结

Windows 用户现在有多种方式来运行生科云码平台：

1. **🥇 最佳选择**: GitHub Codespaces
2. **🥈 本地开发**: PowerShell 脚本
3. **🥉 备选方案**: Git Bash 或 WSL

选择最适合您的方式，开始您的开发之旅吧！🚀
