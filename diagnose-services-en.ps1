# Diagnose service status

Write-Host "Diagnosing service status..." -ForegroundColor Green

# Check port usage
Write-Host ""
Write-Host "Checking port usage:" -ForegroundColor Blue

$ports = @(8001, 8002, 9001, 9002, 9003, 9004, 9005, 9006, 3000, 3001, 3002)
$activeServices = @()

foreach ($port in $ports) {
    $result = netstat -an | findstr ":$port"
    if ($result) {
        Write-Host "  Port $port is listening" -ForegroundColor Green
        $activeServices += $port
    } else {
        Write-Host "  Port $port is not listening" -ForegroundColor Red
    }
}

# Test service responses
Write-Host ""
Write-Host "Testing service responses:" -ForegroundColor Blue

foreach ($port in $activeServices) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$port" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
        Write-Host "  Port $port responds OK (Status: $($response.StatusCode))" -ForegroundColor Green
    }
    catch {
        Write-Host "  Port $port no response: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test health check endpoints
Write-Host ""
Write-Host "Testing health check endpoints:" -ForegroundColor Blue

$healthPorts = @(8001, 8002, 9001, 9002, 9003, 9004, 9005, 9006)
foreach ($port in $healthPorts) {
    if ($port -in $activeServices) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$port/health" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
            $content = $response.Content | ConvertFrom-Json
            Write-Host "  Port $port health check OK: $($content.service)" -ForegroundColor Green
        }
        catch {
            Write-Host "  Port $port health check failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Check processes
Write-Host ""
Write-Host "Checking related processes:" -ForegroundColor Blue

$processes = @("python", "go", "node", "npm")
foreach ($proc in $processes) {
    $running = Get-Process -Name $proc -ErrorAction SilentlyContinue
    if ($running) {
        Write-Host "  $proc processes running ($($running.Count) instances)" -ForegroundColor Green
        foreach ($p in $running) {
            $memory = [math]::Round($p.WorkingSet/1MB, 2)
            Write-Host "    - PID: $($p.Id), Memory: ${memory}MB" -ForegroundColor Gray
        }
    } else {
        Write-Host "  $proc processes not running" -ForegroundColor Red
    }
}

# Summary
Write-Host ""
Write-Host "Diagnosis Summary:" -ForegroundColor Yellow
Write-Host "  Active ports: $($activeServices.Count)/$($ports.Count)" -ForegroundColor White

if ($activeServices.Count -eq 0) {
    Write-Host "  All services are down. Run: ./start-dev.sh" -ForegroundColor Cyan
} elseif ($activeServices.Count -lt $ports.Count) {
    Write-Host "  Some services are down. Restart: ./stop-dev.sh && ./start-dev.sh" -ForegroundColor Cyan
} else {
    Write-Host "  All ports are listening but may have response issues" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Green
Write-Host "  1. If services not responding: ./stop-dev.sh && ./start-dev.sh" -ForegroundColor White
Write-Host "  2. Check detailed status: ./status-dev.sh" -ForegroundColor White
Write-Host "  3. Access services: http://localhost:3000" -ForegroundColor White
