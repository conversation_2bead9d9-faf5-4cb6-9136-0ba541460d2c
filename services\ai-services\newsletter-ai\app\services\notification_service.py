"""
推送通知服务
功能：
1. 邮件推送
2. 短信推送
3. 个性化推送
4. 批量通知
5. 推送模板管理
"""

import asyncio
import json
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Dict, Optional, Any
import aiohttp
from jinja2 import Template
from loguru import logger

from app.core.config import settings
from app.core.redis_client import redis_client

class NotificationService:
    """通知服务类"""
    
    def __init__(self):
        self.email_templates = self._load_email_templates()
        self.sms_templates = self._load_sms_templates()
        self.notification_queue = "notification_queue"
        self.batch_size = 100
        self.retry_attempts = 3
        
    def _load_email_templates(self) -> Dict[str, str]:
        """加载邮件模板"""
        return {
            "newsletter_digest": """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>生科云码 - 每日资讯摘要</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #0ea5e9, #ec4899); color: white; padding: 20px; text-align: center; }
        .article { border-bottom: 1px solid #eee; padding: 15px 0; }
        .article:last-child { border-bottom: none; }
        .article-title { font-size: 16px; font-weight: bold; margin-bottom: 8px; }
        .article-summary { color: #666; margin-bottom: 8px; }
        .article-meta { font-size: 12px; color: #999; }
        .category { background: #f0f9ff; color: #0369a1; padding: 2px 8px; border-radius: 12px; font-size: 11px; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .unsubscribe { color: #999; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 生科云码</h1>
            <p>{{ date }} 生物医学资讯摘要</p>
        </div>
        
        <div style="padding: 20px;">
            <p>亲爱的 {{ user_name }}，</p>
            <p>以下是为您精选的 {{ article_count }} 篇生物医学资讯：</p>
            
            {% for article in articles %}
            <div class="article">
                <div class="article-title">
                    <a href="{{ article.url }}" style="color: #0ea5e9; text-decoration: none;">
                        {{ article.title }}
                    </a>
                </div>
                <div class="article-summary">{{ article.summary }}</div>
                <div class="article-meta">
                    <span class="category">{{ article.category }}</span>
                    {{ article.source }} • {{ article.publish_time }} • {{ article.read_time }}
                </div>
            </div>
            {% endfor %}
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ platform_url }}" style="background: #0ea5e9; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
                    查看更多资讯
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2025 生科云码. 保留所有权利.</p>
            <p>
                <a href="{{ unsubscribe_url }}" class="unsubscribe">取消订阅</a> |
                <a href="{{ preferences_url }}" class="unsubscribe">偏好设置</a>
            </p>
        </div>
    </div>
</body>
</html>
            """,
            
            "welcome": """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>欢迎加入生科云码</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #0ea5e9, #ec4899); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px 20px; }
        .button { background: #0ea5e9; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 欢迎加入生科云码！</h1>
        </div>
        
        <div class="content">
            <p>亲爱的 {{ user_name }}，</p>
            <p>感谢您注册生科云码资讯处理AI平台！我们很高兴为您提供专业的生物医学资讯服务。</p>
            
            <h3>🚀 您可以立即开始：</h3>
            <ul>
                <li>📰 浏览最新的生物医学资讯</li>
                <li>🎯 设置个性化关键词订阅</li>
                <li>🤖 体验AI智能分类和推荐</li>
                <li>📊 查看数据分析和趋势</li>
            </ul>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ platform_url }}" class="button">开始使用</a>
            </div>
            
            <p>如有任何问题，请随时联系我们的客服团队。</p>
            <p>祝您使用愉快！</p>
            <p>生科云码团队</p>
        </div>
        
        <div class="footer">
            <p>© 2025 生科云码. 保留所有权利.</p>
        </div>
    </div>
</body>
</html>
            """,
            
            "subscription_reminder": """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>订阅即将到期提醒</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f59e0b; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .button { background: #0ea5e9; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ 订阅即将到期</h1>
        </div>
        
        <div class="content">
            <p>亲爱的 {{ user_name }}，</p>
            <p>您的 <strong>{{ product_name }}</strong> 订阅将在 {{ days_left }} 天后到期（{{ expiry_date }}）。</p>
            
            <p>为了确保您能继续享受我们的服务，请及时续费：</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ renewal_url }}" class="button">立即续费</a>
            </div>
            
            <p>如有任何问题，请联系我们的客服团队。</p>
            <p>感谢您的支持！</p>
        </div>
        
        <div class="footer">
            <p>© 2025 生科云码. 保留所有权利.</p>
        </div>
    </div>
</body>
</html>
            """
        }
    
    def _load_sms_templates(self) -> Dict[str, str]:
        """加载短信模板"""
        return {
            "verification_code": "【生科云码】您的验证码是：{code}，5分钟内有效，请勿泄露。",
            "login_notification": "【生科云码】您的账户在{time}登录，如非本人操作请及时修改密码。",
            "subscription_expiry": "【生科云码】您的{product}订阅将在{days}天后到期，请及时续费。",
            "important_news": "【生科云码】重要资讯：{title}。查看详情：{url}"
        }
    
    async def start_notifications(self):
        """启动通知服务"""
        logger.info("📧 启动推送通知服务...")
        
        try:
            while True:
                # 处理通知队列
                await self._process_notification_queue()
                
                # 处理定时推送
                await self._process_scheduled_notifications()
                
                # 等待一段时间
                await asyncio.sleep(10)
                
        except Exception as e:
            logger.error(f"通知服务异常: {e}")
    
    async def _process_notification_queue(self):
        """处理通知队列"""
        try:
            # 批量获取通知任务
            notifications = []
            for _ in range(self.batch_size):
                notification_data = await redis_client.rpop(self.notification_queue)
                if not notification_data:
                    break
                notifications.append(json.loads(notification_data))
            
            if not notifications:
                return
            
            logger.info(f"📬 处理 {len(notifications)} 个通知任务")
            
            # 并发处理通知
            tasks = []
            for notification in notifications:
                task = asyncio.create_task(self._send_notification(notification))
                tasks.append(task)
            
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"处理通知队列失败: {e}")
    
    async def _process_scheduled_notifications(self):
        """处理定时推送"""
        try:
            # 获取需要发送的定时通知
            now = datetime.utcnow()
            scheduled_key = f"scheduled_notifications:{now.strftime('%Y%m%d%H%M')}"
            
            scheduled_notifications = await redis_client.lrange(scheduled_key, 0, -1)
            
            for notification_data in scheduled_notifications:
                try:
                    notification = json.loads(notification_data)
                    await self._send_notification(notification)
                except Exception as e:
                    logger.error(f"发送定时通知失败: {e}")
            
            # 清理已处理的定时通知
            if scheduled_notifications:
                await redis_client.delete(scheduled_key)
                
        except Exception as e:
            logger.error(f"处理定时通知失败: {e}")
    
    async def _send_notification(self, notification: Dict[str, Any]):
        """发送通知"""
        notification_type = notification.get("type")
        
        try:
            if notification_type == "email":
                await self._send_email(notification)
            elif notification_type == "sms":
                await self._send_sms(notification)
            elif notification_type == "push":
                await self._send_push_notification(notification)
            else:
                logger.warning(f"未知的通知类型: {notification_type}")
                
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            # 重试机制
            await self._retry_notification(notification)
    
    async def _send_email(self, notification: Dict[str, Any]):
        """发送邮件"""
        try:
            to_email = notification["to"]
            subject = notification["subject"]
            template_name = notification.get("template", "default")
            template_data = notification.get("data", {})
            
            # 渲染邮件内容
            if template_name in self.email_templates:
                template = Template(self.email_templates[template_name])
                html_content = template.render(**template_data)
            else:
                html_content = notification.get("content", "")
            
            # 创建邮件
            msg = MIMEMultipart('alternative')
            msg['From'] = settings.SMTP_FROM if hasattr(settings, 'SMTP_FROM') else "<EMAIL>"
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # 添加HTML内容
            html_part = MIMEText(html_content, 'html', 'utf-8')
            msg.attach(html_part)
            
            # 发送邮件
            if hasattr(settings, 'SMTP_HOST') and settings.SMTP_HOST:
                with smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT) as server:
                    if hasattr(settings, 'SMTP_TLS') and settings.SMTP_TLS:
                        server.starttls()
                    if hasattr(settings, 'SMTP_USER') and settings.SMTP_USER:
                        server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
                    server.send_message(msg)
                
                logger.info(f"✅ 邮件发送成功: {to_email}")
            else:
                logger.warning("SMTP配置未设置，邮件发送跳过")
                
        except Exception as e:
            logger.error(f"发送邮件失败: {e}")
            raise
    
    async def _send_sms(self, notification: Dict[str, Any]):
        """发送短信"""
        try:
            phone = notification["to"]
            template_name = notification.get("template", "default")
            template_data = notification.get("data", {})
            
            # 渲染短信内容
            if template_name in self.sms_templates:
                content = self.sms_templates[template_name].format(**template_data)
            else:
                content = notification.get("content", "")
            
            # 这里应该集成腾讯云SMS或其他短信服务
            # 暂时记录日志
            logger.info(f"📱 短信发送: {phone} - {content}")
            
            # 模拟发送成功
            await asyncio.sleep(0.1)
            
        except Exception as e:
            logger.error(f"发送短信失败: {e}")
            raise
    
    async def _send_push_notification(self, notification: Dict[str, Any]):
        """发送推送通知"""
        try:
            user_id = notification["to"]
            title = notification["title"]
            content = notification["content"]
            
            # 这里应该集成推送服务（如极光推送、个推等）
            # 暂时记录日志
            logger.info(f"🔔 推送通知: {user_id} - {title}")
            
            # 模拟发送成功
            await asyncio.sleep(0.1)
            
        except Exception as e:
            logger.error(f"发送推送通知失败: {e}")
            raise
    
    async def _retry_notification(self, notification: Dict[str, Any]):
        """重试通知"""
        retry_count = notification.get("retry_count", 0)
        
        if retry_count < self.retry_attempts:
            notification["retry_count"] = retry_count + 1
            
            # 延迟重试
            delay = 2 ** retry_count * 60  # 指数退避
            retry_time = datetime.utcnow() + timedelta(seconds=delay)
            
            # 添加到延迟队列
            await redis_client.zadd(
                "notification_retry_queue",
                {json.dumps(notification, ensure_ascii=False): retry_time.timestamp()}
            )
            
            logger.info(f"🔄 通知重试: {retry_count + 1}/{self.retry_attempts}")
        else:
            logger.error(f"❌ 通知发送失败，已达到最大重试次数: {notification}")
    
    async def send_newsletter_digest(self, user_email: str, user_name: str, articles: List[Dict[str, Any]]):
        """发送资讯摘要"""
        notification = {
            "type": "email",
            "to": user_email,
            "subject": f"生科云码 - {datetime.now().strftime('%Y年%m月%d日')} 资讯摘要",
            "template": "newsletter_digest",
            "data": {
                "user_name": user_name,
                "date": datetime.now().strftime('%Y年%m月%d日'),
                "article_count": len(articles),
                "articles": articles,
                "platform_url": "https://ivdnewsletter.biocloude.cn",
                "unsubscribe_url": "https://ivdnewsletter.biocloude.cn/unsubscribe",
                "preferences_url": "https://ivdnewsletter.biocloude.cn/preferences"
            }
        }
        
        await redis_client.lpush(self.notification_queue, json.dumps(notification, ensure_ascii=False))
    
    async def send_welcome_email(self, user_email: str, user_name: str):
        """发送欢迎邮件"""
        notification = {
            "type": "email",
            "to": user_email,
            "subject": "欢迎加入生科云码！",
            "template": "welcome",
            "data": {
                "user_name": user_name,
                "platform_url": "https://ivdnewsletter.biocloude.cn"
            }
        }
        
        await redis_client.lpush(self.notification_queue, json.dumps(notification, ensure_ascii=False))
    
    async def send_subscription_reminder(self, user_email: str, user_name: str, product_name: str, days_left: int, expiry_date: str):
        """发送订阅到期提醒"""
        notification = {
            "type": "email",
            "to": user_email,
            "subject": f"订阅即将到期提醒 - {product_name}",
            "template": "subscription_reminder",
            "data": {
                "user_name": user_name,
                "product_name": product_name,
                "days_left": days_left,
                "expiry_date": expiry_date,
                "renewal_url": "https://www.biocloude.cn/subscription/renew"
            }
        }
        
        await redis_client.lpush(self.notification_queue, json.dumps(notification, ensure_ascii=False))
    
    async def send_verification_code(self, phone: str, code: str):
        """发送验证码"""
        notification = {
            "type": "sms",
            "to": phone,
            "template": "verification_code",
            "data": {
                "code": code
            }
        }
        
        await redis_client.lpush(self.notification_queue, json.dumps(notification, ensure_ascii=False))
    
    async def send_important_news_alert(self, user_contacts: List[str], title: str, url: str):
        """发送重要资讯提醒"""
        for contact in user_contacts:
            if "@" in contact:  # 邮件
                notification = {
                    "type": "email",
                    "to": contact,
                    "subject": f"重要资讯提醒 - {title}",
                    "content": f"<h3>{title}</h3><p><a href='{url}'>查看详情</a></p>"
                }
            else:  # 短信
                notification = {
                    "type": "sms",
                    "to": contact,
                    "template": "important_news",
                    "data": {
                        "title": title[:50] + "..." if len(title) > 50 else title,
                        "url": url
                    }
                }
            
            await redis_client.lpush(self.notification_queue, json.dumps(notification, ensure_ascii=False))
    
    async def schedule_notification(self, notification: Dict[str, Any], send_time: datetime):
        """安排定时通知"""
        scheduled_key = f"scheduled_notifications:{send_time.strftime('%Y%m%d%H%M')}"
        await redis_client.lpush(scheduled_key, json.dumps(notification, ensure_ascii=False))
        await redis_client.expire(scheduled_key, 86400)  # 24小时过期
    
    async def get_notification_stats(self) -> Dict[str, Any]:
        """获取通知统计"""
        try:
            # 获取队列长度
            queue_length = await redis_client.llen(self.notification_queue)
            retry_queue_length = await redis_client.zcard("notification_retry_queue")
            
            # 获取今日发送统计
            today = datetime.now().strftime("%Y%m%d")
            email_sent = await redis_client.get(f"notification_stats:email:{today}") or 0
            sms_sent = await redis_client.get(f"notification_stats:sms:{today}") or 0
            push_sent = await redis_client.get(f"notification_stats:push:{today}") or 0
            
            return {
                "queue_length": queue_length,
                "retry_queue_length": retry_queue_length,
                "today_stats": {
                    "email_sent": int(email_sent),
                    "sms_sent": int(sms_sent),
                    "push_sent": int(push_sent)
                }
            }
            
        except Exception as e:
            logger.error(f"获取通知统计失败: {e}")
            return {}
