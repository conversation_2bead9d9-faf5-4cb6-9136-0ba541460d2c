"""
生科云码 - 资讯处理AI服务
主要功能：
1. 生物科学数据源爬虫
2. AI智能分类和过滤
3. 个性化推荐
4. 实时数据处理
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import asyncio
from contextlib import asynccontextmanager
from loguru import logger
import sys
from datetime import datetime
import os
from typing import List, Optional

# 导入自定义模块
from app.core.config import settings
from app.core.database import init_db
from app.core.redis_client import redis_client
from app.api.v1 import api_router
from app.services.crawler_service import CrawlerService
from app.services.ai_service import AIService
from app.services.notification_service import NotificationService
from app.models.article import Article
from app.schemas.article import ArticleResponse, ArticleListResponse
from app.schemas.common import HealthResponse

# 配置日志
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)
logger.add(
    "logs/newsletter_ai.log",
    rotation="1 day",
    retention="30 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG"
)

# 全局服务实例
crawler_service = None
ai_service = None
notification_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global crawler_service, ai_service, notification_service
    
    logger.info("🚀 启动生科云码资讯处理AI服务...")
    
    try:
        # 初始化数据库
        await init_db()
        logger.info("✅ 数据库初始化完成")
        
        # 初始化Redis
        await redis_client.ping()
        logger.info("✅ Redis连接成功")
        
        # 初始化服务
        crawler_service = CrawlerService()
        ai_service = AIService()
        notification_service = NotificationService()
        
        logger.info("✅ 服务初始化完成")
        
        # 启动后台任务
        asyncio.create_task(start_background_tasks())
        logger.info("✅ 后台任务启动完成")
        
        logger.info("🎉 生科云码资讯处理AI服务启动成功！")
        
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        raise
    
    yield
    
    # 清理资源
    logger.info("🔄 正在关闭服务...")
    if redis_client:
        await redis_client.close()
    logger.info("✅ 服务关闭完成")

# 创建FastAPI应用
app = FastAPI(
    title="生科云码资讯处理AI",
    description="专业的生物医学资讯智能处理平台API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(api_router, prefix="/api/v1")

@app.get("/", response_model=HealthResponse)
async def root():
    """根路径健康检查"""
    return HealthResponse(
        status="healthy",
        message="生科云码资讯处理AI服务运行正常",
        timestamp=datetime.utcnow(),
        version="1.0.0"
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """详细健康检查"""
    try:
        # 检查Redis连接
        await redis_client.ping()
        redis_status = "healthy"
    except Exception:
        redis_status = "unhealthy"
    
    # 检查数据库连接
    try:
        # 这里应该检查数据库连接
        db_status = "healthy"
    except Exception:
        db_status = "unhealthy"
    
    overall_status = "healthy" if redis_status == "healthy" and db_status == "healthy" else "unhealthy"
    
    return HealthResponse(
        status=overall_status,
        message="健康检查完成",
        timestamp=datetime.utcnow(),
        version="1.0.0",
        details={
            "redis": redis_status,
            "database": db_status,
            "services": {
                "crawler": "running" if crawler_service else "stopped",
                "ai": "running" if ai_service else "stopped",
                "notification": "running" if notification_service else "stopped"
            }
        }
    )

@app.get("/api/v1/articles", response_model=ArticleListResponse)
async def get_articles(
    page: int = 1,
    limit: int = 20,
    category: Optional[str] = None,
    source: Optional[str] = None,
    search: Optional[str] = None
):
    """获取文章列表"""
    try:
        # 这里应该从数据库获取文章
        # 暂时返回模拟数据
        articles = [
            {
                "id": "1",
                "title": "新型CRISPR基因编辑技术在治疗遗传性疾病方面取得重大突破",
                "summary": "研究人员开发出更精确的基因编辑工具，能够有效治疗镰状细胞病和β地中海贫血症",
                "content": "",
                "category": "基因治疗",
                "source": "Nature Medicine",
                "author": "Dr. Smith",
                "publish_time": "2025-01-11T10:00:00Z",
                "priority": "high",
                "tags": ["CRISPR", "基因编辑", "遗传病", "临床试验"],
                "read_time": "5分钟",
                "image": "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=200&fit=crop",
                "url": "#",
                "views": 1250,
                "likes": 89
            }
        ]
        
        return ArticleListResponse(
            success=True,
            data=articles,
            pagination={
                "page": page,
                "limit": limit,
                "total": len(articles),
                "total_pages": 1
            }
        )
    except Exception as e:
        logger.error(f"获取文章列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取文章列表失败")

async def start_background_tasks():
    """启动后台任务"""
    logger.info("🔄 启动后台任务...")
    
    # 启动爬虫任务
    if crawler_service:
        asyncio.create_task(crawler_service.start_crawling())
    
    # 启动AI处理任务
    if ai_service:
        asyncio.create_task(ai_service.start_processing())
    
    # 启动通知任务
    if notification_service:
        asyncio.create_task(notification_service.start_notifications())

@app.post("/api/v1/crawl/trigger")
async def trigger_crawl(background_tasks: BackgroundTasks):
    """手动触发爬虫"""
    if not crawler_service:
        raise HTTPException(status_code=503, detail="爬虫服务未启动")
    
    background_tasks.add_task(crawler_service.crawl_all_sources)
    return {"message": "爬虫任务已启动"}

@app.get("/api/v1/stats")
async def get_stats():
    """获取统计信息"""
    try:
        # 从Redis获取统计信息
        stats = await redis_client.hgetall("newsletter:stats")
        
        return {
            "total_articles": int(stats.get("total_articles", 0)),
            "today_articles": int(stats.get("today_articles", 0)),
            "active_sources": int(stats.get("active_sources", 0)),
            "last_update": stats.get("last_update", ""),
            "processing_queue": int(stats.get("processing_queue", 0))
        }
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return {
            "total_articles": 0,
            "today_articles": 0,
            "active_sources": 0,
            "last_update": "",
            "processing_queue": 0
        }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=9001,
        reload=True if os.getenv("ENVIRONMENT") == "development" else False,
        log_level="info"
    )
