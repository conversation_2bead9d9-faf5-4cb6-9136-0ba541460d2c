#!/bin/bash

# 生科云码平台集成测试脚本
# 用于自动化测试整个平台的功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="https://api.biocloude.cn"
MAIN_PORTAL_URL="https://www.biocloude.cn"
NEWSLETTER_APP_URL="https://ivdnewsletter.biocloude.cn"

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_info "运行测试: $test_name"
    
    if eval "$test_command"; then
        log_success "✅ $test_name - 通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        log_error "❌ $test_name - 失败"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 健康检查测试
test_health_checks() {
    log_info "🏥 开始健康检查测试..."
    
    # 测试认证服务健康检查
    run_test "认证服务健康检查" "curl -f -s $BASE_URL/auth/health > /dev/null"
    
    # 测试订阅服务健康检查
    run_test "订阅服务健康检查" "curl -f -s $BASE_URL/subscription/health > /dev/null"
    
    # 测试AI服务健康检查
    run_test "AI服务健康检查" "curl -f -s $BASE_URL/ai/health > /dev/null"
    
    # 测试主站门户
    run_test "主站门户访问" "curl -f -s $MAIN_PORTAL_URL > /dev/null"
    
    # 测试资讯处理AI应用
    run_test "资讯处理AI应用访问" "curl -f -s $NEWSLETTER_APP_URL > /dev/null"
}

# 认证功能测试
test_authentication() {
    log_info "🔐 开始认证功能测试..."
    
    # 测试用户注册
    local register_response=$(curl -s -X POST "$BASE_URL/auth/register" \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "password": "testpassword123",
            "username": "testuser"
        }')
    
    run_test "用户注册" "echo '$register_response' | grep -q 'success'"
    
    # 测试用户登录
    local login_response=$(curl -s -X POST "$BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
            "email": "<EMAIL>",
            "password": "testpassword123"
        }')
    
    run_test "用户登录" "echo '$login_response' | grep -q 'access_token'"
    
    # 提取访问令牌
    ACCESS_TOKEN=$(echo "$login_response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    
    # 测试令牌验证
    run_test "令牌验证" "curl -f -s -H 'Authorization: Bearer $ACCESS_TOKEN' $BASE_URL/auth/me > /dev/null"
}

# 订阅功能测试
test_subscription() {
    log_info "💳 开始订阅功能测试..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_warning "跳过订阅测试：未获取到访问令牌"
        return
    fi
    
    # 测试获取产品列表
    run_test "获取产品列表" "curl -f -s $BASE_URL/subscription/products | grep -q 'products'"
    
    # 测试获取用户订阅
    run_test "获取用户订阅" "curl -f -s -H 'Authorization: Bearer $ACCESS_TOKEN' $BASE_URL/subscription/subscriptions > /dev/null"
    
    # 测试创建订阅
    local subscription_response=$(curl -s -X POST "$BASE_URL/subscription/subscriptions" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -d '{
            "product_id": "newsletter-ai",
            "pricing_plan_id": "basic-monthly"
        }')
    
    run_test "创建订阅" "echo '$subscription_response' | grep -q 'subscription'"
}

# AI功能测试
test_ai_features() {
    log_info "🤖 开始AI功能测试..."
    
    # 测试获取文章列表
    run_test "获取文章列表" "curl -f -s '$BASE_URL/ai/articles?limit=10' | grep -q 'articles'"
    
    # 测试文章分类
    local classification_response=$(curl -s -X POST "$BASE_URL/ai/classify" \
        -H "Content-Type: application/json" \
        -d '{
            "title": "CRISPR基因编辑技术新突破",
            "content": "研究人员开发出更精确的基因编辑工具"
        }')
    
    run_test "文章分类" "echo '$classification_response' | grep -q 'category'"
    
    # 测试关键词提取
    local keywords_response=$(curl -s -X POST "$BASE_URL/ai/extract-keywords" \
        -H "Content-Type: application/json" \
        -d '{
            "text": "生物医学研究中的人工智能应用"
        }')
    
    run_test "关键词提取" "echo '$keywords_response' | grep -q 'keywords'"
}

# 数据库连接测试
test_database_connectivity() {
    log_info "🗄️ 开始数据库连接测试..."
    
    # 通过Kubernetes检查数据库Pod状态
    if command -v kubectl &> /dev/null; then
        run_test "PostgreSQL Pod状态" "kubectl get pod -l app=postgres -n biocloude | grep -q Running"
        run_test "Redis Pod状态" "kubectl get pod -l app=redis -n biocloude | grep -q Running"
    else
        log_warning "kubectl未安装，跳过数据库Pod状态检查"
    fi
}

# 性能测试
test_performance() {
    log_info "⚡ 开始性能测试..."
    
    # 测试API响应时间
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "$BASE_URL/auth/health")
    local response_time_ms=$(echo "$response_time * 1000" | bc)
    
    run_test "API响应时间 (<1000ms)" "[ $(echo '$response_time_ms < 1000' | bc) -eq 1 ]"
    
    # 测试并发请求
    log_info "测试并发请求处理能力..."
    for i in {1..10}; do
        curl -s "$BASE_URL/auth/health" > /dev/null &
    done
    wait
    
    run_test "并发请求处理" "curl -f -s $BASE_URL/auth/health > /dev/null"
}

# 安全测试
test_security() {
    log_info "🔒 开始安全测试..."
    
    # 测试未授权访问
    local unauthorized_response=$(curl -s -w '%{http_code}' "$BASE_URL/auth/me")
    run_test "未授权访问保护" "echo '$unauthorized_response' | grep -q '401'"
    
    # 测试HTTPS重定向
    if [[ $BASE_URL == https* ]]; then
        local http_url=${BASE_URL/https/http}
        local redirect_response=$(curl -s -w '%{http_code}' "$http_url/auth/health")
        run_test "HTTPS重定向" "echo '$redirect_response' | grep -qE '30[1-8]'"
    fi
    
    # 测试CORS头
    local cors_response=$(curl -s -H "Origin: https://example.com" -I "$BASE_URL/auth/health")
    run_test "CORS配置" "echo '$cors_response' | grep -q 'Access-Control-Allow-Origin'"
}

# 监控测试
test_monitoring() {
    log_info "📊 开始监控测试..."
    
    if command -v kubectl &> /dev/null; then
        # 检查Prometheus是否运行
        run_test "Prometheus运行状态" "kubectl get pod -l app.kubernetes.io/name=prometheus -n biocloude | grep -q Running || true"
        
        # 检查Grafana是否运行
        run_test "Grafana运行状态" "kubectl get pod -l app.kubernetes.io/name=grafana -n biocloude | grep -q Running || true"
    else
        log_warning "kubectl未安装，跳过监控测试"
    fi
}

# 清理测试数据
cleanup_test_data() {
    log_info "🧹 清理测试数据..."
    
    if [ -n "$ACCESS_TOKEN" ]; then
        # 删除测试用户（如果API支持）
        curl -s -X DELETE "$BASE_URL/auth/users/<EMAIL>" \
            -H "Authorization: Bearer $ACCESS_TOKEN" > /dev/null || true
    fi
    
    log_success "测试数据清理完成"
}

# 生成测试报告
generate_test_report() {
    log_info "📋 生成测试报告..."
    
    local report_file="test-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
生科云码平台集成测试报告
========================

测试时间: $(date)
测试环境: $BASE_URL

测试结果统计:
- 总测试数: $TOTAL_TESTS
- 通过测试: $PASSED_TESTS
- 失败测试: $FAILED_TESTS
- 成功率: $(echo "scale=2; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)%

测试覆盖范围:
✅ 健康检查测试
✅ 认证功能测试
✅ 订阅功能测试
✅ AI功能测试
✅ 数据库连接测试
✅ 性能测试
✅ 安全测试
✅ 监控测试

EOF

    log_success "测试报告已生成: $report_file"
}

# 显示测试结果
show_test_results() {
    echo ""
    echo "=================================="
    echo "🧪 测试完成！"
    echo "=================================="
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        log_success "🎉 所有测试通过！"
        exit 0
    else
        log_error "❌ 有 $FAILED_TESTS 个测试失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "🧪 开始生科云码平台集成测试..."
    
    # 解析命令行参数
    SKIP_CLEANUP=false
    GENERATE_REPORT=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            --generate-report)
                GENERATE_REPORT=true
                shift
                ;;
            --base-url)
                BASE_URL="$2"
                shift 2
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-cleanup    跳过测试数据清理"
                echo "  --generate-report 生成测试报告"
                echo "  --base-url URL    指定API基础URL"
                echo "  -h, --help        显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行测试
    test_health_checks
    test_authentication
    test_subscription
    test_ai_features
    test_database_connectivity
    test_performance
    test_security
    test_monitoring
    
    # 清理和报告
    if [ "$SKIP_CLEANUP" = false ]; then
        cleanup_test_data
    fi
    
    if [ "$GENERATE_REPORT" = true ]; then
        generate_test_report
    fi
    
    show_test_results
}

# 错误处理
trap 'log_error "测试过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
