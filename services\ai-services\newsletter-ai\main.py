﻿#!/usr/bin/env python3
import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Newsletter AI")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"message": "Welcome to Newsletter AI", "service": "newsletter-ai"}

@app.get("/health")
def health():
    return {"status": "healthy", "service": "newsletter-ai"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=9001)
