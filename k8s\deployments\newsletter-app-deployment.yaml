apiVersion: apps/v1
kind: Deployment
metadata:
  name: newsletter-app
  namespace: biocloude
  labels:
    app: newsletter-app
    component: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: newsletter-app
  template:
    metadata:
      labels:
        app: newsletter-app
        component: frontend
    spec:
      containers:
      - name: newsletter-app
        image: biocloude/newsletter-app:latest
        ports:
        - containerPort: 3001
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: NEXT_PUBLIC_API_URL
          value: "https://api.biocloude.cn"
        - name: NEXT_PUBLIC_APP_NAME
          value: "IVD Newsletter AI"
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: newsletter-app-service
  namespace: biocloude
  labels:
    app: newsletter-app
spec:
  selector:
    app: newsletter-app
  ports:
  - name: http
    port: 3001
    targetPort: 3001
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: newsletter-app-ingress
  namespace: biocloude
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - ivdnewsletter.biocloude.cn
    secretName: newsletter-app-tls
  rules:
  - host: ivdnewsletter.biocloude.cn
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: newsletter-app-service
            port:
              number: 3001
