"""
Redis客户端配置
"""

import redis.asyncio as redis
from typing import Optional, Any, Dict, List
import json
from loguru import logger

from app.core.config import settings

class RedisClient:
    """Redis异步客户端封装"""
    
    def __init__(self):
        self.redis: Optional[redis.Redis] = None
        self.connected = False
    
    async def connect(self):
        """连接Redis"""
        try:
            self.redis = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30
            )
            
            # 测试连接
            await self.redis.ping()
            self.connected = True
            logger.info("✅ Redis连接成功")
            
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            self.connected = False
            raise
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis:
            await self.redis.close()
            self.connected = False
            logger.info("Redis连接已关闭")
    
    async def ping(self):
        """检查Redis连接"""
        if not self.redis:
            await self.connect()
        return await self.redis.ping()
    
    async def get(self, key: str) -> Optional[str]:
        """获取值"""
        if not self.redis:
            await self.connect()
        return await self.redis.get(key)
    
    async def set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """设置值"""
        if not self.redis:
            await self.connect()
        return await self.redis.set(key, value, ex=ex)
    
    async def setex(self, key: str, time: int, value: Any) -> bool:
        """设置值并指定过期时间"""
        if not self.redis:
            await self.connect()
        return await self.redis.setex(key, time, value)
    
    async def delete(self, *keys: str) -> int:
        """删除键"""
        if not self.redis:
            await self.connect()
        return await self.redis.delete(*keys)
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if not self.redis:
            await self.connect()
        return bool(await self.redis.exists(key))
    
    async def expire(self, key: str, time: int) -> bool:
        """设置过期时间"""
        if not self.redis:
            await self.connect()
        return await self.redis.expire(key, time)
    
    async def ttl(self, key: str) -> int:
        """获取剩余生存时间"""
        if not self.redis:
            await self.connect()
        return await self.redis.ttl(key)
    
    # Hash操作
    async def hget(self, name: str, key: str) -> Optional[str]:
        """获取哈希字段值"""
        if not self.redis:
            await self.connect()
        return await self.redis.hget(name, key)
    
    async def hset(self, name: str, key: str = None, value: Any = None, mapping: Dict = None) -> int:
        """设置哈希字段值"""
        if not self.redis:
            await self.connect()
        if mapping:
            return await self.redis.hset(name, mapping=mapping)
        else:
            return await self.redis.hset(name, key, value)
    
    async def hgetall(self, name: str) -> Dict[str, str]:
        """获取所有哈希字段"""
        if not self.redis:
            await self.connect()
        return await self.redis.hgetall(name)
    
    async def hdel(self, name: str, *keys: str) -> int:
        """删除哈希字段"""
        if not self.redis:
            await self.connect()
        return await self.redis.hdel(name, *keys)
    
    async def hincrby(self, name: str, key: str, amount: int = 1) -> int:
        """哈希字段值增加"""
        if not self.redis:
            await self.connect()
        return await self.redis.hincrby(name, key, amount)
    
    # List操作
    async def lpush(self, name: str, *values: Any) -> int:
        """从左侧推入列表"""
        if not self.redis:
            await self.connect()
        return await self.redis.lpush(name, *values)
    
    async def rpush(self, name: str, *values: Any) -> int:
        """从右侧推入列表"""
        if not self.redis:
            await self.connect()
        return await self.redis.rpush(name, *values)
    
    async def lpop(self, name: str) -> Optional[str]:
        """从左侧弹出列表元素"""
        if not self.redis:
            await self.connect()
        return await self.redis.lpop(name)
    
    async def rpop(self, name: str) -> Optional[str]:
        """从右侧弹出列表元素"""
        if not self.redis:
            await self.connect()
        return await self.redis.rpop(name)
    
    async def brpop(self, keys: str, timeout: int = 0):
        """阻塞式从右侧弹出列表元素"""
        if not self.redis:
            await self.connect()
        return await self.redis.brpop(keys, timeout=timeout)
    
    async def lrange(self, name: str, start: int, end: int) -> List[str]:
        """获取列表范围内的元素"""
        if not self.redis:
            await self.connect()
        return await self.redis.lrange(name, start, end)
    
    async def llen(self, name: str) -> int:
        """获取列表长度"""
        if not self.redis:
            await self.connect()
        return await self.redis.llen(name)
    
    async def ltrim(self, name: str, start: int, end: int) -> bool:
        """修剪列表"""
        if not self.redis:
            await self.connect()
        return await self.redis.ltrim(name, start, end)
    
    # Set操作
    async def sadd(self, name: str, *values: Any) -> int:
        """添加集合成员"""
        if not self.redis:
            await self.connect()
        return await self.redis.sadd(name, *values)
    
    async def srem(self, name: str, *values: Any) -> int:
        """删除集合成员"""
        if not self.redis:
            await self.connect()
        return await self.redis.srem(name, *values)
    
    async def smembers(self, name: str) -> set:
        """获取集合所有成员"""
        if not self.redis:
            await self.connect()
        return await self.redis.smembers(name)
    
    async def sismember(self, name: str, value: Any) -> bool:
        """检查是否为集合成员"""
        if not self.redis:
            await self.connect()
        return await self.redis.sismember(name, value)
    
    async def scard(self, name: str) -> int:
        """获取集合成员数量"""
        if not self.redis:
            await self.connect()
        return await self.redis.scard(name)
    
    # Sorted Set操作
    async def zadd(self, name: str, mapping: Dict[str, float]) -> int:
        """添加有序集合成员"""
        if not self.redis:
            await self.connect()
        return await self.redis.zadd(name, mapping)
    
    async def zrange(self, name: str, start: int, end: int, withscores: bool = False) -> List:
        """获取有序集合范围内的成员"""
        if not self.redis:
            await self.connect()
        return await self.redis.zrange(name, start, end, withscores=withscores)
    
    async def zrevrange(self, name: str, start: int, end: int, withscores: bool = False) -> List:
        """获取有序集合范围内的成员（逆序）"""
        if not self.redis:
            await self.connect()
        return await self.redis.zrevrange(name, start, end, withscores=withscores)
    
    async def zrem(self, name: str, *values: Any) -> int:
        """删除有序集合成员"""
        if not self.redis:
            await self.connect()
        return await self.redis.zrem(name, *values)
    
    async def zscore(self, name: str, value: Any) -> Optional[float]:
        """获取有序集合成员分数"""
        if not self.redis:
            await self.connect()
        return await self.redis.zscore(name, value)
    
    async def zcard(self, name: str) -> int:
        """获取有序集合成员数量"""
        if not self.redis:
            await self.connect()
        return await self.redis.zcard(name)
    
    # 自增操作
    async def incr(self, name: str, amount: int = 1) -> int:
        """递增"""
        if not self.redis:
            await self.connect()
        return await self.redis.incr(name, amount)
    
    async def decr(self, name: str, amount: int = 1) -> int:
        """递减"""
        if not self.redis:
            await self.connect()
        return await self.redis.decr(name, amount)
    
    # 批量操作
    async def mget(self, keys: List[str]) -> List[Optional[str]]:
        """批量获取"""
        if not self.redis:
            await self.connect()
        return await self.redis.mget(keys)
    
    async def mset(self, mapping: Dict[str, Any]) -> bool:
        """批量设置"""
        if not self.redis:
            await self.connect()
        return await self.redis.mset(mapping)
    
    # 模式匹配
    async def keys(self, pattern: str) -> List[str]:
        """获取匹配模式的键"""
        if not self.redis:
            await self.connect()
        return await self.redis.keys(pattern)
    
    async def scan(self, cursor: int = 0, match: str = None, count: int = None):
        """扫描键"""
        if not self.redis:
            await self.connect()
        return await self.redis.scan(cursor=cursor, match=match, count=count)
    
    # 发布订阅
    async def publish(self, channel: str, message: Any) -> int:
        """发布消息"""
        if not self.redis:
            await self.connect()
        return await self.redis.publish(channel, message)
    
    # 事务
    async def pipeline(self):
        """创建管道"""
        if not self.redis:
            await self.connect()
        return self.redis.pipeline()
    
    # 关闭连接
    async def close(self):
        """关闭连接"""
        if self.redis:
            await self.redis.close()
            self.connected = False

# 创建全局Redis客户端实例
redis_client = RedisClient()
