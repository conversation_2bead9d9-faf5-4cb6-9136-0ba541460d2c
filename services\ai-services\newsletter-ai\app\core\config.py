"""
配置管理
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator

class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基本配置
    APP_NAME: str = "生科云码资讯处理AI"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "production"
    
    # API配置
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # CORS配置
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    DATABASE_URL: str = "postgresql+asyncpg://biocloude:biocloude123@postgres:5432/biocloude"
    
    # Redis配置
    REDIS_URL: str = "redis://redis:6379/0"
    REDIS_HOST: str = "redis"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # 爬虫配置
    CRAWLER_USER_AGENT: str = "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
    CRAWLER_DELAY: int = 1  # 请求间隔（秒）
    CRAWLER_TIMEOUT: int = 30  # 请求超时（秒）
    CRAWLER_MAX_CONCURRENT: int = 10  # 最大并发数
    CRAWLER_INTERVAL: int = 300  # 爬取间隔（秒）
    
    # AI配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    AI_CLASSIFICATION_ENABLED: bool = True
    AI_SUMMARY_ENABLED: bool = True
    
    # 通知配置
    SMTP_HOST: str = "smtp.qq.com"
    SMTP_PORT: int = 587
    SMTP_USER: str = ""
    SMTP_PASSWORD: str = ""
    SMTP_TLS: bool = True
    
    # 短信配置（腾讯云SMS）
    TENCENT_SMS_SECRET_ID: str = ""
    TENCENT_SMS_SECRET_KEY: str = ""
    TENCENT_SMS_APP_ID: str = ""
    TENCENT_SMS_SIGN: str = "生科云码"
    
    # 文件存储配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/newsletter_ai.log"
    LOG_ROTATION: str = "1 day"
    LOG_RETENTION: str = "30 days"
    
    # 监控配置
    PROMETHEUS_ENABLED: bool = True
    PROMETHEUS_PORT: int = 8000
    
    # 缓存配置
    CACHE_TTL: int = 3600  # 1小时
    CACHE_MAX_SIZE: int = 1000
    
    # 限流配置
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # 60秒
    
    # 数据源配置
    DATA_SOURCES_CONFIG_FILE: str = "config/data_sources.json"
    
    # 分类配置
    CATEGORIES: List[str] = [
        "基因治疗", "医疗器械", "疫苗研发", "再生医学", 
        "抗感染", "免疫治疗", "药物研发", "临床试验",
        "生物技术", "精准医学", "其他"
    ]
    
    # 优先级配置
    PRIORITIES: List[str] = ["high", "medium", "low"]
    
    # 标签配置
    COMMON_TAGS: List[str] = [
        "CRISPR", "AI诊断", "癌症治疗", "基因编辑", "免疫疗法",
        "干细胞", "疫苗开发", "精准医学", "生物标志物", "临床试验",
        "FDA批准", "新药研发", "医疗器械", "生物技术", "细胞治疗"
    ]
    
    @validator("ALLOWED_HOSTS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        # 如果没有提供完整的DATABASE_URL，则从环境变量构建
        return f"postgresql+asyncpg://{os.getenv('DB_USER', 'biocloude')}:{os.getenv('DB_PASSWORD', 'biocloude123')}@{os.getenv('DB_HOST', 'postgres')}:{os.getenv('DB_PORT', '5432')}/{os.getenv('DB_NAME', 'biocloude')}"
    
    @validator("REDIS_URL", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        # 如果没有提供完整的REDIS_URL，则从环境变量构建
        password_part = f":{os.getenv('REDIS_PASSWORD')}@" if os.getenv('REDIS_PASSWORD') else ""
        return f"redis://{password_part}{os.getenv('REDIS_HOST', 'redis')}:{os.getenv('REDIS_PORT', '6379')}/{os.getenv('REDIS_DB', '0')}"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局配置实例
settings = Settings()

# 数据源配置
DATA_SOURCES = [
    {
        "id": "pubmed_rss",
        "name": "PubMed",
        "type": "rss",
        "url": "https://pubmed.ncbi.nlm.nih.gov/rss/search/1QHBVXiNDExOjEqeA1SqSQ1VUysOTVAuS83P1y9IzS3WT8yrBAA--/?limit=100",
        "category": "医学研究",
        "priority": 1,
        "active": True,
        "update_frequency": 60,  # 分钟
        "keywords": ["medicine", "medical", "clinical", "patient", "disease", "treatment"]
    },
    {
        "id": "nature_news",
        "name": "Nature News",
        "type": "rss",
        "url": "https://www.nature.com/nature.rss",
        "category": "科学期刊",
        "priority": 1,
        "active": True,
        "update_frequency": 120,
        "keywords": ["science", "research", "biology", "chemistry", "physics"]
    },
    {
        "id": "science_news",
        "name": "Science Magazine",
        "type": "rss",
        "url": "https://www.science.org/rss/news_current.xml",
        "category": "科学期刊",
        "priority": 1,
        "active": True,
        "update_frequency": 120,
        "keywords": ["science", "research", "discovery", "innovation"]
    },
    {
        "id": "cell_news",
        "name": "Cell",
        "type": "rss",
        "url": "https://www.cell.com/cell/current.rss",
        "category": "生物学期刊",
        "priority": 1,
        "active": True,
        "update_frequency": 180,
        "keywords": ["cell", "biology", "molecular", "genetics", "biochemistry"]
    },
    {
        "id": "nejm_news",
        "name": "New England Journal of Medicine",
        "type": "rss",
        "url": "https://www.nejm.org/action/showFeed?type=etoc&feed=rss",
        "category": "医学期刊",
        "priority": 1,
        "active": True,
        "update_frequency": 240,
        "keywords": ["medicine", "clinical", "patient", "therapy", "diagnosis"]
    },
    {
        "id": "fda_news",
        "name": "FDA News",
        "type": "rss",
        "url": "https://www.fda.gov/about-fda/contact-fda/stay-informed/rss-feeds/press-announcements/rss.xml",
        "category": "监管机构",
        "priority": 2,
        "active": True,
        "update_frequency": 360,
        "keywords": ["fda", "approval", "drug", "device", "safety", "regulation"]
    },
    {
        "id": "biorxiv",
        "name": "bioRxiv",
        "type": "rss",
        "url": "https://connect.biorxiv.org/biorxiv_xml.php?subject=all",
        "category": "预印本",
        "priority": 2,
        "active": True,
        "update_frequency": 480,
        "keywords": ["preprint", "biology", "research", "manuscript"]
    }
]

# AI分类关键词配置
CLASSIFICATION_KEYWORDS = {
    "基因治疗": [
        "gene therapy", "genetic", "genome", "crispr", "gene editing",
        "dna", "rna", "基因", "遗传", "基因编辑", "基因治疗"
    ],
    "医疗器械": [
        "medical device", "diagnostic", "device", "equipment", "instrument",
        "fda approval", "医疗器械", "诊断设备", "医疗设备"
    ],
    "疫苗研发": [
        "vaccine", "vaccination", "immunization", "immunize",
        "疫苗", "免疫", "接种", "疫苗开发"
    ],
    "再生医学": [
        "stem cell", "regenerative medicine", "tissue engineering",
        "cell therapy", "干细胞", "再生医学", "组织工程"
    ],
    "抗感染": [
        "antibiotic", "antimicrobial", "infection", "bacteria", "virus",
        "抗生素", "抗菌", "感染", "细菌", "病毒"
    ],
    "免疫治疗": [
        "immunotherapy", "immune", "antibody", "immunology",
        "免疫治疗", "免疫", "抗体", "免疫学"
    ],
    "药物研发": [
        "drug development", "pharmaceutical", "clinical trial", "medication",
        "药物开发", "制药", "临床试验", "药物"
    ],
    "生物技术": [
        "biotechnology", "biotech", "bioinformatics", "molecular biology",
        "生物技术", "生物信息学", "分子生物学"
    ]
}

# 优先级关键词配置
PRIORITY_KEYWORDS = {
    "high": [
        "breakthrough", "first", "novel", "revolutionary", "major",
        "fda approval", "phase iii", "breakthrough therapy", "urgent",
        "突破", "首个", "新型", "革命性", "重大", "FDA批准", "紧急"
    ],
    "medium": [
        "study", "research", "development", "treatment", "therapy",
        "clinical", "trial", "研究", "开发", "治疗", "临床", "试验"
    ]
}
