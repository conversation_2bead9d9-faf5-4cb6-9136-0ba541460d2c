# 生科云码平台项目结构

## 项目目录结构

```
biocloude/
├── README.md
├── docker-compose.yml
├── k8s/                          # Kubernetes配置文件
│   ├── namespace.yaml
│   ├── configmaps/
│   ├── secrets/
│   └── deployments/
├── scripts/                      # 部署和工具脚本
│   ├── setup.sh
│   ├── deploy.sh
│   └── migrate.sh
├── shared/                       # 共享组件和配置
│   ├── proto/                    # gRPC协议定义
│   ├── configs/                  # 共享配置
│   └── utils/                    # 共享工具库
├── services/                     # 微服务目录
│   ├── gateway/                  # API网关 (Nginx/Kong)
│   ├── auth-service/             # 用户认证服务 (Go)
│   ├── subscription-service/     # 订阅管理服务 (Go)
│   ├── payment-service/          # 支付网关服务 (Go)
│   ├── notification-service/     # 通知服务 (Go)
│   └── ai-services/              # AI模型服务 (Python)
│       ├── newsletter-ai/
│       ├── scholar-ai/
│       ├── primer-ai/
│       ├── protein-ai/
│       ├── gene-edit-ai/
│       └── metabolic-ai/
├── web-apps/                     # 前端应用
│   ├── main-portal/              # 主站 (Next.js)
│   ├── newsletter-app/           # 资讯处理应用
│   ├── scholar-app/              # 文献阅读应用
│   ├── primer-app/               # 引物AI应用
│   ├── protein-app/              # 蛋白质设计应用
│   ├── gene-edit-app/            # 基因编辑应用
│   └── metabolic-app/            # 代谢工程应用
├── database/                     # 数据库相关
│   ├── migrations/
│   ├── seeds/
│   └── schemas/
└── docs/                         # 项目文档
    ├── api/
    ├── deployment/
    └── architecture/
```

## 技术栈详细说明

### 前端技术栈
- **框架**: Next.js 14 (App Router)
- **UI库**: Tailwind CSS + Shadcn/ui
- **状态管理**: Zustand
- **数据获取**: TanStack Query (React Query)
- **表单处理**: React Hook Form + Zod
- **图表可视化**: Recharts + D3.js
- **类型检查**: TypeScript

### 后端技术栈
- **核心服务**: Go 1.21 + Gin
- **AI服务**: Python 3.11 + FastAPI
- **服务通信**: gRPC + Protocol Buffers
- **API网关**: Kong Gateway
- **数据库**: PostgreSQL 15 (腾讯云TDSQL-C)
- **缓存**: Redis 7
- **消息队列**: Redis Streams
- **文件存储**: 腾讯云COS

### 部署与运维
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes (腾讯云TKE)
- **CI/CD**: GitHub Actions
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **服务网格**: Istio (可选)

## 域名配置方案

### 主域名
- `www.biocloude.cn` - 主站门户

### 子产品域名
- `ivdnewsletter.biocloude.cn` 或 `ivdnewsletter.bio` - 资讯处理
- `scholar.bio` - 文献阅读
- `aiprimer.bio` - 引物AI
- `aiprotein.bio` - 蛋白质设计
- `aigenedit.bio` - 基因编辑
- `aimetab.bio` - 代谢工程

### API域名
- `api.biocloude.cn` - 统一API入口
- `auth.biocloude.cn` - 认证服务
- `pay.biocloude.cn` - 支付服务

## 数据库设计原则

### 多租户架构
- 使用 `tenant_id` 字段实现数据隔离
- 每个子产品作为独立租户
- 用户数据在主站统一管理

### 分库分表策略
- 用户相关表：按用户ID哈希分片
- 业务数据表：按产品ID分片
- 日志表：按时间分片

### 数据同步策略
- 主站用户数据为权威数据源
- 子产品通过API同步用户信息
- 使用事件驱动架构保证数据一致性

## 安全设计

### 认证授权
- JWT Token + Refresh Token机制
- 基于RBAC的权限控制
- API Rate Limiting
- CORS配置

### 数据安全
- 数据库连接加密
- 敏感数据字段加密存储
- API请求签名验证
- 定期安全审计

## 性能优化

### 缓存策略
- Redis缓存热点数据
- CDN加速静态资源
- 数据库查询优化
- 接口响应缓存

### 负载均衡
- Nginx负载均衡
- 数据库读写分离
- 微服务水平扩展
- 异步任务处理

## 监控告警

### 应用监控
- 服务健康检查
- 性能指标监控
- 错误日志收集
- 用户行为分析

### 业务监控
- 订阅转化率
- 支付成功率
- API调用统计
- 用户活跃度

## 开发规范

### 代码规范
- Go: 遵循Go官方代码规范
- Python: 遵循PEP8规范
- TypeScript: 使用ESLint + Prettier
- Git: 使用Conventional Commits

### API设计规范
- RESTful API设计
- 统一错误码定义
- API版本管理
- 完整的API文档

### 测试规范
- 单元测试覆盖率 > 80%
- 集成测试自动化
- 性能测试定期执行
- 安全测试持续进行
