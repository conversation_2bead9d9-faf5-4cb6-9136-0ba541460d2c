package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"github.com/biocloude/platform/services/auth-service/internal/config"
	"github.com/biocloude/platform/services/auth-service/internal/database"
	"github.com/biocloude/platform/services/auth-service/internal/handlers"
	"github.com/biocloude/platform/services/auth-service/internal/middleware"
	"github.com/biocloude/platform/services/auth-service/internal/repository"
	"github.com/biocloude/platform/services/auth-service/internal/service"
	pb "github.com/biocloude/platform/shared/proto/auth"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		logrus.Warn("未找到.env文件，使用系统环境变量")
	}

	// 初始化配置
	cfg := config.Load()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.LogLevel)
	if err != nil {
		level = logrus.InfoLevel
	}
	logrus.SetLevel(level)
	logrus.SetFormatter(&logrus.JSONFormatter{})

	logrus.Info("🚀 启动生科云码用户认证服务...")

	// 初始化数据库
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		logrus.Fatalf("❌ 数据库初始化失败: %v", err)
	}
	logrus.Info("✅ 数据库连接成功")

	// 初始化Redis
	redisClient, err := database.InitializeRedis(cfg.RedisURL)
	if err != nil {
		logrus.Fatalf("❌ Redis初始化失败: %v", err)
	}
	logrus.Info("✅ Redis连接成功")

	// 初始化仓库层
	userRepo := repository.NewUserRepository(db)
	sessionRepo := repository.NewSessionRepository(redisClient)

	// 初始化服务层
	authService := service.NewAuthService(userRepo, sessionRepo, cfg)
	userService := service.NewUserService(userRepo, cfg)

	// 初始化处理器
	authHandler := handlers.NewAuthHandler(authService, userService)

	// 启动gRPC服务器
	go startGRPCServer(authService, cfg.GRPCPort)

	// 启动HTTP服务器
	startHTTPServer(authHandler, cfg.HTTPPort)
}

func startGRPCServer(authService *service.AuthService, port int) {
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		logrus.Fatalf("❌ gRPC服务器监听失败: %v", err)
	}

	s := grpc.NewServer()
	pb.RegisterAuthServiceServer(s, authService)

	logrus.Infof("🔗 gRPC服务器启动在端口 %d", port)
	if err := s.Serve(lis); err != nil {
		logrus.Fatalf("❌ gRPC服务器启动失败: %v", err)
	}
}

func startHTTPServer(authHandler *handlers.AuthHandler, port int) {
	// 设置Gin模式
	if os.Getenv("ENVIRONMENT") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// 中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())
	router.Use(middleware.RequestID())

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "auth-service",
			"timestamp": time.Now().UTC(),
			"version":   "1.0.0",
		})
	})

	// API路由
	v1 := router.Group("/api/v1")
	{
		// 认证相关
		auth := v1.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.POST("/logout", middleware.AuthRequired(), authHandler.Logout)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.GET("/me", middleware.AuthRequired(), authHandler.GetCurrentUser)
		}

		// 用户管理
		users := v1.Group("/users")
		users.Use(middleware.AuthRequired())
		{
			users.GET("/:id", authHandler.GetUser)
			users.PUT("/:id", authHandler.UpdateUser)
			users.DELETE("/:id", authHandler.DeleteUser)
			users.GET("", authHandler.ListUsers)
		}

		// 会话管理
		sessions := v1.Group("/sessions")
		sessions.Use(middleware.AuthRequired())
		{
			sessions.GET("", authHandler.ListSessions)
			sessions.DELETE("/:id", authHandler.DeleteSession)
		}

		// 权限验证
		permissions := v1.Group("/permissions")
		{
			permissions.POST("/validate", authHandler.ValidatePermission)
		}
	}

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: router,
	}

	// 启动服务器
	go func() {
		logrus.Infof("🌐 HTTP服务器启动在端口 %d", port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("❌ HTTP服务器启动失败: %v", err)
		}
	}()

	// 优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logrus.Info("🔄 正在关闭服务器...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logrus.Fatalf("❌ 服务器强制关闭: %v", err)
	}

	logrus.Info("✅ 服务器已关闭")
}
