version: '3.8'

services:
  # 数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: biocloude-postgres
    environment:
      POSTGRES_DB: biocloude
      POSTGRES_USER: biocloude
      POSTGRES_PASSWORD: biocloude123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/migrations:/docker-entrypoint-initdb.d
    networks:
      - biocloude-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: biocloude-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - biocloude-network

  # API网关
  gateway:
    build:
      context: ./services/gateway
      dockerfile: Dockerfile
    container_name: biocloude-gateway
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - auth-service
      - subscription-service
      - payment-service
    networks:
      - biocloude-network
    volumes:
      - ./services/gateway/nginx.conf:/etc/nginx/nginx.conf

  # 用户认证服务
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    container_name: biocloude-auth
    ports:
      - "8001:8001"
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=biocloude
      - DB_USER=biocloude
      - DB_PASSWORD=biocloude123
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-jwt-secret-key
      - CIAM_CLIENT_ID=your-ciam-client-id
      - CIAM_CLIENT_SECRET=your-ciam-client-secret
    depends_on:
      - postgres
      - redis
    networks:
      - biocloude-network

  # 订阅管理服务
  subscription-service:
    build:
      context: ./services/subscription-service
      dockerfile: Dockerfile
    container_name: biocloude-subscription
    ports:
      - "8002:8002"
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=biocloude
      - DB_USER=biocloude
      - DB_PASSWORD=biocloude123
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AUTH_SERVICE_URL=http://auth-service:8001
    depends_on:
      - postgres
      - redis
      - auth-service
    networks:
      - biocloude-network

  # 支付网关服务
  payment-service:
    build:
      context: ./services/payment-service
      dockerfile: Dockerfile
    container_name: biocloude-payment
    ports:
      - "8003:8003"
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=biocloude
      - DB_USER=biocloude
      - DB_PASSWORD=biocloude123
      - WECHAT_APP_ID=your-wechat-app-id
      - WECHAT_MCH_ID=your-wechat-mch-id
      - WECHAT_API_KEY=your-wechat-api-key
      - ALIPAY_APP_ID=your-alipay-app-id
      - ALIPAY_PRIVATE_KEY=your-alipay-private-key
    depends_on:
      - postgres
      - subscription-service
    networks:
      - biocloude-network

  # 通知服务
  notification-service:
    build:
      context: ./services/notification-service
      dockerfile: Dockerfile
    container_name: biocloude-notification
    ports:
      - "8004:8004"
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=biocloude
      - DB_USER=biocloude
      - DB_PASSWORD=biocloude123
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SMTP_HOST=smtp.qq.com
      - SMTP_PORT=587
      - SMTP_USER=<EMAIL>
      - SMTP_PASSWORD=your-email-password
    depends_on:
      - postgres
      - redis
    networks:
      - biocloude-network

  # AI服务 - 资讯处理
  newsletter-ai:
    build:
      context: ./services/ai-services/newsletter-ai
      dockerfile: Dockerfile
    container_name: biocloude-newsletter-ai
    ports:
      - "9001:9001"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AUTH_SERVICE_URL=http://auth-service:8001
    depends_on:
      - redis
      - auth-service
    networks:
      - biocloude-network

  # AI服务 - 文献阅读
  scholar-ai:
    build:
      context: ./services/ai-services/scholar-ai
      dockerfile: Dockerfile
    container_name: biocloude-scholar-ai
    ports:
      - "9002:9002"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AUTH_SERVICE_URL=http://auth-service:8001
      - SEMANTIC_SCHOLAR_API_KEY=your-semantic-scholar-api-key
    depends_on:
      - redis
      - auth-service
    networks:
      - biocloude-network

  # AI服务 - 引物设计
  primer-ai:
    build:
      context: ./services/ai-services/primer-ai
      dockerfile: Dockerfile
    container_name: biocloude-primer-ai
    ports:
      - "9003:9003"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AUTH_SERVICE_URL=http://auth-service:8001
    depends_on:
      - redis
      - auth-service
    networks:
      - biocloude-network

  # AI服务 - 蛋白质设计
  protein-ai:
    build:
      context: ./services/ai-services/protein-ai
      dockerfile: Dockerfile
    container_name: biocloude-protein-ai
    ports:
      - "9004:9004"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AUTH_SERVICE_URL=http://auth-service:8001
    depends_on:
      - redis
      - auth-service
    networks:
      - biocloude-network

  # AI服务 - 基因编辑
  gene-edit-ai:
    build:
      context: ./services/ai-services/gene-edit-ai
      dockerfile: Dockerfile
    container_name: biocloude-gene-edit-ai
    ports:
      - "9005:9005"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AUTH_SERVICE_URL=http://auth-service:8001
    depends_on:
      - redis
      - auth-service
    networks:
      - biocloude-network

  # AI服务 - 代谢工程
  metabolic-ai:
    build:
      context: ./services/ai-services/metabolic-ai
      dockerfile: Dockerfile
    container_name: biocloude-metabolic-ai
    ports:
      - "9006:9006"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AUTH_SERVICE_URL=http://auth-service:8001
    depends_on:
      - redis
      - auth-service
    networks:
      - biocloude-network

  # 主站门户
  main-portal:
    build:
      context: ./web-apps/main-portal
      dockerfile: Dockerfile
    container_name: biocloude-main-portal
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost/api
      - NEXT_PUBLIC_AUTH_URL=http://localhost/auth
    depends_on:
      - gateway
    networks:
      - biocloude-network

volumes:
  postgres_data:
  redis_data:

networks:
  biocloude-network:
    driver: bridge
