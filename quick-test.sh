#!/bin/bash

# 快速测试修复是否成功

echo "🧪 快速测试路径和服务修复..."

# 检测项目根目录
if [ -f "/workspace/biocloude/docker-compose.yml" ] || [ -d "/workspace/biocloude/.devcontainer" ]; then
    PROJECT_ROOT="/workspace/biocloude"
    cd "$PROJECT_ROOT"
elif [ -f "docker-compose.yml" ] || [ -d ".devcontainer" ]; then
    PROJECT_ROOT=$(pwd)
else
    echo "❌ 无法找到项目根目录"
    exit 1
fi

echo "📍 项目根目录: $PROJECT_ROOT"

# 测试 AI 服务文件
echo ""
echo "🤖 测试 AI 服务文件:"
ai_services=("newsletter-ai" "scholar-ai" "primer-ai" "protein-ai" "gene-editing-ai" "metabolic-ai")
ai_missing=0

for service in "${ai_services[@]}"; do
    main_py="$PROJECT_ROOT/services/ai-services/$service/main.py"
    if [ -f "$main_py" ]; then
        echo "  ✅ $service/main.py"
    else
        echo "  ❌ $service/main.py 缺失"
        ai_missing=$((ai_missing + 1))
    fi
done

# 测试 Go 服务文件
echo ""
echo "🔧 测试 Go 服务文件:"
go_missing=0

for service in "auth-service" "subscription-service"; do
    main_go="$PROJECT_ROOT/services/$service/main.go"
    go_mod="$PROJECT_ROOT/services/$service/go.mod"
    
    if [ -f "$main_go" ] && [ -f "$go_mod" ]; then
        echo "  ✅ $service (main.go + go.mod)"
    else
        echo "  ❌ $service 文件缺失"
        go_missing=$((go_missing + 1))
    fi
done

# 测试 Go 模块
echo ""
echo "🔍 测试 Go 模块依赖:"
cd "$PROJECT_ROOT/services/auth-service"
if go mod verify 2>/dev/null; then
    echo "  ✅ auth-service go.mod 有效"
else
    echo "  ⚠️ auth-service go.mod 需要下载依赖"
fi

cd "$PROJECT_ROOT/services/subscription-service"
if go mod verify 2>/dev/null; then
    echo "  ✅ subscription-service go.mod 有效"
else
    echo "  ⚠️ subscription-service go.mod 需要下载依赖"
fi

cd "$PROJECT_ROOT"

# 总结
echo ""
echo "📊 测试结果:"
if [ $ai_missing -eq 0 ] && [ $go_missing -eq 0 ]; then
    echo "  ✅ 所有服务文件都存在"
    echo "  🚀 可以运行: ./start-dev.sh"
else
    echo "  ❌ 发现 $ai_missing 个 AI 服务文件缺失"
    echo "  ❌ 发现 $go_missing 个 Go 服务文件缺失"
    echo "  🔧 请运行: ./create-structure.ps1"
fi

echo ""
echo "🎯 下一步:"
echo "  1. 运行: ./start-dev.sh"
echo "  2. 检查: ./status-dev.sh"
echo "  3. 如有问题: ./quick-fix.sh"
