@echo off
REM Windows 环境下启动 Docker 基础服务脚本

echo 🐳 启动 Docker 基础服务...

REM 检查 Docker 是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未安装或未运行
    echo.
    echo 请先安装 Docker Desktop:
    echo 1. 访问 https://www.docker.com/products/docker-desktop/
    echo 2. 下载并安装 Docker Desktop for Windows
    echo 3. 启动 Docker Desktop
    echo 4. 等待 Docker 完全启动
    echo.
    echo 或者使用 GitHub Codespaces ^(推荐^):
    echo 1. 访问 GitHub 仓库
    echo 2. 点击 Code → Codespaces → Create codespace
    echo.
    pause
    exit /b 1
)

echo ✅ Docker 已安装

REM 检查 Docker 是否运行
docker ps >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未运行
    echo 请启动 Docker Desktop，然后重新运行此脚本
    pause
    exit /b 1
)

echo ✅ Docker 正在运行

REM 检查 docker-compose 文件
if not exist ".devcontainer\docker-compose.yml" (
    echo ❌ 未找到 docker-compose.yml 文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo 🚀 启动基础服务...
echo 这可能需要几分钟时间，请耐心等待...

REM 启动服务
docker-compose -f .devcontainer\docker-compose.yml up -d

if %errorlevel% neq 0 (
    echo ❌ 启动 Docker 服务失败
    echo 请检查 Docker Desktop 是否正常运行
    pause
    exit /b 1
)

echo ✅ Docker 容器已启动

echo.
echo ⏰ 等待服务启动...

REM 检查容器状态
echo 📊 检查容器状态...
docker-compose -f .devcontainer\docker-compose.yml ps

echo.
echo 🗄️ 等待 PostgreSQL 启动...
echo 这可能需要 2-5 分钟，请耐心等待...

REM 等待 PostgreSQL 启动
set /a attempts=0
set /a maxAttempts=60

:wait_postgres
timeout /t 2 /nobreak >nul
set /a attempts+=1

REM 检查 PostgreSQL 是否准备就绪
for /f "tokens=*" %%i in ('docker ps --filter "name=postgres" --format "{{.Names}}" 2^>nul') do (
    docker exec %%i pg_isready -U biocloude >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ PostgreSQL 已启动并准备就绪
        goto postgres_ready
    )
)

if %attempts% geq %maxAttempts% (
    echo ⚠️ PostgreSQL 启动超时，但容器可能仍在初始化
    echo    数据库初始化可能需要更多时间
    goto postgres_done
)

set /a remainder=%attempts% %% 10
if %remainder% equ 0 (
    echo    仍在等待 PostgreSQL... ^(%attempts%/%maxAttempts%^)
)

goto wait_postgres

:postgres_ready
echo ✅ PostgreSQL 启动完成

:postgres_done
echo.
echo 🎉 基础服务启动完成！
echo.
echo 📋 服务访问地址:
echo   🗄️ PostgreSQL:    localhost:5432 ^(用户: biocloude, 密码: biocloude123^)
echo   🔴 Redis:          localhost:6379 ^(密码: biocloude123^)
echo   📧 MailHog:        http://localhost:8025 ^(SMTP: localhost:1025^)
echo   💾 MinIO:          http://localhost:9090 ^(用户: biocloude, 密码: biocloude123^)
echo   📊 Prometheus:     http://localhost:9091
echo   📈 Grafana:        http://localhost:3030 ^(用户: admin, 密码: biocloude123^)
echo.
echo ⚡ 下一步:
echo   启动应用服务:      start-dev.ps1 或 start-dev.bat
echo   检查服务状态:      status-dev.ps1
echo   查看容器日志:      docker-compose -f .devcontainer\docker-compose.yml logs
echo.
echo 💡 提示:
echo   - PostgreSQL 初始化可能需要几分钟
echo   - 如果服务未就绪，请等待更长时间
echo   - 使用 GitHub Codespaces 可以避免本地环境问题
echo.

pause
