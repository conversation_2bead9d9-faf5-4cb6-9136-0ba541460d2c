# ⚡ 快速修复指南

## 🚨 常见问题快速修复

### 问题1: `./start-dev.sh: No such file or directory`

#### 🎯 一键修复 (复制粘贴运行)

```bash
# 修复脚本权限并启动服务
chmod +x *.sh && ./start-dev.sh
```

如果上面的命令不工作，尝试：

```bash
# 使用 bash 直接运行
bash start-dev.sh
```

### 问题2: PostgreSQL 启动超时

#### 🎯 一键检查和等待

```bash
# 运行 PostgreSQL 诊断
chmod +x check-postgres.sh && ./check-postgres.sh

# 或等待 PostgreSQL 启动
echo "等待 PostgreSQL..." && while ! pg_isready -h localhost -p 5432 -U biocloude 2>/dev/null; do echo "仍在等待..."; sleep 5; done && echo "PostgreSQL 已就绪！"
```

## 🔧 详细修复步骤

### 步骤1: 修复权限
```bash
# 给所有脚本添加执行权限
chmod +x start-dev.sh
chmod +x stop-dev.sh
chmod +x status-dev.sh
chmod +x test-dev.sh
chmod +x fix-permissions.sh
```

### 步骤2: 验证修复
```bash
# 检查权限
ls -la *.sh

# 应该看到类似这样的输出：
# -rwxr-xr-x 1 <USER> <GROUP> 8714 Jul 12 17:29 start-dev.sh
```

### 步骤3: 启动服务
```bash
# 现在可以正常运行
./start-dev.sh
```

## 🪟 Windows 用户

如果您在 Windows 环境中，请使用：

```powershell
# PowerShell 版本
.\start-dev.ps1

# 或批处理版本
start-dev.bat
```

## 🌐 GitHub Codespaces 用户

在 Codespaces 中，权限应该自动修复。如果没有：

```bash
# 运行修复脚本
bash fix-permissions.sh

# 或手动修复
chmod +x *.sh
./start-dev.sh
```

## 🆘 仍然有问题？

### 备用方案
```bash
# 方案1: 使用 bash 命令
bash start-dev.sh
bash status-dev.sh

# 方案2: 重新克隆仓库
git clone <repository-url>
cd biocloude
chmod +x *.sh
./start-dev.sh

# 方案3: 使用 Codespaces
# 在 GitHub 上点击 Code → Codespaces → Create codespace
```

### 检查环境
```bash
# 检查当前目录
pwd
ls -la

# 检查 shell
echo $SHELL

# 检查权限
ls -la *.sh
```

## 📞 获取帮助

1. 📖 查看 [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
2. 📖 查看 [SCRIPTS_GUIDE.md](SCRIPTS_GUIDE.md)
3. 🐛 创建 GitHub Issue
4. 💬 联系团队支持

---

🎯 **90% 的问题都可以通过 `chmod +x *.sh` 解决！**
