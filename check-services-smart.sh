#!/bin/bash

# 智能服务状态检查脚本 - 适用于 Codespaces 环境

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔍 智能服务状态检查 (Codespaces 优化版)"
echo "============================================"

# 检测环境
if [ -n "$CODESPACES" ]; then
    log_info "检测到 Codespaces 环境"
    IN_CODESPACES=true
else
    log_info "本地环境"
    IN_CODESPACES=false
fi

# 服务定义
declare -A SERVICES=(
    ["3000"]="主站门户"
    ["3001"]="资讯处理应用"
    ["3002"]="文献阅读应用"
    ["8001"]="认证服务"
    ["8002"]="订阅服务"
    ["9001"]="资讯AI"
    ["9002"]="文献AI"
    ["9003"]="引物AI"
    ["9004"]="蛋白质AI"
    ["9005"]="基因编辑AI"
    ["9006"]="代谢工程AI"
)

# 检查端口是否监听
check_port_listening() {
    local port=$1
    
    # 尝试多种方法检查端口
    if command -v netstat >/dev/null 2>&1; then
        netstat -tln 2>/dev/null | grep -q ":$port "
    elif command -v ss >/dev/null 2>&1; then
        ss -tln 2>/dev/null | grep -q ":$port "
    elif command -v lsof >/dev/null 2>&1; then
        lsof -i :$port >/dev/null 2>&1
    else
        # 最后尝试连接测试
        timeout 2 bash -c "</dev/tcp/localhost/$port" 2>/dev/null
    fi
}

# 检查 HTTP 响应
check_http_response() {
    local port=$1
    local endpoint=$2
    
    if [ "$IN_CODESPACES" = true ]; then
        # 在 Codespaces 中，HTTP 检查可能不可靠，主要依赖端口检查
        return 1
    else
        # 本地环境中尝试 HTTP 检查
        if command -v curl >/dev/null 2>&1; then
            curl -f -s --connect-timeout 3 "http://localhost:$port$endpoint" >/dev/null 2>&1
        else
            return 1
        fi
    fi
}

# 主要检查逻辑
echo ""
log_info "检查服务状态..."
echo ""

total_services=0
running_services=0
responding_services=0

for port in $(echo "${!SERVICES[@]}" | tr ' ' '\n' | sort -n); do
    service_name="${SERVICES[$port]}"
    total_services=$((total_services + 1))
    
    printf "%-20s (端口 %s): " "$service_name" "$port"
    
    # 检查端口监听
    if check_port_listening "$port"; then
        running_services=$((running_services + 1))
        
        # 尝试 HTTP 检查
        http_ok=false
        if [ "$port" -ge 8000 ] && [ "$port" -le 9999 ]; then
            # API 服务，检查 /health 端点
            if check_http_response "$port" "/health"; then
                http_ok=true
                responding_services=$((responding_services + 1))
            fi
        elif [ "$port" -ge 3000 ] && [ "$port" -le 3999 ]; then
            # 前端服务，检查根路径
            if check_http_response "$port" "/"; then
                http_ok=true
                responding_services=$((responding_services + 1))
            fi
        fi
        
        if [ "$http_ok" = true ]; then
            echo -e "${GREEN}✅ 运行正常${NC}"
        elif [ "$IN_CODESPACES" = true ]; then
            echo -e "${GREEN}✅ 端口监听 ${CYAN}(Codespaces 转发)${NC}"
        else
            echo -e "${YELLOW}⚠️ 端口监听但无响应${NC}"
        fi
    else
        echo -e "${RED}❌ 未启动${NC}"
    fi
done

# 统计和建议
echo ""
echo "📊 统计信息:"
echo "============"
echo "总服务数: $total_services"
echo "端口监听: $running_services"
if [ "$IN_CODESPACES" = false ]; then
    echo "HTTP响应: $responding_services"
fi

echo ""
if [ "$running_services" -eq "$total_services" ]; then
    log_success "🎉 所有服务都在运行！"
    
    if [ "$IN_CODESPACES" = true ]; then
        echo ""
        log_info "🌐 在 Codespaces 中访问服务:"
        echo "  1. 查看 VS Code 的'端口'标签页"
        echo "  2. 点击端口号旁的🌐图标访问服务"
        echo "  3. 或直接访问以下地址:"
        echo "     - 主站: http://localhost:3000"
        echo "     - 资讯AI: http://localhost:9001"
        echo "     - 文献AI: http://localhost:9002"
    else
        echo ""
        log_info "🌐 访问服务:"
        echo "  - 主站门户: http://localhost:3000"
        echo "  - API文档: http://localhost:9001/docs"
    fi
    
elif [ "$running_services" -gt $((total_services * 2 / 3)) ]; then
    log_warning "⚠️ 大部分服务在运行，但有些可能需要更多时间启动"
    echo ""
    echo "建议操作:"
    echo "  1. 等待 1-2 分钟让服务完全启动"
    echo "  2. 重新运行此检查: ./check-services-smart.sh"
    echo "  3. 如果问题持续，重启服务: ./stop-dev.sh && ./start-dev.sh"
    
else
    log_error "❌ 多数服务未启动"
    echo ""
    echo "建议操作:"
    echo "  1. 重启所有服务: ./stop-dev.sh && ./start-dev.sh"
    echo "  2. 检查启动日志中的错误信息"
    echo "  3. 确保所有依赖已正确安装"
fi

# Codespaces 特殊提示
if [ "$IN_CODESPACES" = true ]; then
    echo ""
    log_info "💡 Codespaces 提示:"
    echo "  - 端口会自动转发，无需手动配置"
    echo "  - 如果浏览器访问正常，说明服务运行正常"
    echo "  - curl/wget 可能失败，但这是正常现象"
fi

echo ""
echo "🔧 其他有用命令:"
echo "  - 查看详细状态: ./status-dev.sh"
echo "  - 停止所有服务: ./stop-dev.sh"
echo "  - 重启服务: ./stop-dev.sh && ./start-dev.sh"
