package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// Product 产品模型
type Product struct {
	ID          uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string          `json:"name" gorm:"not null"`
	Slug        string          `json:"slug" gorm:"uniqueIndex;not null"`
	Domain      string          `json:"domain" gorm:"uniqueIndex;not null"`
	Description string          `json:"description"`
	BasePrice   decimal.Decimal `json:"base_price" gorm:"type:decimal(10,2);default:0"`
	BillingType string          `json:"billing_type" gorm:"default:'subscription'"` // subscription, usage, one_time
	Features    string          `json:"features" gorm:"type:jsonb"`                 // JSON格式的功能配置
	IsActive    bool            `json:"is_active" gorm:"default:true"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	Subscriptions []Subscription `json:"-" gorm:"foreignKey:ProductID"`
	PricingPlans  []PricingPlan  `json:"pricing_plans,omitempty" gorm:"foreignKey:ProductID"`
}

// BeforeCreate GORM钩子：创建前
func (p *Product) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (Product) TableName() string {
	return "products"
}

// PricingPlan 定价计划
type PricingPlan struct {
	ID          uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ProductID   uuid.UUID       `json:"product_id" gorm:"type:uuid;not null;index"`
	Name        string          `json:"name" gorm:"not null"`
	Description string          `json:"description"`
	Price       decimal.Decimal `json:"price" gorm:"type:decimal(10,2);not null"`
	BillingCycle string         `json:"billing_cycle" gorm:"not null"` // monthly, yearly, weekly
	Features    string          `json:"features" gorm:"type:jsonb"`    // JSON格式的功能配置
	IsActive    bool            `json:"is_active" gorm:"default:true"`
	SortOrder   int             `json:"sort_order" gorm:"default:0"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联
	Product       Product        `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	Subscriptions []Subscription `json:"-" gorm:"foreignKey:PricingPlanID"`
}

// BeforeCreate GORM钩子：创建前
func (pp *PricingPlan) BeforeCreate(tx *gorm.DB) error {
	if pp.ID == uuid.Nil {
		pp.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (PricingPlan) TableName() string {
	return "pricing_plans"
}

// Subscription 订阅模型
type Subscription struct {
	ID            uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID        uuid.UUID       `json:"user_id" gorm:"type:uuid;not null;index"`
	ProductID     uuid.UUID       `json:"product_id" gorm:"type:uuid;not null;index"`
	PricingPlanID *uuid.UUID      `json:"pricing_plan_id" gorm:"type:uuid;index"`
	Status        string          `json:"status" gorm:"default:'active'"` // active, cancelled, expired, suspended
	Price         decimal.Decimal `json:"price" gorm:"type:decimal(10,2);not null"`
	BillingCycle  string          `json:"billing_cycle" gorm:"default:'monthly'"` // monthly, yearly, weekly
	StartDate     time.Time       `json:"start_date" gorm:"default:CURRENT_TIMESTAMP"`
	EndDate       *time.Time      `json:"end_date"`
	AutoRenew     bool            `json:"auto_renew" gorm:"default:true"`
	Metadata      string          `json:"metadata" gorm:"type:jsonb"` // JSON格式的额外信息
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	Product     Product      `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	PricingPlan *PricingPlan `json:"pricing_plan,omitempty" gorm:"foreignKey:PricingPlanID"`
	Orders      []Order      `json:"-" gorm:"foreignKey:SubscriptionID"`
	
	// 订阅配置
	SubscriptionConfigs []SubscriptionConfig `json:"configs,omitempty" gorm:"foreignKey:SubscriptionID"`
}

// BeforeCreate GORM钩子：创建前
func (s *Subscription) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (Subscription) TableName() string {
	return "subscriptions"
}

// IsActive 检查订阅是否有效
func (s *Subscription) IsActive() bool {
	if s.Status != "active" {
		return false
	}
	if s.EndDate != nil && time.Now().After(*s.EndDate) {
		return false
	}
	return true
}

// IsExpired 检查订阅是否过期
func (s *Subscription) IsExpired() bool {
	return s.EndDate != nil && time.Now().After(*s.EndDate)
}

// SubscriptionConfig 订阅配置
type SubscriptionConfig struct {
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	SubscriptionID uuid.UUID `json:"subscription_id" gorm:"type:uuid;not null;index"`
	ConfigKey      string    `json:"config_key" gorm:"not null"`
	ConfigValue    string    `json:"config_value" gorm:"type:jsonb"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联
	Subscription Subscription `json:"-" gorm:"foreignKey:SubscriptionID"`
}

// BeforeCreate GORM钩子：创建前
func (sc *SubscriptionConfig) BeforeCreate(tx *gorm.DB) error {
	if sc.ID == uuid.Nil {
		sc.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (SubscriptionConfig) TableName() string {
	return "subscription_configs"
}

// NewsletterSubscription 资讯订阅配置
type NewsletterSubscription struct {
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	SubscriptionID uuid.UUID `json:"subscription_id" gorm:"type:uuid;not null;index"`
	
	// 订阅配置
	Keywords   string `json:"keywords" gorm:"type:jsonb"`   // JSON数组格式的关键词
	Categories string `json:"categories" gorm:"type:jsonb"` // JSON数组格式的分类
	Sources    string `json:"sources" gorm:"type:jsonb"`    // JSON数组格式的数据源
	
	// 推送设置
	Frequency           string `json:"frequency" gorm:"default:'daily'"`        // realtime, hourly, daily, weekly
	EmailNotification   bool   `json:"email_notification" gorm:"default:true"`
	SMSNotification     bool   `json:"sms_notification" gorm:"default:false"`
	PushNotification    bool   `json:"push_notification" gorm:"default:true"`
	DigestTime          string `json:"digest_time" gorm:"default:'09:00'"`       // HH:MM格式
	MaxArticlesPerDay   int    `json:"max_articles_per_day" gorm:"default:50"`
	MinRelevanceScore   float64 `json:"min_relevance_score" gorm:"default:0.6"`
	
	// 过滤设置
	EnableAIFiltering   bool   `json:"enable_ai_filtering" gorm:"default:true"`
	EnableDuplicateFilter bool `json:"enable_duplicate_filter" gorm:"default:true"`
	PriorityFilter      string `json:"priority_filter" gorm:"default:'all'"` // all, high, medium
	
	IsActive  bool      `json:"is_active" gorm:"default:true"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联
	Subscription Subscription `json:"subscription,omitempty" gorm:"foreignKey:SubscriptionID"`
}

// BeforeCreate GORM钩子：创建前
func (ns *NewsletterSubscription) BeforeCreate(tx *gorm.DB) error {
	if ns.ID == uuid.Nil {
		ns.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (NewsletterSubscription) TableName() string {
	return "newsletter_subscriptions"
}

// Order 订单模型
type Order struct {
	ID             uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID         uuid.UUID       `json:"user_id" gorm:"type:uuid;not null;index"`
	SubscriptionID *uuid.UUID      `json:"subscription_id" gorm:"type:uuid;index"`
	Amount         decimal.Decimal `json:"amount" gorm:"type:decimal(10,2);not null"`
	Currency       string          `json:"currency" gorm:"default:'CNY'"`
	Status         string          `json:"status" gorm:"default:'pending'"` // pending, paid, failed, refunded
	PaymentMethod  string          `json:"payment_method"`                  // wechat, alipay, bank_card
	PaymentID      string          `json:"payment_id"`                      // 第三方支付平台的交易ID
	PaymentData    string          `json:"payment_data" gorm:"type:jsonb"`  // 支付相关的额外数据
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联
	Subscription *Subscription `json:"subscription,omitempty" gorm:"foreignKey:SubscriptionID"`
}

// BeforeCreate GORM钩子：创建前
func (o *Order) BeforeCreate(tx *gorm.DB) error {
	if o.ID == uuid.Nil {
		o.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (Order) TableName() string {
	return "orders"
}

// APIUsage API使用统计
type APIUsage struct {
	ID           uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID       uuid.UUID       `json:"user_id" gorm:"type:uuid;not null;index"`
	ProductID    uuid.UUID       `json:"product_id" gorm:"type:uuid;not null;index"`
	Endpoint     string          `json:"endpoint" gorm:"not null"`
	Method       string          `json:"method" gorm:"not null"`
	RequestCount int             `json:"request_count" gorm:"default:1"`
	ResponseTime int             `json:"response_time_ms"`
	StatusCode   int             `json:"status_code"`
	Date         time.Time       `json:"date" gorm:"type:date;default:CURRENT_DATE;index"`
	Cost         decimal.Decimal `json:"cost" gorm:"type:decimal(10,4);default:0"`
	
	CreatedAt time.Time `json:"created_at"`
}

// BeforeCreate GORM钩子：创建前
func (au *APIUsage) BeforeCreate(tx *gorm.DB) error {
	if au.ID == uuid.Nil {
		au.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (APIUsage) TableName() string {
	return "api_usage"
}

// Invitation 邀请返佣
type Invitation struct {
	ID               uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	InviterID        uuid.UUID       `json:"inviter_id" gorm:"type:uuid;not null;index"`
	InviteeID        *uuid.UUID      `json:"invitee_id" gorm:"type:uuid;index"`
	ProductID        *uuid.UUID      `json:"product_id" gorm:"type:uuid;index"`
	InvitationCode   string          `json:"invitation_code" gorm:"uniqueIndex;not null"`
	CommissionRate   decimal.Decimal `json:"commission_rate" gorm:"type:decimal(5,4);default:0.1000"` // 10%佣金率
	CommissionAmount decimal.Decimal `json:"commission_amount" gorm:"type:decimal(10,2);default:0"`
	Status           string          `json:"status" gorm:"default:'pending'"` // pending, completed, cancelled
	CompletedAt      *time.Time      `json:"completed_at"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// BeforeCreate GORM钩子：创建前
func (i *Invitation) BeforeCreate(tx *gorm.DB) error {
	if i.ID == uuid.Nil {
		i.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (Invitation) TableName() string {
	return "invitations"
}

// ProductCategory 产品分类
type ProductCategory struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"not null"`
	Slug        string    `json:"slug" gorm:"uniqueIndex;not null"`
	Description string    `json:"description"`
	SortOrder   int       `json:"sort_order" gorm:"default:0"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// BeforeCreate GORM钩子：创建前
func (pc *ProductCategory) BeforeCreate(tx *gorm.DB) error {
	if pc.ID == uuid.Nil {
		pc.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (ProductCategory) TableName() string {
	return "product_categories"
}

// ProductCategoryRelation 产品与分类的关联
type ProductCategoryRelation struct {
	ProductID  uuid.UUID `json:"product_id" gorm:"type:uuid;primaryKey"`
	CategoryID uuid.UUID `json:"category_id" gorm:"type:uuid;primaryKey"`
	
	CreatedAt time.Time `json:"created_at"`
	
	// 关联
	Product  Product         `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	Category ProductCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
}

// TableName 指定表名
func (ProductCategoryRelation) TableName() string {
	return "product_category_relations"
}

// SubscriptionHistory 订阅历史记录
type SubscriptionHistory struct {
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	SubscriptionID uuid.UUID `json:"subscription_id" gorm:"type:uuid;not null;index"`
	Action         string    `json:"action" gorm:"not null"` // created, renewed, cancelled, suspended, reactivated
	OldStatus      string    `json:"old_status"`
	NewStatus      string    `json:"new_status"`
	Reason         string    `json:"reason"`
	Metadata       string    `json:"metadata" gorm:"type:jsonb"`
	
	CreatedAt time.Time `json:"created_at"`
	
	// 关联
	Subscription Subscription `json:"subscription,omitempty" gorm:"foreignKey:SubscriptionID"`
}

// BeforeCreate GORM钩子：创建前
func (sh *SubscriptionHistory) BeforeCreate(tx *gorm.DB) error {
	if sh.ID == uuid.Nil {
		sh.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (SubscriptionHistory) TableName() string {
	return "subscription_history"
}
