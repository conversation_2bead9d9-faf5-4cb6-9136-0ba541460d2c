apiVersion: apps/v1
kind: Deployment
metadata:
  name: newsletter-ai
  namespace: biocloude
  labels:
    app: newsletter-ai
    component: ai-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: newsletter-ai
  template:
    metadata:
      labels:
        app: newsletter-ai
        component: ai-service
    spec:
      containers:
      - name: newsletter-ai
        image: biocloude/newsletter-ai:latest
        ports:
        - containerPort: 9001
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: redis-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: openai-api-key
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 9001
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: newsletter-ai-service
  namespace: biocloude
  labels:
    app: newsletter-ai
spec:
  selector:
    app: newsletter-ai
  ports:
  - name: http
    port: 9001
    targetPort: 9001
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: newsletter-ai-hpa
  namespace: biocloude
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: newsletter-ai
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
