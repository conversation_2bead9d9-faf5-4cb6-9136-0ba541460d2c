"""
生科云码代谢工程AI
设计和优化微生物细胞工厂的代谢途径
"""

import asyncio
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from fastapi import FastAPI, HTTPException, Depends, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
import asyncpg
import redis.asyncio as redis
from loguru import logger
import numpy as np
import networkx as nx

# 配置日志
logger.add("logs/metabolic_ai.log", rotation="1 day", retention="30 days")

app = FastAPI(
    title="生科云码代谢工程AI",
    description="设计和优化微生物细胞工厂的代谢途径",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置
REDIS_URL = "redis://redis:6379/5"
DATABASE_URL = "*************************************************/biocloude"

# 全局变量
redis_client: Optional[redis.Redis] = None
db_pool: Optional[asyncpg.Pool] = None

# 数据模型
class PathwayDesignRequest(BaseModel):
    target_product: str = Field(..., description="目标产物")
    starting_substrate: str = Field(..., description="起始底物")
    host_organism: str = Field(default="E.coli", description="宿主微生物")
    optimization_objective: str = Field(default="yield", description="优化目标：yield, productivity, titer")
    constraints: Dict[str, Any] = Field(default={}, description="约束条件")

class PathwayOptimizationRequest(BaseModel):
    pathway_id: str = Field(..., description="途径ID")
    optimization_targets: List[str] = Field(..., description="优化目标")
    expression_levels: Dict[str, float] = Field(default={}, description="表达水平")
    knockout_genes: List[str] = Field(default=[], description="敲除基因")

class FluxAnalysisRequest(BaseModel):
    pathway_model: Dict[str, Any] = Field(..., description="途径模型")
    objective_function: str = Field(..., description="目标函数")
    constraints: Dict[str, Any] = Field(default={}, description="约束条件")

class StrainDesignRequest(BaseModel):
    target_pathway: Dict[str, Any] = Field(..., description="目标途径")
    host_strain: str = Field(..., description="宿主菌株")
    genetic_modifications: List[str] = Field(default=[], description="遗传修饰")
    culture_conditions: Dict[str, Any] = Field(default={}, description="培养条件")

class FermentationOptimizationRequest(BaseModel):
    strain_id: str = Field(..., description="菌株ID")
    medium_composition: Dict[str, float] = Field(..., description="培养基组成")
    process_parameters: Dict[str, float] = Field(..., description="工艺参数")
    optimization_target: str = Field(default="productivity", description="优化目标")

# 代谢途径设计服务
class PathwayDesignService:
    """代谢途径设计服务"""
    
    def __init__(self):
        # 代谢反应数据库（简化版）
        self.reaction_database = {
            "glucose_to_pyruvate": {
                "substrates": ["glucose"],
                "products": ["pyruvate"],
                "enzymes": ["hexokinase", "phosphoglucose_isomerase", "phosphofructokinase"],
                "energy_yield": -2,  # ATP消耗
                "cofactors": ["ATP", "ADP", "NAD+", "NADH"]
            },
            "pyruvate_to_acetyl_coa": {
                "substrates": ["pyruvate"],
                "products": ["acetyl_coa"],
                "enzymes": ["pyruvate_dehydrogenase"],
                "energy_yield": 0,
                "cofactors": ["CoA", "NAD+", "NADH"]
            },
            "acetyl_coa_to_ethanol": {
                "substrates": ["acetyl_coa"],
                "products": ["ethanol"],
                "enzymes": ["alcohol_dehydrogenase"],
                "energy_yield": 1,
                "cofactors": ["NADH", "NAD+"]
            }
        }
        
        # 宿主微生物特性
        self.host_characteristics = {
            "E.coli": {
                "growth_rate": 0.7,
                "glucose_uptake": 10.0,
                "oxygen_requirement": "facultative",
                "temperature_optimum": 37,
                "pH_optimum": 7.0
            },
            "S.cerevisiae": {
                "growth_rate": 0.3,
                "glucose_uptake": 8.0,
                "oxygen_requirement": "facultative",
                "temperature_optimum": 30,
                "pH_optimum": 5.5
            },
            "B.subtilis": {
                "growth_rate": 0.5,
                "glucose_uptake": 6.0,
                "oxygen_requirement": "aerobic",
                "temperature_optimum": 37,
                "pH_optimum": 7.2
            }
        }
    
    async def design_pathway(self, request: PathwayDesignRequest) -> Dict[str, Any]:
        """设计代谢途径"""
        try:
            # 搜索可能的代谢途径
            possible_pathways = self._search_pathways(
                request.starting_substrate,
                request.target_product
            )
            
            # 评估每条途径
            evaluated_pathways = []
            for pathway in possible_pathways:
                evaluation = self._evaluate_pathway(pathway, request)
                evaluated_pathways.append({
                    "pathway": pathway,
                    "evaluation": evaluation,
                    "score": evaluation["overall_score"]
                })
            
            # 排序并返回最佳途径
            best_pathways = sorted(evaluated_pathways, key=lambda x: x["score"], reverse=True)[:5]
            
            return {
                "target_product": request.target_product,
                "starting_substrate": request.starting_substrate,
                "host_organism": request.host_organism,
                "designed_pathways": best_pathways,
                "total_pathways_found": len(possible_pathways),
                "design_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"途径设计失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _search_pathways(self, substrate: str, product: str) -> List[Dict[str, Any]]:
        """搜索代谢途径"""
        pathways = []
        
        # 简化的途径搜索算法
        if substrate == "glucose" and product == "ethanol":
            pathways.append({
                "name": "Ethanol production pathway",
                "reactions": [
                    "glucose_to_pyruvate",
                    "pyruvate_to_acetyl_coa", 
                    "acetyl_coa_to_ethanol"
                ],
                "length": 3,
                "type": "fermentation"
            })
        
        # 添加更多途径搜索逻辑
        pathways.extend(self._generate_alternative_pathways(substrate, product))
        
        return pathways
    
    def _generate_alternative_pathways(self, substrate: str, product: str) -> List[Dict[str, Any]]:
        """生成替代途径"""
        alternatives = []
        
        # 基于图搜索算法生成替代途径
        for i in range(3):  # 生成3条替代途径
            alternatives.append({
                "name": f"Alternative pathway {i+1}",
                "reactions": [f"reaction_{j}" for j in range(2, 6)],
                "length": np.random.randint(2, 6),
                "type": "synthetic"
            })
        
        return alternatives
    
    def _evaluate_pathway(self, pathway: Dict[str, Any], request: PathwayDesignRequest) -> Dict[str, Any]:
        """评估代谢途径"""
        # 计算理论产率
        theoretical_yield = self._calculate_theoretical_yield(pathway)
        
        # 计算能量平衡
        energy_balance = self._calculate_energy_balance(pathway)
        
        # 评估途径复杂度
        complexity_score = self._evaluate_complexity(pathway)
        
        # 评估宿主兼容性
        host_compatibility = self._evaluate_host_compatibility(pathway, request.host_organism)
        
        # 计算总体评分
        overall_score = (
            theoretical_yield * 0.3 +
            energy_balance * 0.2 +
            complexity_score * 0.2 +
            host_compatibility * 0.3
        )
        
        return {
            "theoretical_yield": theoretical_yield,
            "energy_balance": energy_balance,
            "complexity_score": complexity_score,
            "host_compatibility": host_compatibility,
            "overall_score": overall_score,
            "estimated_titer": theoretical_yield * 0.8,  # 考虑实际损失
            "estimated_productivity": theoretical_yield * 0.6
        }
    
    def _calculate_theoretical_yield(self, pathway: Dict[str, Any]) -> float:
        """计算理论产率"""
        # 简化的产率计算
        base_yield = 0.9
        length_penalty = pathway["length"] * 0.05
        return max(base_yield - length_penalty, 0.1)
    
    def _calculate_energy_balance(self, pathway: Dict[str, Any]) -> float:
        """计算能量平衡"""
        total_energy = 0
        for reaction_id in pathway.get("reactions", []):
            reaction = self.reaction_database.get(reaction_id, {})
            total_energy += reaction.get("energy_yield", 0)
        
        # 归一化到0-1范围
        return max(0, min(1, (total_energy + 10) / 20))
    
    def _evaluate_complexity(self, pathway: Dict[str, Any]) -> float:
        """评估途径复杂度"""
        length = pathway["length"]
        # 长度越短，复杂度评分越高
        return max(0, 1 - length * 0.1)
    
    def _evaluate_host_compatibility(self, pathway: Dict[str, Any], host: str) -> float:
        """评估宿主兼容性"""
        host_chars = self.host_characteristics.get(host, {})
        
        # 基于宿主特性评估兼容性
        compatibility = 0.8  # 基础兼容性
        
        if pathway.get("type") == "fermentation" and host == "S.cerevisiae":
            compatibility += 0.2  # 酵母适合发酵
        elif pathway.get("type") == "aerobic" and host == "E.coli":
            compatibility += 0.1  # 大肠杆菌适合好氧过程
        
        return min(compatibility, 1.0)

class FluxAnalysisService:
    """通量分析服务"""
    
    async def analyze_flux(self, request: FluxAnalysisRequest) -> Dict[str, Any]:
        """进行通量分析"""
        try:
            # 构建代谢网络
            network = self._build_metabolic_network(request.pathway_model)
            
            # 进行通量平衡分析
            flux_distribution = self._flux_balance_analysis(network, request)
            
            # 识别限速步骤
            bottlenecks = self._identify_bottlenecks(flux_distribution)
            
            # 计算代谢控制系数
            control_coefficients = self._calculate_control_coefficients(flux_distribution)
            
            return {
                "flux_distribution": flux_distribution,
                "bottlenecks": bottlenecks,
                "control_coefficients": control_coefficients,
                "objective_value": flux_distribution.get("objective_flux", 0),
                "analysis_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"通量分析失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _build_metabolic_network(self, pathway_model: Dict[str, Any]) -> nx.DiGraph:
        """构建代谢网络"""
        G = nx.DiGraph()
        
        # 添加代谢物节点
        metabolites = pathway_model.get("metabolites", [])
        for metabolite in metabolites:
            G.add_node(metabolite, type="metabolite")
        
        # 添加反应节点和边
        reactions = pathway_model.get("reactions", [])
        for reaction in reactions:
            reaction_id = reaction["id"]
            G.add_node(reaction_id, type="reaction")
            
            # 添加底物到反应的边
            for substrate in reaction.get("substrates", []):
                G.add_edge(substrate, reaction_id)
            
            # 添加反应到产物的边
            for product in reaction.get("products", []):
                G.add_edge(reaction_id, product)
        
        return G
    
    def _flux_balance_analysis(self, network: nx.DiGraph, request: FluxAnalysisRequest) -> Dict[str, float]:
        """通量平衡分析"""
        # 简化的FBA实现
        flux_distribution = {}
        
        # 模拟通量分布
        reactions = [n for n in network.nodes() if network.nodes[n].get("type") == "reaction"]
        
        for reaction in reactions:
            # 随机生成通量值（实际应用中需要线性规划求解）
            flux_distribution[reaction] = np.random.uniform(0, 10)
        
        # 设置目标通量
        objective_reaction = request.objective_function
        if objective_reaction in flux_distribution:
            flux_distribution["objective_flux"] = flux_distribution[objective_reaction]
        
        return flux_distribution
    
    def _identify_bottlenecks(self, flux_distribution: Dict[str, float]) -> List[Dict[str, Any]]:
        """识别限速步骤"""
        bottlenecks = []
        
        # 找出通量最低的反应
        sorted_fluxes = sorted(flux_distribution.items(), key=lambda x: x[1])
        
        for reaction, flux in sorted_fluxes[:3]:  # 前3个最低通量
            if reaction != "objective_flux":
                bottlenecks.append({
                    "reaction": reaction,
                    "flux": flux,
                    "bottleneck_severity": "high" if flux < 1.0 else "medium"
                })
        
        return bottlenecks
    
    def _calculate_control_coefficients(self, flux_distribution: Dict[str, float]) -> Dict[str, float]:
        """计算代谢控制系数"""
        control_coefficients = {}
        
        objective_flux = flux_distribution.get("objective_flux", 1.0)
        
        for reaction, flux in flux_distribution.items():
            if reaction != "objective_flux":
                # 简化的控制系数计算
                control_coefficients[reaction] = flux / objective_flux if objective_flux > 0 else 0
        
        return control_coefficients

class StrainDesignService:
    """菌株设计服务"""
    
    async def design_strain(self, request: StrainDesignRequest) -> Dict[str, Any]:
        """设计工程菌株"""
        try:
            # 分析目标途径
            pathway_analysis = self._analyze_target_pathway(request.target_pathway)
            
            # 设计遗传修饰策略
            genetic_modifications = self._design_genetic_modifications(request)
            
            # 优化表达水平
            expression_optimization = self._optimize_expression_levels(request)
            
            # 预测菌株性能
            performance_prediction = self._predict_strain_performance(request, genetic_modifications)
            
            return {
                "target_pathway": request.target_pathway,
                "host_strain": request.host_strain,
                "pathway_analysis": pathway_analysis,
                "genetic_modifications": genetic_modifications,
                "expression_optimization": expression_optimization,
                "performance_prediction": performance_prediction,
                "design_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"菌株设计失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _analyze_target_pathway(self, pathway: Dict[str, Any]) -> Dict[str, Any]:
        """分析目标途径"""
        return {
            "pathway_length": len(pathway.get("reactions", [])),
            "complexity": "medium",
            "energy_requirement": "moderate",
            "cofactor_balance": "balanced"
        }
    
    def _design_genetic_modifications(self, request: StrainDesignRequest) -> List[Dict[str, Any]]:
        """设计遗传修饰"""
        modifications = []
        
        # 基因过表达
        modifications.append({
            "type": "overexpression",
            "target": "target_enzyme_1",
            "method": "strong_promoter",
            "expected_effect": "increased_flux"
        })
        
        # 基因敲除
        modifications.append({
            "type": "knockout",
            "target": "competing_pathway_gene",
            "method": "CRISPR",
            "expected_effect": "reduced_byproduct"
        })
        
        # 代谢工程
        modifications.append({
            "type": "pathway_introduction",
            "target": "heterologous_pathway",
            "method": "plasmid_transformation",
            "expected_effect": "new_functionality"
        })
        
        return modifications
    
    def _optimize_expression_levels(self, request: StrainDesignRequest) -> Dict[str, Any]:
        """优化表达水平"""
        return {
            "promoter_selection": {
                "enzyme_1": "strong_promoter",
                "enzyme_2": "medium_promoter",
                "enzyme_3": "weak_promoter"
            },
            "copy_number": {
                "plasmid_1": "high_copy",
                "plasmid_2": "medium_copy"
            },
            "ribosome_binding_sites": {
                "optimized_rbs_1": "high_translation",
                "optimized_rbs_2": "medium_translation"
            }
        }
    
    def _predict_strain_performance(self, request: StrainDesignRequest, modifications: List[Dict[str, Any]]) -> Dict[str, Any]:
        """预测菌株性能"""
        # 基于修饰预测性能
        base_yield = 0.5
        base_productivity = 1.0
        base_titer = 5.0
        
        # 根据修饰调整预测值
        for mod in modifications:
            if mod["type"] == "overexpression":
                base_yield += 0.1
                base_productivity += 0.2
            elif mod["type"] == "knockout":
                base_yield += 0.05
                base_titer += 1.0
        
        return {
            "predicted_yield": min(base_yield, 0.9),
            "predicted_productivity": base_productivity,
            "predicted_titer": base_titer,
            "growth_rate": 0.6,
            "stability": "high",
            "confidence": 0.75
        }

# 初始化服务
pathway_design_service = PathwayDesignService()
flux_analysis_service = FluxAnalysisService()
strain_design_service = StrainDesignService()

# 启动事件
@app.on_event("startup")
async def startup_event():
    global redis_client, db_pool
    
    # 初始化Redis
    redis_client = redis.from_url(REDIS_URL)
    
    # 初始化数据库连接池
    db_pool = await asyncpg.create_pool(DATABASE_URL)
    
    logger.info("🚀 Metabolic AI服务启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    global redis_client, db_pool
    
    if redis_client:
        await redis_client.close()
    
    if db_pool:
        await db_pool.close()
    
    logger.info("👋 Metabolic AI服务关闭")

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "metabolic-ai",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0"
    }

@app.post("/api/v1/design-pathway")
async def design_pathway(request: PathwayDesignRequest):
    """设计代谢途径"""
    result = await pathway_design_service.design_pathway(request)
    
    # 更新统计
    await redis_client.incr("stats:total_pathway_designs")
    
    return JSONResponse(content=result)

@app.post("/api/v1/analyze-flux")
async def analyze_flux(request: FluxAnalysisRequest):
    """进行通量分析"""
    result = await flux_analysis_service.analyze_flux(request)
    
    # 更新统计
    await redis_client.incr("stats:total_flux_analyses")
    
    return JSONResponse(content=result)

@app.post("/api/v1/design-strain")
async def design_strain(request: StrainDesignRequest):
    """设计工程菌株"""
    result = await strain_design_service.design_strain(request)
    
    # 更新统计
    await redis_client.incr("stats:total_strain_designs")
    
    return JSONResponse(content=result)

@app.get("/api/v1/stats")
async def get_stats():
    """获取统计信息"""
    try:
        stats = {
            "total_pathway_designs": int(await redis_client.get("stats:total_pathway_designs") or 0),
            "total_flux_analyses": int(await redis_client.get("stats:total_flux_analyses") or 0),
            "total_strain_designs": int(await redis_client.get("stats:total_strain_designs") or 0),
            "success_rate": 0.88,  # 示例数据
            "avg_design_time": 3.2  # 示例数据
        }
        
        return JSONResponse(content={"stats": stats})
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9006)
