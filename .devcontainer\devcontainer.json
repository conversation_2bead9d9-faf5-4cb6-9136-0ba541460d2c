{
  "name": "生科云码平台开发环境",
  "dockerComposeFile": "docker-compose.yml",
  "service": "workspace",
  "workspaceFolder": "/workspace",
  "shutdownAction": "stopCompose",
  
  "features": {
    "ghcr.io/devcontainers/features/common-utils:2": {
      "installZsh": true,
      "configureZshAsDefaultShell": true,
      "installOhMyZsh": true,
      "upgradePackages": true,
      "username": "vscode",
      "userUid": "automatic",
      "userGid": "automatic"
    },
    "ghcr.io/devcontainers/features/node:1": {
      "nodeGypDependencies": true,
      "version": "18",
      "nvmVersion": "latest"
    },
    "ghcr.io/devcontainers/features/go:1": {
      "version": "1.21"
    },
    "ghcr.io/devcontainers/features/python:1": {
      "version": "3.11",
      "installTools": true,
      "optimize": true
    },
    "ghcr.io/devcontainers/features/docker-in-docker:2": {
      "version": "latest",
      "enableNonRootDocker": true,
      "moby": true
    },
    "ghcr.io/devcontainers/features/kubectl-helm-minikube:1": {
      "version": "latest",
      "helm": "latest",
      "minikube": "latest"
    },
    "ghcr.io/devcontainers/features/git:1": {
      "ppa": true,
      "version": "latest"
    }
  },

  "customizations": {
    "vscode": {
      "extensions": [
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-python.python",
        "ms-python.pylint",
        "ms-python.black-formatter",
        "golang.go",
        "ms-kubernetes-tools.vscode-kubernetes-tools",
        "ms-azuretools.vscode-docker",
        "redhat.vscode-yaml",
        "ms-vscode.vscode-json",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",
        "ms-vscode.vscode-thunder-client",
        "humao.rest-client",
        "ms-vscode-remote.remote-containers",
        "github.copilot",
        "github.copilot-chat",
        "ms-vscode.hexeditor",
        "ms-toolsai.jupyter",
        "ms-python.isort",
        "ms-python.flake8",
        "charliermarsh.ruff",
        "tamasfe.even-better-toml",
        "redhat.vscode-xml",
        "ms-vscode.makefile-tools"
      ],
      "settings": {
        "terminal.integrated.defaultProfile.linux": "zsh",
        "terminal.integrated.profiles.linux": {
          "zsh": {
            "path": "/bin/zsh"
          }
        },
        "python.defaultInterpreterPath": "/usr/local/bin/python",
        "python.linting.enabled": true,
        "python.linting.pylintEnabled": true,
        "python.formatting.provider": "black",
        "python.formatting.blackArgs": ["--line-length=88"],
        "go.toolsManagement.checkForUpdates": "local",
        "go.useLanguageServer": true,
        "go.gopath": "/go",
        "go.goroot": "/usr/local/go",
        "typescript.preferences.quoteStyle": "single",
        "javascript.preferences.quoteStyle": "single",
        "prettier.singleQuote": true,
        "prettier.semi": false,
        "prettier.trailingComma": "es5",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": true,
          "source.organizeImports": true
        },
        "files.associations": {
          "*.yaml": "yaml",
          "*.yml": "yaml",
          "Dockerfile*": "dockerfile",
          "docker-compose*.yml": "dockercompose",
          "docker-compose*.yaml": "dockercompose"
        },
        "yaml.schemas": {
          "https://json.schemastore.org/github-workflow.json": "/.github/workflows/*.{yml,yaml}",
          "https://json.schemastore.org/docker-compose.json": "/docker-compose*.{yml,yaml}",
          "https://raw.githubusercontent.com/instrumenta/kubernetes-json-schema/master/v1.18.0-standalone-strict/all.json": "/k8s/**/*.{yml,yaml}"
        },
        "kubernetes.kubectlPath": "/usr/local/bin/kubectl",
        "kubernetes.helmPath": "/usr/local/bin/helm"
      }
    }
  },

  "forwardPorts": [
    3000,  // 主站门户
    3001,  // 资讯处理AI
    3002,  // 文献阅读AI
    8001,  // 认证服务
    8002,  // 订阅服务
    9001,  // 资讯处理AI后端
    9002,  // 文献阅读AI后端
    9003,  // 引物AI后端
    9004,  // 蛋白质设计AI后端
    9005,  // 基因编辑AI后端
    9006,  // 代谢工程AI后端
    5432,  // PostgreSQL
    6379,  // Redis
    80,    // Nginx
    443    // Nginx HTTPS
  ],

  "portsAttributes": {
    "3000": {
      "label": "主站门户",
      "onAutoForward": "notify"
    },
    "3001": {
      "label": "资讯处理AI",
      "onAutoForward": "notify"
    },
    "3002": {
      "label": "文献阅读AI",
      "onAutoForward": "notify"
    },
    "8001": {
      "label": "认证服务API",
      "onAutoForward": "silent"
    },
    "8002": {
      "label": "订阅服务API",
      "onAutoForward": "silent"
    },
    "9001": {
      "label": "资讯处理AI API",
      "onAutoForward": "silent"
    },
    "5432": {
      "label": "PostgreSQL",
      "onAutoForward": "silent"
    },
    "6379": {
      "label": "Redis",
      "onAutoForward": "silent"
    }
  },

  "postCreateCommand": "bash .devcontainer/setup.sh",
  "postStartCommand": "bash .devcontainer/start-services.sh",

  "remoteUser": "vscode",
  "containerUser": "vscode",

  "mounts": [
    "source=/var/run/docker.sock,target=/var/run/docker-host.sock,type=bind",
    "source=${localWorkspaceFolder}/.devcontainer/data,target=/workspace/.devcontainer/data,type=bind"
  ]
}
