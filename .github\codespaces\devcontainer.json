{"name": "生科云码平台 - GitHub Codespaces", "image": "mcr.microsoft.com/devcontainers/universal:2-linux", "features": {"ghcr.io/devcontainers/features/docker-in-docker:2": {}, "ghcr.io/devcontainers/features/kubectl-helm-minikube:1": {}, "ghcr.io/devcontainers/features/go:1": {"version": "1.21"}, "ghcr.io/devcontainers/features/python:1": {"version": "3.11"}, "ghcr.io/devcontainers/features/node:1": {"version": "18"}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-python.python", "golang.go", "ms-kubernetes-tools.vscode-kubernetes-tools", "ms-azuretools.vscode-docker", "github.copilot"]}}, "forwardPorts": [3000, 3001, 3002, 8001, 8002, 9001, 9002, 9003, 9004, 9005, 9006], "postCreateCommand": "npm install -g @devcontainers/cli && echo '🎉 生科云码平台 Codespace 准备就绪！'", "remoteUser": "codespace"}