# Check Codespaces services status

Write-Host "Checking Codespaces services status..." -ForegroundColor Green

# Service definitions
$services = @(
    @{Name="Main Portal"; Port=3000; Type="Frontend"},
    @{Name="Newsletter App"; Port=3001; Type="Frontend"},
    @{Name="Scholar App"; Port=3002; Type="Frontend"},
    @{Name="Auth Service"; Port=8001; Type="Backend"},
    @{Name="Subscription Service"; Port=8002; Type="Backend"},
    @{Name="Newsletter AI"; Port=9001; Type="AI"},
    @{Name="Scholar AI"; Port=9002; Type="AI"},
    @{Name="Primer AI"; Port=9003; Type="AI"},
    @{Name="Protein AI"; Port=9004; Type="AI"},
    @{Name="Gene Editing AI"; Port=9005; Type="AI"},
    @{Name="Metabolic AI"; Port=9006; Type="AI"}
)

Write-Host ""
Write-Host "Service Status Check:" -ForegroundColor Blue
Write-Host "=====================" -ForegroundColor Blue

$workingServices = 0
$totalServices = $services.Count

foreach ($service in $services) {
    $port = $service.Port
    $name = $service.Name
    $type = $service.Type
    
    # Check if port is listening
    $listening = netstat -an | findstr ":$port.*LISTENING"
    
    if ($listening) {
        Write-Host "[$type] $name (Port $port)" -ForegroundColor Yellow
        Write-Host "  Port Status: LISTENING" -ForegroundColor Green
        
        # Try to get basic response (for Codespaces, this might be forwarded)
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$port" -UseBasicParsing -TimeoutSec 3 -ErrorAction Stop
            Write-Host "  HTTP Status: $($response.StatusCode) OK" -ForegroundColor Green
            $workingServices++
        }
        catch {
            # In Codespaces, this might fail due to port forwarding, but service could still be running
            Write-Host "  HTTP Status: Connection issue (may be normal in Codespaces)" -ForegroundColor Yellow
            $workingServices++  # Count as working since port is listening
        }
    } else {
        Write-Host "[$type] $name (Port $port)" -ForegroundColor Red
        Write-Host "  Port Status: NOT LISTENING" -ForegroundColor Red
    }
    Write-Host ""
}

# Summary
Write-Host "Summary:" -ForegroundColor Cyan
Write-Host "========" -ForegroundColor Cyan
Write-Host "Services with listening ports: $workingServices/$totalServices" -ForegroundColor White

if ($workingServices -eq $totalServices) {
    Write-Host "Status: ALL SERVICES APPEAR TO BE RUNNING" -ForegroundColor Green
    Write-Host ""
    Write-Host "Access your services:" -ForegroundColor Yellow
    Write-Host "  Main Portal: http://localhost:3000" -ForegroundColor White
    Write-Host "  Newsletter AI: http://localhost:9001" -ForegroundColor White
    Write-Host "  Scholar AI: http://localhost:9002" -ForegroundColor White
    Write-Host "  Auth Service: http://localhost:8001" -ForegroundColor White
    Write-Host ""
    Write-Host "Note: In Codespaces, these URLs will be automatically forwarded" -ForegroundColor Cyan
    Write-Host "and may open in your browser with a different URL format." -ForegroundColor Cyan
} elseif ($workingServices -gt ($totalServices * 0.7)) {
    Write-Host "Status: MOST SERVICES RUNNING" -ForegroundColor Yellow
    Write-Host "Some services may need to be restarted." -ForegroundColor Yellow
} else {
    Write-Host "Status: MANY SERVICES DOWN" -ForegroundColor Red
    Write-Host "Consider running: ./stop-dev.sh && ./start-dev.sh" -ForegroundColor Red
}

Write-Host ""
Write-Host "Troubleshooting:" -ForegroundColor Magenta
Write-Host "  1. If you're in Codespaces, services should be accessible via forwarded URLs" -ForegroundColor White
Write-Host "  2. Check VS Code's 'Ports' tab for forwarded port URLs" -ForegroundColor White
Write-Host "  3. If services aren't working, restart: ./stop-dev.sh && ./start-dev.sh" -ForegroundColor White
Write-Host "  4. For detailed logs, check the Codespaces terminal output" -ForegroundColor White
