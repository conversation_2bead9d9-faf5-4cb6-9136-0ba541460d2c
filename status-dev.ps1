# 生科云码平台开发环境状态检查脚本 (PowerShell版本)
# 检查所有服务的运行状态

Write-Host "🔍 检查生科云码平台服务状态..." -ForegroundColor Green

# 定义服务配置
$baseServices = @(
    @{Port=5432; Name="PostgreSQL"; Type="基础服务"},
    @{Port=6379; Name="Redis"; Type="基础服务"},
    @{Port=1025; Name="MailHog SMTP"; Type="基础服务"},
    @{Port=8025; Name="MailHog Web"; Type="基础服务"},
    @{Port=9000; Name="MinIO API"; Type="基础服务"},
    @{Port=9090; Name="MinIO Console"; Type="基础服务"}
)

$backendServices = @(
    @{Port=8001; Name="认证服务"; Type="后端服务"},
    @{Port=8002; Name="订阅服务"; Type="后端服务"}
)

$aiServices = @(
    @{Port=9001; Name="资讯处理AI"; Type="AI服务"},
    @{Port=9002; Name="文献阅读AI"; Type="AI服务"},
    @{Port=9003; Name="引物AI"; Type="AI服务"},
    @{Port=9004; Name="蛋白质设计AI"; Type="AI服务"},
    @{Port=9005; Name="基因编辑AI"; Type="AI服务"},
    @{Port=9006; Name="代谢工程AI"; Type="AI服务"}
)

$frontendServices = @(
    @{Port=3000; Name="主站门户"; Type="前端应用"},
    @{Port=3001; Name="资讯处理AI应用"; Type="前端应用"},
    @{Port=3002; Name="文献阅读AI应用"; Type="前端应用"}
)

$monitoringServices = @(
    @{Port=9091; Name="Prometheus"; Type="监控服务"},
    @{Port=3030; Name="Grafana"; Type="监控服务"}
)

# 检查端口函数
function Test-Port {
    param($Port, $ServiceName)
    
    try {
        $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        if ($connections) {
            $processId = $connections[0].OwningProcess
            Write-Host "  ✅ $ServiceName (端口 $Port) - 运行中 (PID: $processId)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ❌ $ServiceName (端口 $Port) - 未运行" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ❌ $ServiceName (端口 $Port) - 检查失败" -ForegroundColor Red
        return $false
    }
}

# 检查HTTP服务函数
function Test-HttpService {
    param($Url, $ServiceName)
    
    try {
        $response = Invoke-WebRequest -Uri $Url -Method GET -TimeoutSec 5 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "  ✅ $ServiceName - HTTP响应正常" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ❌ $ServiceName - HTTP响应异常 ($($response.StatusCode))" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  ❌ $ServiceName - HTTP无响应" -ForegroundColor Red
        return $false
    }
}

# 检查基础服务
Write-Host ""
Write-Host "🗄️ 基础服务状态:" -ForegroundColor Blue
$baseRunning = 0
foreach ($service in $baseServices) {
    if (Test-Port -Port $service.Port -ServiceName $service.Name) {
        $baseRunning++
    }
}
Write-Host "  📊 基础服务: $baseRunning/$($baseServices.Count) 运行中" -ForegroundColor Cyan

# 检查后端服务
Write-Host ""
Write-Host "⚙️ 后端服务状态:" -ForegroundColor Blue
$backendRunning = 0
foreach ($service in $backendServices) {
    if (Test-Port -Port $service.Port -ServiceName $service.Name) {
        $backendRunning++
        # 检查健康端点
        Test-HttpService -Url "http://localhost:$($service.Port)/health" -ServiceName "$($service.Name) API"
    }
}
Write-Host "  📊 后端服务: $backendRunning/$($backendServices.Count) 运行中" -ForegroundColor Cyan

# 检查AI服务
Write-Host ""
Write-Host "🤖 AI服务状态:" -ForegroundColor Blue
$aiRunning = 0
foreach ($service in $aiServices) {
    if (Test-Port -Port $service.Port -ServiceName $service.Name) {
        $aiRunning++
        # 检查健康端点
        Test-HttpService -Url "http://localhost:$($service.Port)/health" -ServiceName "$($service.Name) API"
    }
}
Write-Host "  📊 AI服务: $aiRunning/$($aiServices.Count) 运行中" -ForegroundColor Cyan

# 检查前端应用
Write-Host ""
Write-Host "🌐 前端应用状态:" -ForegroundColor Blue
$frontendRunning = 0
foreach ($service in $frontendServices) {
    if (Test-Port -Port $service.Port -ServiceName $service.Name) {
        $frontendRunning++
        # 检查前端页面
        Test-HttpService -Url "http://localhost:$($service.Port)" -ServiceName $service.Name
    }
}
Write-Host "  📊 前端应用: $frontendRunning/$($frontendServices.Count) 运行中" -ForegroundColor Cyan

# 检查监控服务
Write-Host ""
Write-Host "📊 监控服务状态:" -ForegroundColor Blue
$monitoringRunning = 0
foreach ($service in $monitoringServices) {
    if (Test-Port -Port $service.Port -ServiceName $service.Name) {
        $monitoringRunning++
        # 检查监控页面
        Test-HttpService -Url "http://localhost:$($service.Port)" -ServiceName $service.Name
    }
}
Write-Host "  📊 监控服务: $monitoringRunning/$($monitoringServices.Count) 运行中" -ForegroundColor Cyan

# 检查PID文件
Write-Host ""
Write-Host "📁 PID文件状态:" -ForegroundColor Blue
if (Test-Path ".pids") {
    $pidFiles = Get-ChildItem ".pids\*.pid" -ErrorAction SilentlyContinue
    if ($pidFiles) {
        $activeCount = 0
        foreach ($pidFile in $pidFiles) {
            $serviceName = [System.IO.Path]::GetFileNameWithoutExtension($pidFile.Name)
            try {
                $pid = Get-Content $pidFile.FullName
                $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                if ($process) {
                    Write-Host "  ✅ $serviceName (PID: $pid) - 活跃" -ForegroundColor Green
                    $activeCount++
                } else {
                    Write-Host "  ❌ $serviceName (PID: $pid) - 已停止" -ForegroundColor Red
                }
            }
            catch {
                Write-Host "  ❌ $serviceName - PID文件读取失败" -ForegroundColor Red
            }
        }
        Write-Host "  📊 PID文件: $activeCount/$($pidFiles.Count) 活跃" -ForegroundColor Cyan
    } else {
        Write-Host "  ℹ️ 没有PID文件" -ForegroundColor Gray
    }
} else {
    Write-Host "  ℹ️ PID目录不存在" -ForegroundColor Gray
}

# 显示访问地址
Write-Host ""
Write-Host "🌐 访问地址:" -ForegroundColor Yellow
Write-Host "  📱 前端应用:" -ForegroundColor Cyan
Write-Host "    🏠 主站门户:     http://localhost:3000"
Write-Host "    📰 资讯处理AI:   http://localhost:3001"
Write-Host "    📚 文献阅读AI:   http://localhost:3002"
Write-Host ""
Write-Host "  🔧 API服务:" -ForegroundColor Cyan
Write-Host "    🔐 认证服务:     http://localhost:8001"
Write-Host "    💳 订阅服务:     http://localhost:8002"
Write-Host "    🤖 资讯AI:       http://localhost:9001"
Write-Host "    📖 文献AI:       http://localhost:9002"
Write-Host "    🧬 引物AI:       http://localhost:9003"
Write-Host "    🔬 蛋白质AI:     http://localhost:9004"
Write-Host "    ✂️ 基因编辑AI:   http://localhost:9005"
Write-Host "    ⚗️ 代谢工程AI:   http://localhost:9006"
Write-Host ""
Write-Host "  🛠️ 开发工具:" -ForegroundColor Cyan
Write-Host "    📧 MailHog:      http://localhost:8025"
Write-Host "    💾 MinIO:        http://localhost:9090"
Write-Host "    📊 Prometheus:   http://localhost:9091"
Write-Host "    📈 Grafana:      http://localhost:3030"

# 显示总结
$totalAppServices = $backendServices.Count + $aiServices.Count + $frontendServices.Count
$totalAppRunning = $backendRunning + $aiRunning + $frontendRunning

Write-Host ""
Write-Host "📋 状态总结:" -ForegroundColor Yellow
Write-Host "  🗄️ 基础服务: $baseRunning/$($baseServices.Count)" -ForegroundColor White
Write-Host "  ⚙️ 后端服务: $backendRunning/$($backendServices.Count)" -ForegroundColor White
Write-Host "  🤖 AI服务: $aiRunning/$($aiServices.Count)" -ForegroundColor White
Write-Host "  🌐 前端应用: $frontendRunning/$($frontendServices.Count)" -ForegroundColor White
Write-Host "  📊 监控服务: $monitoringRunning/$($monitoringServices.Count)" -ForegroundColor White
Write-Host "  📊 应用服务总计: $totalAppRunning/$totalAppServices" -ForegroundColor White

if ($totalAppRunning -eq $totalAppServices) {
    Write-Host ""
    Write-Host "🎉 所有应用服务运行正常！" -ForegroundColor Green
} elseif ($totalAppRunning -gt 0) {
    Write-Host ""
    Write-Host "⚠️ 部分服务未运行，请检查配置" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "❌ 没有应用服务在运行" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 启动服务: .\start-dev.ps1" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "⚡ 快捷命令:" -ForegroundColor Yellow
Write-Host "  启动服务: .\start-dev.ps1"
Write-Host "  停止服务: .\stop-dev.ps1"
Write-Host "  运行测试: .\test-dev.ps1"
Write-Host ""

Read-Host "按回车键关闭"
