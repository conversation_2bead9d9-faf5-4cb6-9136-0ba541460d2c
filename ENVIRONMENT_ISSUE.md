# 🚨 环境问题诊断和解决

## 🔍 当前问题分析

根据您提供的信息，我发现了问题所在：

### 问题描述
- ✅ **Docker 容器正在运行** - 所有服务包括 PostgreSQL 都显示为 "healthy"
- ❌ **环境混乱** - 您在 Windows 本地环境中运行脚本，但 Docker 容器在 Codespaces 中
- ❌ **连接失败** - Windows 环境无法连接到 Codespaces 中的 PostgreSQL

### 容器状态（正常）
```
fa00aa75ba18   postgres:15-alpine   Up 14 minutes (healthy)   0.0.0.0:5432->5432/tcp   biocloude_devcontainer-postgres-1
```

## 🎯 解决方案

### 方案1: 在 Codespaces 中正确运行 (推荐) 🌟

**问题根源：** 您需要在 **Codespaces 的终端** 中运行脚本，而不是在本地 Windows 环境中。

#### 步骤：
1. **打开 Codespaces**
   - 访问您的 GitHub 仓库
   - 点击 Code → Codespaces
   - 选择现有的 Codespace 或创建新的

2. **在 Codespaces 终端中运行**
   ```bash
   # 确保在正确的目录
   cd /workspace
   
   # 修复脚本权限
   chmod +x *.sh
   
   # 启动服务
   ./start-dev.sh
   ```

3. **验证环境**
   ```bash
   # 检查是否在 Codespaces 中
   echo $CODESPACES
   
   # 应该输出: true
   ```

### 方案2: 使用 WSL 连接到 Codespaces

如果您想在本地使用 WSL 连接：

```bash
# 在 WSL 中
cd /mnt/c/Users/<USER>/Documents/augment-projects/biocloude

# 或者如果项目在 WSL 文件系统中
cd /workspace

# 运行脚本
./start-dev.sh
```

### 方案3: 完全本地开发

如果您想完全在本地开发：

```powershell
# 1. 停止 Codespaces 中的容器
# 2. 在本地启动 Docker 服务
.\start-docker-services.ps1

# 3. 启动应用服务
.\start-dev.ps1
```

## 🔧 快速修复

### 立即解决（推荐）

1. **打开 Codespaces 网页界面**
2. **在 Codespaces 终端中运行：**
   ```bash
   cd /workspace
   chmod +x *.sh
   ./start-dev.sh
   ```

### 或者运行修复脚本

```powershell
# 在当前 Windows 环境中运行
.\fix-codespaces-connection.ps1
```

## 📋 环境检查清单

### ✅ 确认您在正确的环境中

#### 在 Codespaces 中：
```bash
# 检查环境变量
echo $CODESPACES  # 应该是 "true"
echo $GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN  # 应该有值

# 检查路径
pwd  # 应该是 /workspace 或类似路径

# 检查 Docker
docker ps  # 应该显示容器列表
```

#### 在 Windows 本地：
```powershell
# 检查环境
$env:CODESPACES  # 应该是空的
Get-Location     # 应该是 Windows 路径

# 检查 Docker
docker ps  # 如果本地有 Docker 则显示容器
```

## 🎯 推荐的工作流程

### 🥇 最佳方案：完全使用 Codespaces

1. **访问 GitHub 仓库**
2. **创建或打开 Codespace**
3. **在 Codespaces 终端中开发**
4. **所有命令都在 Codespaces 中运行**

### 🥈 备选方案：WSL + Codespaces

1. **在 Windows 中编辑代码**
2. **在 WSL 中运行命令**
3. **连接到 Codespaces 中的服务**

### 🥉 本地方案：完全本地开发

1. **安装 Docker Desktop**
2. **使用 PowerShell 脚本**
3. **在本地运行所有服务**

## 💡 避免混乱的提示

### ✅ 推荐做法
- 🎯 **选择一种环境并坚持使用**
- 🌐 **优先使用 Codespaces**
- 📍 **确认当前环境再运行命令**
- 🔄 **不要混用不同环境的命令**

### ❌ 避免做法
- 🚫 **不要在 Windows 中运行 Linux 脚本**
- 🚫 **不要混用本地和云端环境**
- 🚫 **不要忽略环境检查**

## 🆘 仍然有问题？

### 诊断步骤
1. **确认当前环境**
2. **检查 Docker 容器状态**
3. **验证网络连接**
4. **查看详细错误信息**

### 获取帮助
1. 📖 查看 [CODESPACES_GUIDE.md](CODESPACES_GUIDE.md)
2. 🔧 运行 `.\fix-codespaces-connection.ps1`
3. 🐛 创建 GitHub Issue 并提供环境信息

---

🎯 **关键：确保在正确的环境中运行正确的脚本！**

- **Codespaces 中**: 使用 `./start-dev.sh`
- **Windows 本地**: 使用 `.\start-dev.ps1`
