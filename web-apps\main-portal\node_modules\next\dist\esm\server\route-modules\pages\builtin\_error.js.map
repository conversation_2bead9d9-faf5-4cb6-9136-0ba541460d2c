{"version": 3, "sources": ["../../../../../src/server/route-modules/pages/builtin/_error.tsx"], "sourcesContent": ["import Document from '../../../../pages/_document'\nimport App from '../../../../pages/_app'\nimport { RouteKind } from '../../../route-kind'\n\nimport * as moduleError from '../../../../pages/_error'\n\nimport PagesRouteModule from '../module'\n\nexport const routeModule = new PagesRouteModule({\n  // TODO: add descriptor for internal error page\n  definition: {\n    kind: RouteKind.PAGES,\n    page: '/_error',\n    pathname: '/_error',\n    filename: '',\n    bundlePath: '',\n  },\n  components: {\n    App,\n    Document,\n  },\n  // @ts-expect-error -- Types don't account for getInitialProps. `Error` requires to be instantiated with `statusCode` but the types currently don't guarantee that.\n  userland: moduleError,\n})\n"], "names": ["Document", "App", "RouteKind", "moduleError", "PagesRouteModule", "routeModule", "definition", "kind", "PAGES", "page", "pathname", "filename", "bundlePath", "components", "userland"], "mappings": "AAAA,OAAOA,cAAc,8BAA6B;AAClD,OAAOC,SAAS,yBAAwB;AACxC,SAASC,SAAS,QAAQ,sBAAqB;AAE/C,YAAYC,iBAAiB,2BAA0B;AAEvD,OAAOC,sBAAsB,YAAW;AAExC,OAAO,MAAMC,cAAc,IAAID,iBAAiB;IAC9C,+CAA+C;IAC/CE,YAAY;QACVC,MAAML,UAAUM,KAAK;QACrBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,YAAY;QACVZ;QACAD;IACF;IACA,mKAAmK;IACnKc,UAAUX;AACZ,GAAE"}