# 环境诊断脚本

Write-Host "🔍 环境诊断..." -ForegroundColor Green

# 检查操作系统
Write-Host ""
Write-Host "🖥️ 操作系统信息:" -ForegroundColor Blue
Write-Host "  OS: $($env:OS)"
Write-Host "  计算机名: $($env:COMPUTERNAME)"
Write-Host "  用户: $($env:USERNAME)"
Write-Host "  PowerShell 版本: $($PSVersionTable.PSVersion)"

# 检查当前路径
Write-Host ""
Write-Host "📍 路径信息:" -ForegroundColor Blue
$currentPath = Get-Location
Write-Host "  当前目录: $currentPath"
Write-Host "  项目目录存在: $(Test-Path 'services')"
Write-Host "  .devcontainer 存在: $(Test-Path '.devcontainer')"

# 检查环境变量
Write-Host ""
Write-Host "🌐 环境变量:" -ForegroundColor Blue
Write-Host "  CODESPACES: $($env:CODESPACES)"
Write-Host "  GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN: $($env:GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN)"
Write-Host "  PATH (前5个): $($env:PATH.Split(';')[0..4] -join '; ')..."

# 检查开发工具
Write-Host ""
Write-Host "🛠️ 开发工具检查:" -ForegroundColor Blue

# Python
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Python: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    try {
        $python3Version = python3 --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ Python3: $python3Version" -ForegroundColor Green
        } else {
            throw "Python3 not found"
        }
    } catch {
        Write-Host "  ❌ Python: 未安装" -ForegroundColor Red
    }
}

# Go
try {
    $goVersion = go version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Go: $goVersion" -ForegroundColor Green
    } else {
        throw "Go not found"
    }
} catch {
    Write-Host "  ❌ Go: 未安装" -ForegroundColor Red
}

# Node.js
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Node.js: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Host "  ❌ Node.js: 未安装" -ForegroundColor Red
}

# npm
try {
    $npmVersion = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ npm: $npmVersion" -ForegroundColor Green
    } else {
        throw "npm not found"
    }
} catch {
    Write-Host "  ❌ npm: 未安装" -ForegroundColor Red
}

# 检查端口占用
Write-Host ""
Write-Host "🔌 端口占用检查:" -ForegroundColor Blue
$ports = @(3000, 3001, 3002, 8001, 8002, 9001, 9002, 9003, 9004, 9005, 9006)
$occupiedPorts = @()

foreach ($port in $ports) {
    $result = netstat -an | findstr ":$port"
    if ($result) {
        Write-Host "  🔴 端口 $port: 被占用" -ForegroundColor Yellow
        $occupiedPorts += $port
    } else {
        Write-Host "  🟢 端口 $port: 可用" -ForegroundColor Green
    }
}

# 检查进程
Write-Host ""
Write-Host "🔄 相关进程:" -ForegroundColor Blue
$processes = @("python", "go", "node", "npm")
foreach ($proc in $processes) {
    $running = Get-Process -Name $proc -ErrorAction SilentlyContinue
    if ($running) {
        Write-Host "  ✅ $proc: $($running.Count) 个进程运行中" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $proc: 无进程运行" -ForegroundColor Red
    }
}

# 环境判断
Write-Host ""
Write-Host "🎯 环境判断:" -ForegroundColor Cyan
Write-Host "============" -ForegroundColor Cyan

if ($env:CODESPACES -eq "true" -or $env:GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN) {
    Write-Host "  📍 当前环境: GitHub Codespaces" -ForegroundColor Green
    Write-Host "  💡 建议: 在 Codespaces 终端中运行 ./start-dev.sh" -ForegroundColor Yellow
} elseif ($currentPath -like "*workspace*" -and $currentPath -like "*biocloude*") {
    Write-Host "  📍 当前环境: 可能是 Codespaces (路径特征)" -ForegroundColor Yellow
    Write-Host "  💡 建议: 确认是否在 Codespaces 中，如果是请在 Codespaces 终端运行" -ForegroundColor Yellow
} else {
    Write-Host "  📍 当前环境: Windows 本地环境" -ForegroundColor Blue
    Write-Host "  💡 建议: 使用 GitHub Codespaces 或安装完整的开发环境" -ForegroundColor Yellow
}

# 建议操作
Write-Host ""
Write-Host "📋 建议操作:" -ForegroundColor Magenta
Write-Host "============" -ForegroundColor Magenta

if ($occupiedPorts.Count -gt 0) {
    Write-Host "  🔴 端口冲突: $($occupiedPorts.Count) 个端口被占用" -ForegroundColor Red
    Write-Host "     - 可能有服务已在运行" -ForegroundColor Yellow
    Write-Host "     - 建议: 先停止现有服务或使用不同端口" -ForegroundColor Yellow
}

$missingTools = @()
if (-not (Get-Command python -ErrorAction SilentlyContinue) -and -not (Get-Command python3 -ErrorAction SilentlyContinue)) {
    $missingTools += "Python"
}
if (-not (Get-Command go -ErrorAction SilentlyContinue)) {
    $missingTools += "Go"
}
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    $missingTools += "Node.js"
}

if ($missingTools.Count -gt 0) {
    Write-Host "  ⚠️ 缺少开发工具: $($missingTools -join ', ')" -ForegroundColor Red
    Write-Host "     - 建议: 使用 GitHub Codespaces (推荐)" -ForegroundColor Green
    Write-Host "     - 或者: 安装缺少的开发工具" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🚀 推荐方案:" -ForegroundColor Green
Write-Host "1. 使用 GitHub Codespaces (零配置)" -ForegroundColor White
Write-Host "   - 访问 GitHub 仓库" -ForegroundColor Gray
Write-Host "   - 点击 Code → Codespaces → Create codespace" -ForegroundColor Gray
Write-Host "   - 在 Codespaces 终端运行: ./start-dev.sh" -ForegroundColor Gray
Write-Host ""
Write-Host "2. 本地开发环境配置" -ForegroundColor White
Write-Host "   - 安装 Python 3.9+" -ForegroundColor Gray
Write-Host "   - 安装 Go 1.21+" -ForegroundColor Gray
Write-Host "   - 安装 Node.js 18+" -ForegroundColor Gray
Write-Host "   - 运行: .\start-dev.ps1" -ForegroundColor Gray
