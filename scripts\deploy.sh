#!/bin/bash

# 生科云码平台部署脚本
# 用于自动化部署整个平台

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl未安装，请先安装kubectl"
        exit 1
    fi
    
    # 检查helm
    if ! command -v helm &> /dev/null; then
        log_warning "Helm未安装，某些功能可能不可用"
    fi
    
    log_success "依赖检查完成"
}

# 构建Docker镜像
build_images() {
    log_info "开始构建Docker镜像..."
    
    # 构建主站门户
    log_info "构建主站门户镜像..."
    docker build -t biocloude/main-portal:latest ./web-apps/main-portal/
    
    # 构建资讯处理AI前端
    log_info "构建资讯处理AI前端镜像..."
    docker build -t biocloude/newsletter-app:latest ./web-apps/newsletter-app/
    
    # 构建认证服务
    log_info "构建认证服务镜像..."
    docker build -t biocloude/auth-service:latest ./services/auth-service/
    
    # 构建订阅服务
    log_info "构建订阅服务镜像..."
    docker build -t biocloude/subscription-service:latest ./services/subscription-service/
    
    # 构建AI服务
    log_info "构建AI服务镜像..."
    docker build -t biocloude/newsletter-ai:latest ./services/ai-services/newsletter-ai/
    
    log_success "所有镜像构建完成"
}

# 推送镜像到仓库
push_images() {
    log_info "推送镜像到Docker仓库..."
    
    # 这里应该推送到实际的Docker仓库
    # docker push biocloude/main-portal:latest
    # docker push biocloude/newsletter-app:latest
    # docker push biocloude/auth-service:latest
    # docker push biocloude/subscription-service:latest
    # docker push biocloude/newsletter-ai:latest
    
    log_success "镜像推送完成"
}

# 部署基础设施
deploy_infrastructure() {
    log_info "部署基础设施..."
    
    # 创建命名空间
    kubectl apply -f k8s/infrastructure/namespace.yaml
    
    # 部署PostgreSQL
    kubectl apply -f k8s/infrastructure/postgres.yaml
    
    # 部署Redis
    kubectl apply -f k8s/infrastructure/redis.yaml
    
    # 等待基础设施就绪
    log_info "等待基础设施就绪..."
    kubectl wait --for=condition=ready pod -l app=postgres -n biocloude --timeout=300s
    kubectl wait --for=condition=ready pod -l app=redis -n biocloude --timeout=300s
    
    log_success "基础设施部署完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 创建迁移Job
    kubectl apply -f k8s/jobs/migration-job.yaml
    
    # 等待迁移完成
    kubectl wait --for=condition=complete job/database-migration -n biocloude --timeout=300s
    
    log_success "数据库迁移完成"
}

# 部署应用服务
deploy_services() {
    log_info "部署应用服务..."
    
    # 部署认证服务
    kubectl apply -f k8s/deployments/auth-service-deployment.yaml
    
    # 部署订阅服务
    kubectl apply -f k8s/deployments/subscription-service-deployment.yaml
    
    # 部署AI服务
    kubectl apply -f k8s/deployments/newsletter-ai-deployment.yaml
    
    # 等待服务就绪
    log_info "等待后端服务就绪..."
    kubectl wait --for=condition=available deployment/auth-service -n biocloude --timeout=300s
    kubectl wait --for=condition=available deployment/subscription-service -n biocloude --timeout=300s
    kubectl wait --for=condition=available deployment/newsletter-ai -n biocloude --timeout=300s
    
    log_success "后端服务部署完成"
}

# 部署前端应用
deploy_frontend() {
    log_info "部署前端应用..."
    
    # 部署主站门户
    kubectl apply -f k8s/deployments/main-portal-deployment.yaml
    
    # 部署资讯处理AI应用
    kubectl apply -f k8s/deployments/newsletter-app-deployment.yaml
    
    # 等待前端应用就绪
    log_info "等待前端应用就绪..."
    kubectl wait --for=condition=available deployment/main-portal -n biocloude --timeout=300s
    kubectl wait --for=condition=available deployment/newsletter-app -n biocloude --timeout=300s
    
    log_success "前端应用部署完成"
}

# 配置监控
setup_monitoring() {
    log_info "配置监控系统..."
    
    # 部署Prometheus
    if command -v helm &> /dev/null; then
        helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
        helm repo update
        helm install prometheus prometheus-community/kube-prometheus-stack -n biocloude
    else
        log_warning "Helm未安装，跳过监控配置"
    fi
    
    log_success "监控系统配置完成"
}

# 运行健康检查
health_check() {
    log_info "运行健康检查..."
    
    # 检查所有Pod状态
    kubectl get pods -n biocloude
    
    # 检查服务状态
    kubectl get services -n biocloude
    
    # 检查Ingress状态
    kubectl get ingress -n biocloude
    
    log_success "健康检查完成"
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 生科云码平台部署完成！"
    echo ""
    echo "访问地址："
    echo "  主站门户: https://www.biocloude.cn"
    echo "  资讯处理AI: https://ivdnewsletter.biocloude.cn"
    echo ""
    echo "API端点："
    echo "  认证服务: https://api.biocloude.cn/auth"
    echo "  订阅服务: https://api.biocloude.cn/subscription"
    echo "  AI服务: https://api.biocloude.cn/ai"
    echo ""
    echo "管理命令："
    echo "  查看Pod状态: kubectl get pods -n biocloude"
    echo "  查看日志: kubectl logs -f deployment/<service-name> -n biocloude"
    echo "  扩容服务: kubectl scale deployment/<service-name> --replicas=<count> -n biocloude"
}

# 主函数
main() {
    log_info "🚀 开始部署生科云码平台..."
    
    # 解析命令行参数
    SKIP_BUILD=false
    SKIP_PUSH=false
    SKIP_MONITORING=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --skip-push)
                SKIP_PUSH=true
                shift
                ;;
            --skip-monitoring)
                SKIP_MONITORING=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-build      跳过镜像构建"
                echo "  --skip-push       跳过镜像推送"
                echo "  --skip-monitoring 跳过监控配置"
                echo "  -h, --help        显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_dependencies
    
    if [ "$SKIP_BUILD" = false ]; then
        build_images
    fi
    
    if [ "$SKIP_PUSH" = false ]; then
        push_images
    fi
    
    deploy_infrastructure
    run_migrations
    deploy_services
    deploy_frontend
    
    if [ "$SKIP_MONITORING" = false ]; then
        setup_monitoring
    fi
    
    health_check
    show_deployment_info
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
