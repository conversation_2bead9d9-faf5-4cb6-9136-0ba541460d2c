@echo off
REM 修复行结束符脚本 (Windows版本)
REM 将所有文本文件的行结束符统一为 LF

setlocal enabledelayedexpansion

echo 🔧 修复生科云码平台文件行结束符...

REM 检查是否安装了 Git
where git >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Git，请先安装 Git
    pause
    exit /b 1
)

REM 检查是否在 Git 仓库中
git rev-parse --git-dir >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 当前目录不是 Git 仓库
    pause
    exit /b 1
)

echo 📋 配置 Git 行结束符处理...

REM 配置 Git
git config core.autocrlf false
git config core.eol lf

echo ✅ Git 配置已更新

echo 📁 添加 .gitattributes 文件...

REM 添加 .gitattributes 文件
git add .gitattributes

echo 🔄 重新规范化所有文件...

REM 重新规范化所有文件
git add --renormalize .

echo ✅ 文件重新规范化完成

echo.
echo 🎉 行结束符修复完成！
echo.
echo 📋 后续步骤：
echo 1. 检查修改: git status
echo 2. 提交更改: git commit -m "fix: 统一行结束符为 LF"
echo 3. 推送更改: git push
echo.
echo 💡 提示: 现在所有新文件都会自动使用正确的行结束符
echo.

pause
