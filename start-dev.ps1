# 生科云码平台开发环境启动脚本 (PowerShell版本)
# 启动所有前端和后端应用服务

param(
    [switch]$Help
)

if ($Help) {
    Write-Host "生科云码平台开发环境启动脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法:"
    Write-Host "  .\start-dev.ps1        启动所有服务"
    Write-Host "  .\start-dev.ps1 -Help  显示帮助"
    Write-Host ""
    Write-Host "启动的服务:"
    Write-Host "  - 后端服务: 认证服务(8001), 订阅服务(8002)"
    Write-Host "  - AI服务: 6个AI微服务(9001-9006)"
    Write-Host "  - 前端应用: 3个Web应用(3000-3002)"
    exit 0
}

Write-Host "🚀 启动生科云码平台开发环境..." -ForegroundColor Green

# 检查基础服务
Write-Host "📋 检查基础服务状态..." -ForegroundColor Blue

# 检查PostgreSQL
$pgRunning = Get-NetTCPConnection -LocalPort 5432 -ErrorAction SilentlyContinue
if (-not $pgRunning) {
    Write-Host "❌ PostgreSQL 未运行，请先启动基础服务" -ForegroundColor Red
    Write-Host "💡 在 Codespaces 中，基础服务应该自动启动" -ForegroundColor Yellow
    Read-Host "按回车键继续"
    exit 1
}
Write-Host "✅ PostgreSQL 运行正常" -ForegroundColor Green

# 检查Redis
$redisRunning = Get-NetTCPConnection -LocalPort 6379 -ErrorAction SilentlyContinue
if (-not $redisRunning) {
    Write-Host "⚠️ Redis 未运行，某些功能可能受限" -ForegroundColor Yellow
} else {
    Write-Host "✅ Redis 运行正常" -ForegroundColor Green
}

# 创建PID目录
if (-not (Test-Path ".pids")) {
    New-Item -ItemType Directory -Path ".pids" | Out-Null
}

Write-Host "📦 检查并安装依赖..." -ForegroundColor Blue

# 检查Go服务依赖
if (Test-Path "services\auth-service") {
    Write-Host "🔧 检查认证服务依赖..." -ForegroundColor Cyan
    Push-Location "services\auth-service"
    if (-not (Test-Path "vendor") -and (Test-Path "go.mod")) {
        Write-Host "安装认证服务依赖..." -ForegroundColor Yellow
        go mod download
        go mod tidy
    }
    Pop-Location
}

if (Test-Path "services\subscription-service") {
    Write-Host "🔧 检查订阅服务依赖..." -ForegroundColor Cyan
    Push-Location "services\subscription-service"
    if (-not (Test-Path "vendor") -and (Test-Path "go.mod")) {
        Write-Host "安装订阅服务依赖..." -ForegroundColor Yellow
        go mod download
        go mod tidy
    }
    Pop-Location
}

# 检查前端依赖
$frontendApps = @("main-portal", "newsletter-app", "scholar-app")
foreach ($app in $frontendApps) {
    if (Test-Path "web-apps\$app") {
        Write-Host "🌐 检查 $app 依赖..." -ForegroundColor Cyan
        Push-Location "web-apps\$app"
        if (-not (Test-Path "node_modules") -and (Test-Path "package.json")) {
            Write-Host "安装 $app 依赖..." -ForegroundColor Yellow
            npm install
        }
        Pop-Location
    }
}

Write-Host "🔧 启动后端服务..." -ForegroundColor Blue

# 启动认证服务
if (Test-Path "services\auth-service") {
    Write-Host "启动认证服务 (端口 8001)..." -ForegroundColor Cyan
    Push-Location "services\auth-service"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "go run main.go" -WindowStyle Normal
    Pop-Location
    Start-Sleep -Seconds 2
}

# 启动订阅服务
if (Test-Path "services\subscription-service") {
    Write-Host "启动订阅服务 (端口 8002)..." -ForegroundColor Cyan
    Push-Location "services\subscription-service"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "go run main.go" -WindowStyle Normal
    Pop-Location
    Start-Sleep -Seconds 2
}

Write-Host "🤖 启动AI服务..." -ForegroundColor Blue

# 启动AI服务
$aiServices = @(
    @{Name="newsletter-ai"; Port=9001},
    @{Name="scholar-ai"; Port=9002},
    @{Name="primer-ai"; Port=9003},
    @{Name="protein-ai"; Port=9004},
    @{Name="gene-editing-ai"; Port=9005},
    @{Name="metabolic-ai"; Port=9006}
)

foreach ($service in $aiServices) {
    if (Test-Path "services\ai-services\$($service.Name)") {
        Write-Host "启动 $($service.Name) (端口 $($service.Port))..." -ForegroundColor Cyan
        Push-Location "services\ai-services\$($service.Name)"
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "python main.py" -WindowStyle Normal
        Pop-Location
        Start-Sleep -Seconds 1
    }
}

Write-Host "🌐 启动前端应用..." -ForegroundColor Blue

# 启动前端应用
$frontendServices = @(
    @{Name="main-portal"; Port=3000; Title="主站门户"},
    @{Name="newsletter-app"; Port=3001; Title="资讯处理AI"},
    @{Name="scholar-app"; Port=3002; Title="文献阅读AI"}
)

foreach ($service in $frontendServices) {
    if (Test-Path "web-apps\$($service.Name)") {
        Write-Host "启动 $($service.Title) (端口 $($service.Port))..." -ForegroundColor Cyan
        Push-Location "web-apps\$($service.Name)"
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev" -WindowStyle Normal
        Pop-Location
        Start-Sleep -Seconds 3
    }
}

Write-Host ""
Write-Host "🎉 生科云码平台开发环境启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📱 前端应用:" -ForegroundColor Yellow
Write-Host "  🏠 主站门户:     http://localhost:3000"
Write-Host "  📰 资讯处理AI:   http://localhost:3001"
Write-Host "  📚 文献阅读AI:   http://localhost:3002"
Write-Host ""
Write-Host "🔧 API服务:" -ForegroundColor Yellow
Write-Host "  🔐 认证服务:     http://localhost:8001"
Write-Host "  💳 订阅服务:     http://localhost:8002"
Write-Host "  🤖 资讯AI:       http://localhost:9001"
Write-Host "  📖 文献AI:       http://localhost:9002"
Write-Host "  🧬 引物AI:       http://localhost:9003"
Write-Host "  🔬 蛋白质AI:     http://localhost:9004"
Write-Host "  ✂️ 基因编辑AI:   http://localhost:9005"
Write-Host "  ⚗️ 代谢工程AI:   http://localhost:9006"
Write-Host ""
Write-Host "🛠️ 开发工具:" -ForegroundColor Yellow
Write-Host "  📧 MailHog:      http://localhost:8025"
Write-Host "  💾 MinIO:        http://localhost:9090"
Write-Host "  📊 Prometheus:   http://localhost:9091"
Write-Host "  📈 Grafana:      http://localhost:3030"
Write-Host ""
Write-Host "⚡ 快捷命令:" -ForegroundColor Yellow
Write-Host "  停止所有服务:    .\stop-dev.ps1"
Write-Host "  查看服务状态:    .\status-dev.ps1"
Write-Host "  运行测试:        .\test-dev.ps1"
Write-Host ""
Write-Host "💡 提示: 前端应用支持热重载，修改代码后会自动刷新" -ForegroundColor Cyan
Write-Host "💡 提示: 所有服务已在新窗口中启动，可以查看各自的日志" -ForegroundColor Cyan
Write-Host ""

Read-Host "按回车键关闭此窗口"
