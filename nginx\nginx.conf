user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 上游服务器配置
    upstream auth_backend {
        server auth-service:8001;
        keepalive 32;
    }

    upstream subscription_backend {
        server subscription-service:8002;
        keepalive 32;
    }

    upstream payment_backend {
        server payment-service:8003;
        keepalive 32;
    }

    upstream notification_backend {
        server notification-service:8004;
        keepalive 32;
    }

    upstream newsletter_ai_backend {
        server newsletter-ai:9001;
        keepalive 32;
    }

    upstream main_portal_backend {
        server main-portal:3000;
        keepalive 32;
    }

    upstream newsletter_app_backend {
        server newsletter-app:3001;
        keepalive 32;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    # 主站门户 (www.biocloude.cn)
    server {
        listen 80;
        server_name www.biocloude.cn biocloude.cn localhost;

        # 重定向到HTTPS (生产环境)
        # return 301 https://$server_name$request_uri;

        location / {
            proxy_pass http://main_portal_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }

        # API路由
        location /api/auth/ {
            limit_req zone=auth burst=20 nodelay;
            proxy_pass http://auth_backend/api/v1/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/subscription/ {
            limit_req zone=api burst=50 nodelay;
            proxy_pass http://subscription_backend/api/v1/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/payment/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://payment_backend/api/v1/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/notification/ {
            limit_req zone=api burst=30 nodelay;
            proxy_pass http://notification_backend/api/v1/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # 资讯处理AI应用 (ivdnewsletter.biocloude.cn)
    server {
        listen 80;
        server_name ivdnewsletter.biocloude.cn;

        # 重定向到HTTPS (生产环境)
        # return 301 https://$server_name$request_uri;

        location / {
            proxy_pass http://newsletter_app_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }

        # AI API路由
        location /api/ai/ {
            limit_req zone=api burst=100 nodelay;
            proxy_pass http://newsletter_ai_backend/api/v1/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 300;
        }

        # 认证API路由
        location /api/auth/ {
            limit_req zone=auth burst=20 nodelay;
            proxy_pass http://auth_backend/api/v1/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # HTTPS配置 (生产环境)
    # server {
    #     listen 443 ssl http2;
    #     server_name www.biocloude.cn biocloude.cn;
    #
    #     ssl_certificate /etc/nginx/ssl/biocloude.cn.crt;
    #     ssl_certificate_key /etc/nginx/ssl/biocloude.cn.key;
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     ssl_session_cache shared:SSL:10m;
    #     ssl_session_timeout 10m;
    #
    #     # 其他配置与HTTP相同...
    # }

    # 健康检查端点
    server {
        listen 8080;
        server_name _;

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
        }
    }
}
