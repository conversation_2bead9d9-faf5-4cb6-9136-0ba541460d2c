"""
生科云码蛋白质设计AI
从头设计具有特定功能的新型蛋白质
"""

import asyncio
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from fastapi import FastAPI, HTTPException, Depends, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
import asyncpg
import redis.asyncio as redis
from loguru import logger
import numpy as np
from Bio.Seq import Seq
from Bio.SeqUtils.ProtParam import ProteinAnalysis

# 配置日志
logger.add("logs/protein_ai.log", rotation="1 day", retention="30 days")

app = FastAPI(
    title="生科云码蛋白质设计AI",
    description="从头设计具有特定功能的新型蛋白质",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置
REDIS_URL = "redis://redis:6379/3"
DATABASE_URL = "*************************************************/biocloude"

# 全局变量
redis_client: Optional[redis.Redis] = None
db_pool: Optional[asyncpg.Pool] = None

# 数据模型
class ProteinDesignRequest(BaseModel):
    target_function: str = Field(..., description="目标功能")
    protein_length: int = Field(default=100, ge=50, le=500, description="蛋白质长度")
    secondary_structure: Optional[str] = Field(None, description="期望的二级结构")
    stability_requirement: str = Field(default="medium", description="稳定性要求：low, medium, high")
    expression_system: str = Field(default="ecoli", description="表达系统：ecoli, yeast, mammalian")
    constraints: Dict[str, Any] = Field(default={}, description="设计约束")

class ProteinOptimizationRequest(BaseModel):
    protein_sequence: str = Field(..., description="蛋白质序列")
    optimization_target: str = Field(default="stability", description="优化目标：stability, activity, expression")
    mutations_allowed: int = Field(default=10, ge=1, le=50, description="允许的突变数量")
    preserve_regions: List[Tuple[int, int]] = Field(default=[], description="保守区域")

class ProteinAnalysisRequest(BaseModel):
    protein_sequence: str = Field(..., description="蛋白质序列")
    analysis_type: str = Field(default="comprehensive", description="分析类型")

    @validator('protein_sequence')
    def validate_sequence(cls, v):
        amino_acids = "ACDEFGHIKLMNPQRSTVWY"
        if not all(aa in amino_acids for aa in v.upper()):
            raise ValueError('序列只能包含标准氨基酸字符')
        return v.upper()

class StructurePredictionRequest(BaseModel):
    protein_sequence: str = Field(..., description="蛋白质序列")
    prediction_method: str = Field(default="alphafold", description="预测方法")
    confidence_threshold: float = Field(default=0.7, description="置信度阈值")

class FoldingSimulationRequest(BaseModel):
    protein_sequence: str = Field(..., description="蛋白质序列")
    simulation_steps: int = Field(default=1000, description="模拟步数")
    temperature: float = Field(default=300.0, description="温度(K)")
    force_field: str = Field(default="amber", description="力场类型")

# 蛋白质设计服务
class ProteinDesignService:
    """蛋白质设计服务"""

    def __init__(self):
        # 氨基酸属性
        self.amino_acids = {
            'A': {'hydrophobic': True, 'charge': 0, 'size': 'small'},
            'R': {'hydrophobic': False, 'charge': 1, 'size': 'large'},
            'N': {'hydrophobic': False, 'charge': 0, 'size': 'medium'},
            'D': {'hydrophobic': False, 'charge': -1, 'size': 'medium'},
            'C': {'hydrophobic': True, 'charge': 0, 'size': 'small'},
            'Q': {'hydrophobic': False, 'charge': 0, 'size': 'medium'},
            'E': {'hydrophobic': False, 'charge': -1, 'size': 'medium'},
            'G': {'hydrophobic': True, 'charge': 0, 'size': 'small'},
            'H': {'hydrophobic': False, 'charge': 0.5, 'size': 'medium'},
            'I': {'hydrophobic': True, 'charge': 0, 'size': 'medium'},
            'L': {'hydrophobic': True, 'charge': 0, 'size': 'medium'},
            'K': {'hydrophobic': False, 'charge': 1, 'size': 'large'},
            'M': {'hydrophobic': True, 'charge': 0, 'size': 'medium'},
            'F': {'hydrophobic': True, 'charge': 0, 'size': 'large'},
            'P': {'hydrophobic': True, 'charge': 0, 'size': 'small'},
            'S': {'hydrophobic': False, 'charge': 0, 'size': 'small'},
            'T': {'hydrophobic': False, 'charge': 0, 'size': 'small'},
            'W': {'hydrophobic': True, 'charge': 0, 'size': 'large'},
            'Y': {'hydrophobic': False, 'charge': 0, 'size': 'large'},
            'V': {'hydrophobic': True, 'charge': 0, 'size': 'small'}
        }

        # 二级结构倾向性
        self.secondary_structure_propensity = {
            'helix': ['A', 'E', 'L', 'M'],
            'sheet': ['V', 'I', 'Y', 'F'],
            'turn': ['G', 'N', 'P', 'S']
        }

    async def design_protein(self, request: ProteinDesignRequest) -> Dict[str, Any]:
        """设计蛋白质"""
        try:
            # 生成候选序列
            candidates = self._generate_candidate_sequences(request)

            # 评估候选序列
            evaluated_candidates = []
            for candidate in candidates:
                score = self._evaluate_sequence(candidate, request)
                evaluated_candidates.append({
                    "sequence": candidate,
                    "score": score,
                    "properties": self._analyze_sequence_properties(candidate)
                })

            # 排序并返回最佳候选
            best_candidates = sorted(evaluated_candidates, key=lambda x: x['score'], reverse=True)[:5]

            return {
                "designed_proteins": best_candidates,
                "design_parameters": request.dict(),
                "total_candidates": len(candidates),
                "design_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"蛋白质设计失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    def _generate_candidate_sequences(self, request: ProteinDesignRequest) -> List[str]:
        """生成候选序列"""
        candidates = []

        for _ in range(100):  # 生成100个候选序列
            sequence = self._generate_sequence_by_function(
                request.target_function,
                request.protein_length,
                request.secondary_structure
            )
            candidates.append(sequence)

        return candidates

    def _generate_sequence_by_function(self, function: str, length: int, structure: Optional[str]) -> str:
        """根据功能生成序列"""
        sequence = ""

        # 根据功能选择氨基酸偏好
        if "enzyme" in function.lower():
            # 酶类蛋白偏好
            preferred_aa = ['H', 'D', 'E', 'C', 'S', 'T']
        elif "binding" in function.lower():
            # 结合蛋白偏好
            preferred_aa = ['R', 'K', 'H', 'Y', 'W', 'F']
        elif "structural" in function.lower():
            # 结构蛋白偏好
            preferred_aa = ['G', 'P', 'A', 'V', 'L', 'I']
        else:
            # 默认偏好
            preferred_aa = list(self.amino_acids.keys())

        # 生成序列
        for i in range(length):
            if structure and i < len(structure):
                # 根据二级结构选择氨基酸
                if structure[i] == 'H':  # 螺旋
                    aa_choices = self.secondary_structure_propensity['helix']
                elif structure[i] == 'E':  # 折叠
                    aa_choices = self.secondary_structure_propensity['sheet']
                elif structure[i] == 'T':  # 转角
                    aa_choices = self.secondary_structure_propensity['turn']
                else:
                    aa_choices = preferred_aa
            else:
                aa_choices = preferred_aa

            # 随机选择氨基酸
            sequence += np.random.choice(aa_choices)

        return sequence

    def _evaluate_sequence(self, sequence: str, request: ProteinDesignRequest) -> float:
        """评估序列质量"""
        score = 100.0

        # 分析序列属性
        analysis = ProteinAnalysis(sequence)

        # 分子量评分
        mw = analysis.molecular_weight()
        if 10000 <= mw <= 50000:  # 理想分子量范围
            score += 10
        else:
            score -= abs(mw - 30000) / 1000

        # 等电点评分
        pi = analysis.isoelectric_point()
        if 6.0 <= pi <= 8.0:  # 生理pH范围
            score += 10
        else:
            score -= abs(pi - 7.0) * 2

        # 疏水性评分
        gravy = analysis.gravy()
        if -0.5 <= gravy <= 0.5:  # 适中的疏水性
            score += 10
        else:
            score -= abs(gravy) * 10

        # 不稳定指数评分
        instability = analysis.instability_index()
        if instability < 40:  # 稳定蛋白
            score += 15
        else:
            score -= (instability - 40) * 0.5

        # 芳香族氨基酸含量
        aromatic = analysis.get_amino_acids_percent()
        aromatic_content = aromatic.get('F', 0) + aromatic.get('W', 0) + aromatic.get('Y', 0)
        if 0.05 <= aromatic_content <= 0.15:  # 适当的芳香族含量
            score += 5

        return max(score, 0.0)

    def _analyze_sequence_properties(self, sequence: str) -> Dict[str, Any]:
        """分析序列属性"""
        analysis = ProteinAnalysis(sequence)

        return {
            "length": len(sequence),
            "molecular_weight": analysis.molecular_weight(),
            "isoelectric_point": analysis.isoelectric_point(),
            "gravy": analysis.gravy(),
            "instability_index": analysis.instability_index(),
            "amino_acid_composition": analysis.get_amino_acids_percent(),
            "secondary_structure_fraction": analysis.secondary_structure_fraction()
        }

class ProteinOptimizationService:
    """蛋白质优化服务"""

    async def optimize_protein(self, request: ProteinOptimizationRequest) -> Dict[str, Any]:
        """优化蛋白质"""
        try:
            original_properties = self._analyze_protein(request.protein_sequence)

            # 生成优化候选
            optimized_candidates = self._generate_optimized_variants(request)

            # 评估优化效果
            best_variants = []
            for variant in optimized_candidates:
                properties = self._analyze_protein(variant["sequence"])
                improvement = self._calculate_improvement(
                    original_properties,
                    properties,
                    request.optimization_target
                )

                best_variants.append({
                    "sequence": variant["sequence"],
                    "mutations": variant["mutations"],
                    "properties": properties,
                    "improvement": improvement
                })

            # 排序并返回最佳变体
            best_variants = sorted(best_variants, key=lambda x: x['improvement'], reverse=True)[:10]

            return {
                "original_sequence": request.protein_sequence,
                "original_properties": original_properties,
                "optimized_variants": best_variants,
                "optimization_target": request.optimization_target
            }

        except Exception as e:
            logger.error(f"蛋白质优化失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    def _generate_optimized_variants(self, request: ProteinOptimizationRequest) -> List[Dict[str, Any]]:
        """生成优化变体"""
        variants = []
        sequence = request.protein_sequence

        for _ in range(50):  # 生成50个变体
            variant_seq = list(sequence)
            mutations = []

            # 随机选择突变位点
            mutation_sites = np.random.choice(
                len(sequence),
                size=min(request.mutations_allowed, len(sequence)),
                replace=False
            )

            for site in mutation_sites:
                # 检查是否在保守区域
                if self._is_in_conserved_region(site, request.preserve_regions):
                    continue

                original_aa = sequence[site]
                new_aa = self._select_beneficial_mutation(
                    original_aa,
                    request.optimization_target
                )

                if new_aa != original_aa:
                    variant_seq[site] = new_aa
                    mutations.append({
                        "position": site + 1,
                        "original": original_aa,
                        "mutant": new_aa
                    })

            variants.append({
                "sequence": "".join(variant_seq),
                "mutations": mutations
            })

        return variants

    def _is_in_conserved_region(self, position: int, conserved_regions: List[Tuple[int, int]]) -> bool:
        """检查位置是否在保守区域"""
        for start, end in conserved_regions:
            if start <= position + 1 <= end:  # 转换为1基索引
                return True
        return False

    def _select_beneficial_mutation(self, original_aa: str, target: str) -> str:
        """选择有益突变"""
        amino_acids = "ACDEFGHIKLMNPQRSTVWY"

        if target == "stability":
            # 稳定性优化：偏好小的、疏水的氨基酸
            preferred = ['A', 'V', 'L', 'I', 'G']
        elif target == "activity":
            # 活性优化：偏好极性、带电氨基酸
            preferred = ['R', 'K', 'D', 'E', 'H', 'S', 'T']
        elif target == "expression":
            # 表达优化：避免稀有密码子对应的氨基酸
            preferred = ['A', 'G', 'V', 'L', 'S', 'T']
        else:
            preferred = list(amino_acids)

        # 随机选择一个偏好氨基酸
        return np.random.choice(preferred)

    def _analyze_protein(self, sequence: str) -> Dict[str, Any]:
        """分析蛋白质属性"""
        analysis = ProteinAnalysis(sequence)

        return {
            "molecular_weight": analysis.molecular_weight(),
            "isoelectric_point": analysis.isoelectric_point(),
            "gravy": analysis.gravy(),
            "instability_index": analysis.instability_index(),
            "amino_acid_composition": analysis.get_amino_acids_percent()
        }

    def _calculate_improvement(self, original: Dict, optimized: Dict, target: str) -> float:
        """计算改进程度"""
        if target == "stability":
            # 稳定性改进：降低不稳定指数
            original_instability = original.get("instability_index", 50)
            optimized_instability = optimized.get("instability_index", 50)
            return max(0, original_instability - optimized_instability)

        elif target == "activity":
            # 活性改进：基于等电点和疏水性
            original_pi = original.get("isoelectric_point", 7)
            optimized_pi = optimized.get("isoelectric_point", 7)
            pi_improvement = abs(7 - optimized_pi) - abs(7 - original_pi)
            return pi_improvement

        elif target == "expression":
            # 表达改进：基于GRAVY值
            original_gravy = original.get("gravy", 0)
            optimized_gravy = optimized.get("gravy", 0)
            return abs(original_gravy) - abs(optimized_gravy)

        return 0.0

class StructurePredictionService:
    """结构预测服务"""

    async def predict_structure(self, request: StructurePredictionRequest) -> Dict[str, Any]:
        """预测蛋白质结构"""
        try:
            # 模拟结构预测（实际应用中会调用AlphaFold等工具）
            prediction = self._simulate_structure_prediction(request)

            return {
                "sequence": request.protein_sequence,
                "predicted_structure": prediction,
                "prediction_method": request.prediction_method,
                "confidence": prediction["confidence"],
                "prediction_time": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"结构预测失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    def _simulate_structure_prediction(self, request: StructurePredictionRequest) -> Dict[str, Any]:
        """模拟结构预测"""
        sequence = request.protein_sequence
        length = len(sequence)

        # 模拟二级结构预测
        secondary_structure = self._predict_secondary_structure(sequence)

        # 模拟3D坐标（简化）
        coordinates = self._generate_mock_coordinates(length)

        # 模拟置信度分数
        confidence_scores = np.random.uniform(0.5, 0.95, length)
        avg_confidence = np.mean(confidence_scores)

        return {
            "secondary_structure": secondary_structure,
            "coordinates": coordinates,
            "confidence_scores": confidence_scores.tolist(),
            "confidence": avg_confidence,
            "domains": self._predict_domains(sequence),
            "disorder_regions": self._predict_disorder(sequence)
        }

    def _predict_secondary_structure(self, sequence: str) -> str:
        """预测二级结构"""
        # 简化的二级结构预测
        structure = ""
        for aa in sequence:
            if aa in ['A', 'E', 'L', 'M']:
                structure += 'H'  # 螺旋
            elif aa in ['V', 'I', 'Y', 'F']:
                structure += 'E'  # 折叠
            else:
                structure += 'C'  # 无规卷曲

        return structure

    def _generate_mock_coordinates(self, length: int) -> List[List[float]]:
        """生成模拟坐标"""
        coordinates = []
        for i in range(length):
            # 生成CA原子坐标
            x = i * 3.8 + np.random.normal(0, 0.5)
            y = np.sin(i * 0.1) * 5 + np.random.normal(0, 0.5)
            z = np.cos(i * 0.1) * 5 + np.random.normal(0, 0.5)
            coordinates.append([x, y, z])

        return coordinates

    def _predict_domains(self, sequence: str) -> List[Dict[str, Any]]:
        """预测蛋白质域"""
        domains = []
        length = len(sequence)

        # 简单的域预测
        if length > 100:
            domains.append({
                "name": "Domain1",
                "start": 1,
                "end": length // 2,
                "confidence": 0.8
            })
            domains.append({
                "name": "Domain2",
                "start": length // 2 + 1,
                "end": length,
                "confidence": 0.7
            })

        return domains

    def _predict_disorder(self, sequence: str) -> List[Dict[str, Any]]:
        """预测无序区域"""
        disorder_regions = []

        # 简单的无序区域预测
        for i in range(0, len(sequence), 20):
            if np.random.random() > 0.7:  # 30%概率有无序区域
                disorder_regions.append({
                    "start": i + 1,
                    "end": min(i + 10, len(sequence)),
                    "confidence": np.random.uniform(0.6, 0.9)
                })

        return disorder_regions

# 初始化服务
protein_design_service = ProteinDesignService()
protein_optimization_service = ProteinOptimizationService()
structure_prediction_service = StructurePredictionService()

# 启动事件
@app.on_event("startup")
async def startup_event():
    global redis_client, db_pool

    # 初始化Redis
    redis_client = redis.from_url(REDIS_URL)

    # 初始化数据库连接池
    db_pool = await asyncpg.create_pool(DATABASE_URL)

    logger.info("🚀 Protein AI服务启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    global redis_client, db_pool

    if redis_client:
        await redis_client.close()

    if db_pool:
        await db_pool.close()

    logger.info("👋 Protein AI服务关闭")

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "protein-ai",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0"
    }

@app.post("/api/v1/design")
async def design_protein(request: ProteinDesignRequest):
    """设计蛋白质"""
    result = await protein_design_service.design_protein(request)

    # 更新统计
    await redis_client.incr("stats:total_designs")

    return JSONResponse(content=result)

@app.post("/api/v1/optimize")
async def optimize_protein(request: ProteinOptimizationRequest):
    """优化蛋白质"""
    result = await protein_optimization_service.optimize_protein(request)

    # 更新统计
    await redis_client.incr("stats:total_optimizations")

    return JSONResponse(content=result)

@app.post("/api/v1/analyze")
async def analyze_protein(request: ProteinAnalysisRequest):
    """分析蛋白质"""
    try:
        analysis = ProteinAnalysis(request.protein_sequence)

        result = {
            "sequence": request.protein_sequence,
            "length": len(request.protein_sequence),
            "molecular_weight": analysis.molecular_weight(),
            "isoelectric_point": analysis.isoelectric_point(),
            "gravy": analysis.gravy(),
            "instability_index": analysis.instability_index(),
            "amino_acid_composition": analysis.get_amino_acids_percent(),
            "secondary_structure_fraction": analysis.secondary_structure_fraction(),
            "analysis_time": datetime.utcnow().isoformat()
        }

        # 更新统计
        await redis_client.incr("stats:total_analyses")

        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"蛋白质分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/predict-structure")
async def predict_structure(request: StructurePredictionRequest):
    """预测蛋白质结构"""
    result = await structure_prediction_service.predict_structure(request)

    # 更新统计
    await redis_client.incr("stats:total_predictions")

    return JSONResponse(content=result)

@app.post("/api/v1/simulate-folding")
async def simulate_folding(request: FoldingSimulationRequest):
    """模拟蛋白质折叠"""
    try:
        # 简化的折叠模拟
        simulation_result = {
            "sequence": request.protein_sequence,
            "simulation_steps": request.simulation_steps,
            "temperature": request.temperature,
            "force_field": request.force_field,
            "folding_trajectory": self._simulate_folding_trajectory(request),
            "final_energy": np.random.uniform(-1000, -500),
            "convergence": True,
            "simulation_time": datetime.utcnow().isoformat()
        }

        # 更新统计
        await redis_client.incr("stats:total_simulations")

        return JSONResponse(content=simulation_result)

    except Exception as e:
        logger.error(f"折叠模拟失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def _simulate_folding_trajectory(request: FoldingSimulationRequest) -> List[Dict[str, Any]]:
    """模拟折叠轨迹"""
    trajectory = []

    for step in range(0, request.simulation_steps, 100):
        # 模拟能量变化
        energy = -500 - step * 0.1 + np.random.normal(0, 10)
        rmsd = 10 * np.exp(-step / 500) + np.random.normal(0, 0.5)

        trajectory.append({
            "step": step,
            "energy": energy,
            "rmsd": max(0, rmsd),
            "temperature": request.temperature
        })

    return trajectory

@app.get("/api/v1/stats")
async def get_stats():
    """获取统计信息"""
    try:
        stats = {
            "total_designs": int(await redis_client.get("stats:total_designs") or 0),
            "total_optimizations": int(await redis_client.get("stats:total_optimizations") or 0),
            "total_analyses": int(await redis_client.get("stats:total_analyses") or 0),
            "total_predictions": int(await redis_client.get("stats:total_predictions") or 0),
            "total_simulations": int(await redis_client.get("stats:total_simulations") or 0),
            "success_rate": 0.94,  # 示例数据
            "avg_design_time": 2.3  # 示例数据
        }

        return JSONResponse(content={"stats": stats})

    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/amino-acids")
async def get_amino_acid_properties():
    """获取氨基酸属性"""
    amino_acid_data = {
        'A': {'name': 'Alanine', 'code': 'Ala', 'hydrophobic': True, 'charge': 0, 'size': 'small'},
        'R': {'name': 'Arginine', 'code': 'Arg', 'hydrophobic': False, 'charge': 1, 'size': 'large'},
        'N': {'name': 'Asparagine', 'code': 'Asn', 'hydrophobic': False, 'charge': 0, 'size': 'medium'},
        'D': {'name': 'Aspartic acid', 'code': 'Asp', 'hydrophobic': False, 'charge': -1, 'size': 'medium'},
        'C': {'name': 'Cysteine', 'code': 'Cys', 'hydrophobic': True, 'charge': 0, 'size': 'small'},
        'Q': {'name': 'Glutamine', 'code': 'Gln', 'hydrophobic': False, 'charge': 0, 'size': 'medium'},
        'E': {'name': 'Glutamic acid', 'code': 'Glu', 'hydrophobic': False, 'charge': -1, 'size': 'medium'},
        'G': {'name': 'Glycine', 'code': 'Gly', 'hydrophobic': True, 'charge': 0, 'size': 'small'},
        'H': {'name': 'Histidine', 'code': 'His', 'hydrophobic': False, 'charge': 0.5, 'size': 'medium'},
        'I': {'name': 'Isoleucine', 'code': 'Ile', 'hydrophobic': True, 'charge': 0, 'size': 'medium'},
        'L': {'name': 'Leucine', 'code': 'Leu', 'hydrophobic': True, 'charge': 0, 'size': 'medium'},
        'K': {'name': 'Lysine', 'code': 'Lys', 'hydrophobic': False, 'charge': 1, 'size': 'large'},
        'M': {'name': 'Methionine', 'code': 'Met', 'hydrophobic': True, 'charge': 0, 'size': 'medium'},
        'F': {'name': 'Phenylalanine', 'code': 'Phe', 'hydrophobic': True, 'charge': 0, 'size': 'large'},
        'P': {'name': 'Proline', 'code': 'Pro', 'hydrophobic': True, 'charge': 0, 'size': 'small'},
        'S': {'name': 'Serine', 'code': 'Ser', 'hydrophobic': False, 'charge': 0, 'size': 'small'},
        'T': {'name': 'Threonine', 'code': 'Thr', 'hydrophobic': False, 'charge': 0, 'size': 'small'},
        'W': {'name': 'Tryptophan', 'code': 'Trp', 'hydrophobic': True, 'charge': 0, 'size': 'large'},
        'Y': {'name': 'Tyrosine', 'code': 'Tyr', 'hydrophobic': False, 'charge': 0, 'size': 'large'},
        'V': {'name': 'Valine', 'code': 'Val', 'hydrophobic': True, 'charge': 0, 'size': 'small'}
    }

    return JSONResponse(content={"amino_acids": amino_acid_data})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9004)