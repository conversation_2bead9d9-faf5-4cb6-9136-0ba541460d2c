# 📝 行结束符配置说明

## 🎯 问题背景

在跨平台开发中，不同操作系统使用不同的行结束符：
- **Windows**: CRLF (`\r\n`)
- **Linux/macOS**: LF (`\n`)

这会导致 Git 警告和代码不一致的问题。

## ✅ 解决方案

我们已经配置了完整的行结束符统一方案：

### 1. `.gitattributes` 文件
- 🎯 **统一所有文本文件使用 LF**
- 🔧 **特殊文件类型特殊处理** (如 `.bat` 文件使用 CRLF)
- 📦 **二进制文件不进行转换**

### 2. `.editorconfig` 文件
- 🖥️ **确保所有编辑器使用一致的设置**
- 📏 **统一缩进和编码格式**
- 🔚 **强制使用 LF 行结束符**

### 3. Git 配置
```bash
git config core.autocrlf false
git config core.eol lf
```

## 🚀 使用方法

### 对于新的开发者

1. **克隆仓库后自动应用配置**：
   ```bash
   git clone <repository-url>
   cd biocloude
   # 配置会自动应用
   ```

2. **如果遇到行结束符警告**：
   ```bash
   # Linux/macOS
   ./scripts/fix-line-endings.sh
   
   # Windows
   scripts\fix-line-endings.bat
   ```

### 对于现有开发者

如果您已经在开发，请运行修复脚本：

```bash
# Linux/macOS
chmod +x scripts/fix-line-endings.sh
./scripts/fix-line-endings.sh

# Windows (以管理员身份运行 PowerShell)
scripts\fix-line-endings.bat
```

## 📋 文件类型配置

### 强制使用 LF 的文件
- 📄 **源代码**: `.go`, `.py`, `.js`, `.ts`, `.jsx`, `.tsx`
- ⚙️ **配置文件**: `.json`, `.yaml`, `.yml`, `.env`
- 🐳 **Docker 文件**: `Dockerfile`, `docker-compose.yml`
- 🔧 **脚本文件**: `.sh`, `.bash`
- 📖 **文档文件**: `.md`, `.txt`

### 使用 CRLF 的文件
- 🪟 **Windows 脚本**: `.bat`, `.cmd`, `.ps1`

### 二进制文件 (不转换)
- 🖼️ **图片**: `.png`, `.jpg`, `.gif`, `.svg`
- 📦 **压缩包**: `.zip`, `.tar`, `.gz`
- 🔤 **字体**: `.woff`, `.ttf`, `.otf`

## 🔧 编辑器配置

### VS Code
安装推荐的扩展后，会自动应用 `.editorconfig` 设置。

### JetBrains IDEs
默认支持 `.editorconfig`，无需额外配置。

### Vim/Neovim
安装 `editorconfig-vim` 插件。

### Sublime Text
安装 `EditorConfig` 包。

## 🐛 故障排除

### 问题：仍然看到 CRLF 警告
**解决方案**：
```bash
# 重新规范化仓库
git add --renormalize .
git commit -m "fix: 重新规范化行结束符"
```

### 问题：某些文件仍然使用 CRLF
**解决方案**：
1. 检查 `.gitattributes` 配置
2. 运行修复脚本
3. 手动转换：
   ```bash
   # Linux/macOS
   dos2unix <filename>
   
   # Windows (PowerShell)
   (Get-Content <filename>) -join "`n" | Set-Content <filename>
   ```

### 问题：编辑器不遵循配置
**解决方案**：
1. 确保安装了 EditorConfig 插件
2. 重启编辑器
3. 检查编辑器设置是否覆盖了 EditorConfig

## 📚 最佳实践

### ✅ 推荐做法
- 🔧 **使用支持 EditorConfig 的编辑器**
- 📝 **提交前检查文件格式**
- 🔄 **定期运行修复脚本**
- 👥 **团队成员统一配置**

### ❌ 避免做法
- 🚫 **手动修改 `core.autocrlf` 设置**
- 🚫 **忽略 `.gitattributes` 文件**
- 🚫 **在不同环境使用不同的行结束符**

## 🎯 验证配置

### 检查 Git 配置
```bash
git config --list | grep -E "(autocrlf|eol)"
```

### 检查文件行结束符
```bash
# Linux/macOS
file <filename>

# Windows
Get-Content <filename> -Raw | Select-String "`r`n"
```

### 检查 .gitattributes 生效
```bash
git check-attr text <filename>
```

## 📞 获取帮助

如果遇到行结束符相关问题：

1. 📖 **查看此文档**
2. 🔧 **运行修复脚本**
3. 🐛 **创建 GitHub Issue**
4. 💬 **在团队群组询问**

---

🎉 **配置完成后，您将享受无缝的跨平台开发体验！** 🌍
