syntax = "proto3";

package biocloude.auth;

option go_package = "github.com/biocloude/platform/shared/proto/auth";

import "google/protobuf/timestamp.proto";

// 用户认证服务
service AuthService {
  // 验证JWT Token
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
  
  // 获取用户信息
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  
  // 检查用户权限
  rpc CheckPermission(CheckPermissionRequest) returns (CheckPermissionResponse);
  
  // 刷新Token
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
  
  // 用户登录
  rpc Login(LoginRequest) returns (LoginResponse);
  
  // 用户注册
  rpc Register(RegisterRequest) returns (RegisterResponse);
  
  // 用户登出
  rpc Logout(LogoutRequest) returns (LogoutResponse);
}

// 验证Token请求
message ValidateTokenRequest {
  string token = 1;
  string product_id = 2; // 可选，用于产品特定的权限检查
}

// 验证Token响应
message ValidateTokenResponse {
  bool valid = 1;
  User user = 2;
  repeated string permissions = 3;
  google.protobuf.Timestamp expires_at = 4;
}

// 获取用户请求
message GetUserRequest {
  string user_id = 1;
}

// 获取用户响应
message GetUserResponse {
  User user = 1;
}

// 检查权限请求
message CheckPermissionRequest {
  string user_id = 1;
  string product_id = 2;
  string permission = 3; // 例如: "read", "write", "admin"
}

// 检查权限响应
message CheckPermissionResponse {
  bool allowed = 1;
  string reason = 2; // 如果不允许，说明原因
}

// 刷新Token请求
message RefreshTokenRequest {
  string refresh_token = 1;
}

// 刷新Token响应
message RefreshTokenResponse {
  string access_token = 1;
  string refresh_token = 2;
  google.protobuf.Timestamp expires_at = 3;
}

// 登录请求
message LoginRequest {
  string email = 1;
  string password = 2;
  string product_id = 3; // 登录的产品ID
}

// 登录响应
message LoginResponse {
  bool success = 1;
  string message = 2;
  string access_token = 3;
  string refresh_token = 4;
  User user = 5;
  google.protobuf.Timestamp expires_at = 6;
}

// 注册请求
message RegisterRequest {
  string email = 1;
  string password = 2;
  string username = 3;
  string phone = 4;
  string invitation_code = 5; // 可选的邀请码
}

// 注册响应
message RegisterResponse {
  bool success = 1;
  string message = 2;
  User user = 3;
}

// 登出请求
message LogoutRequest {
  string token = 1;
}

// 登出响应
message LogoutResponse {
  bool success = 1;
  string message = 2;
}

// 用户信息
message User {
  string id = 1;
  string email = 2;
  string username = 3;
  string phone = 4;
  string avatar_url = 5;
  bool is_active = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  repeated Subscription subscriptions = 9;
}

// 订阅信息
message Subscription {
  string id = 1;
  string product_id = 2;
  string product_name = 3;
  string status = 4;
  double price = 5;
  string billing_cycle = 6;
  google.protobuf.Timestamp start_date = 7;
  google.protobuf.Timestamp end_date = 8;
  bool auto_renew = 9;
}
