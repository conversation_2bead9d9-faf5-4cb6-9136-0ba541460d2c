-- 生科云码平台产品初始数据
-- 插入6个AI产品的基础信息

INSERT INTO products (name, slug, domain, description, base_price, billing_type, features) VALUES
(
    '资讯处理AI',
    'newsletter',
    'ivdnewsletter.biocloude.cn',
    '智能简讯订阅过滤配置系统，为生物医学领域提供精准的资讯筛选和个性化推送服务',
    99.00,
    'subscription',
    '{
        "max_subscriptions": 10,
        "ai_filtering": true,
        "custom_keywords": true,
        "email_digest": true,
        "api_access": false,
        "priority_support": false
    }'::jsonb
),
(
    '文献阅读AI',
    'scholar',
    'scholar.bio',
    '基于Semantic Scholar API的智能文献检索和分析平台，提供MRR和ARR收费模式',
    199.00,
    'subscription',
    '{
        "monthly_searches": 1000,
        "pdf_analysis": true,
        "citation_tracking": true,
        "collaboration_tools": false,
        "api_access": true,
        "priority_support": false
    }'::jsonb
),
(
    '引物AI综合体',
    'primer',
    'aiprimer.bio',
    '分子生物学引物查找、设计、验证及计算机模拟评估全生命周期AI系统',
    299.00,
    'subscription',
    '{
        "monthly_designs": 500,
        "primer_validation": true,
        "simulation_analysis": true,
        "batch_processing": true,
        "data_visualization": true,
        "api_access": true,
        "priority_support": true
    }'::jsonb
),
(
    '蛋白质设计AI',
    'protein',
    'aiprotein.bio',
    '从头设计具有特定结构和功能的新型蛋白质，优化现有蛋白质的酶活性、稳定性、亲和力',
    399.00,
    'subscription',
    '{
        "monthly_designs": 200,
        "structure_prediction": true,
        "function_optimization": true,
        "stability_analysis": true,
        "3d_visualization": true,
        "molecular_dynamics": true,
        "api_access": true,
        "priority_support": true
    }'::jsonb
),
(
    '基因编辑AI',
    'gene-edit',
    'aigenedit.bio',
    '智能设计CRISPR gRNAs和基因编辑工具，预测编辑效率和脱靶效应，优化编辑策略',
    349.00,
    'subscription',
    '{
        "monthly_designs": 300,
        "crispr_design": true,
        "off_target_prediction": true,
        "efficiency_scoring": true,
        "guide_optimization": true,
        "batch_analysis": true,
        "data_visualization": true,
        "api_access": true,
        "priority_support": true
    }'::jsonb
),
(
    '代谢工程AI',
    'metabolic',
    'aimetab.bio',
    '设计和优化微生物细胞工厂的代谢途径，高效生产有价值的化合物',
    449.00,
    'subscription',
    '{
        "monthly_pathways": 100,
        "pathway_optimization": true,
        "flux_analysis": true,
        "strain_design": true,
        "yield_prediction": true,
        "metabolic_modeling": true,
        "data_visualization": true,
        "api_access": true,
        "priority_support": true
    }'::jsonb
);

-- 为每个产品创建不同的订阅套餐
-- 基础版、专业版、企业版

-- 资讯处理AI套餐
INSERT INTO products (name, slug, domain, description, base_price, billing_type, features) VALUES
(
    '资讯处理AI - 专业版',
    'newsletter-pro',
    'ivdnewsletter.biocloude.cn',
    '资讯处理AI专业版，提供更多订阅源和高级过滤功能',
    199.00,
    'subscription',
    '{
        "max_subscriptions": 50,
        "ai_filtering": true,
        "custom_keywords": true,
        "email_digest": true,
        "api_access": true,
        "priority_support": true,
        "advanced_analytics": true
    }'::jsonb
),
(
    '资讯处理AI - 企业版',
    'newsletter-enterprise',
    'ivdnewsletter.biocloude.cn',
    '资讯处理AI企业版，无限制使用和定制化服务',
    499.00,
    'subscription',
    '{
        "max_subscriptions": -1,
        "ai_filtering": true,
        "custom_keywords": true,
        "email_digest": true,
        "api_access": true,
        "priority_support": true,
        "advanced_analytics": true,
        "custom_integration": true,
        "dedicated_support": true
    }'::jsonb
);

-- 文献阅读AI套餐
INSERT INTO products (name, slug, domain, description, base_price, billing_type, features) VALUES
(
    '文献阅读AI - 专业版',
    'scholar-pro',
    'scholar.bio',
    '文献阅读AI专业版，提供更多检索次数和协作功能',
    399.00,
    'subscription',
    '{
        "monthly_searches": 5000,
        "pdf_analysis": true,
        "citation_tracking": true,
        "collaboration_tools": true,
        "api_access": true,
        "priority_support": true,
        "advanced_analytics": true
    }'::jsonb
),
(
    '文献阅读AI - 企业版',
    'scholar-enterprise',
    'scholar.bio',
    '文献阅读AI企业版，无限制检索和团队管理功能',
    999.00,
    'subscription',
    '{
        "monthly_searches": -1,
        "pdf_analysis": true,
        "citation_tracking": true,
        "collaboration_tools": true,
        "api_access": true,
        "priority_support": true,
        "advanced_analytics": true,
        "team_management": true,
        "custom_integration": true,
        "dedicated_support": true
    }'::jsonb
);

-- 创建产品分类表
CREATE TABLE product_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 插入产品分类
INSERT INTO product_categories (name, slug, description, sort_order) VALUES
('AI即服务', 'ai-as-a-service', 'AI即服务产品线，提供各种生物科技AI工具', 1),
('预测性维护', 'pdmaas', '预测性维护即服务产品线', 2),
('数据分析', 'data-analytics', '生物数据分析和可视化工具', 3);

-- 创建产品与分类的关联表
CREATE TABLE product_category_relations (
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    category_id UUID REFERENCES product_categories(id) ON DELETE CASCADE,
    PRIMARY KEY (product_id, category_id)
);

-- 将所有AI产品关联到AI即服务分类
INSERT INTO product_category_relations (product_id, category_id)
SELECT p.id, c.id
FROM products p, product_categories c
WHERE c.slug = 'ai-as-a-service';

-- 创建产品定价历史表（用于价格变更追踪）
CREATE TABLE product_pricing_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    old_price DECIMAL(10,2),
    new_price DECIMAL(10,2) NOT NULL,
    change_reason TEXT,
    effective_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 为定价历史表创建索引
CREATE INDEX idx_product_pricing_history_product_id ON product_pricing_history(product_id);
CREATE INDEX idx_product_pricing_history_effective_date ON product_pricing_history(effective_date);
