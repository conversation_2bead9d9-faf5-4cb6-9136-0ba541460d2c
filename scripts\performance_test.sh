#!/bin/bash

# 生科云码平台性能测试脚本
# 使用Apache Bench (ab) 和 wrk 进行负载测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost"
NEWSLETTER_URL="http://ivdnewsletter.biocloude.cn"
CONCURRENT_USERS=50
TEST_DURATION=60
REQUESTS_PER_SECOND=100

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查性能测试工具..."
    
    # 检查Apache Bench
    if ! command -v ab &> /dev/null; then
        log_warning "Apache Bench (ab) 未安装，尝试安装..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y apache2-utils
        elif command -v yum &> /dev/null; then
            sudo yum install -y httpd-tools
        elif command -v brew &> /dev/null; then
            brew install httpd
        else
            log_error "无法自动安装Apache Bench，请手动安装"
            exit 1
        fi
    fi
    
    # 检查wrk
    if ! command -v wrk &> /dev/null; then
        log_warning "wrk 未安装，尝试安装..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get install -y wrk
        elif command -v brew &> /dev/null; then
            brew install wrk
        else
            log_warning "wrk 未安装，将跳过wrk测试"
        fi
    fi
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装curl"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."
    
    local services=(
        "$BASE_URL/api/auth/health"
        "$BASE_URL/api/subscription/health"
        "$BASE_URL/api/ai/health"
        "$BASE_URL/"
        "$NEWSLETTER_URL/"
    )
    
    for service in "${services[@]}"; do
        local retries=30
        while [ $retries -gt 0 ]; do
            if curl -f -s "$service" > /dev/null 2>&1; then
                log_success "服务就绪: $service"
                break
            fi
            
            retries=$((retries - 1))
            if [ $retries -eq 0 ]; then
                log_error "服务启动超时: $service"
                exit 1
            fi
            
            sleep 2
        done
    done
    
    log_success "所有服务已就绪"
}

# 基准性能测试
run_baseline_tests() {
    log_info "🚀 运行基准性能测试..."
    
    local endpoints=(
        "$BASE_URL/api/auth/health"
        "$BASE_URL/api/subscription/products"
        "$BASE_URL/api/ai/health"
        "$BASE_URL/"
    )
    
    for endpoint in "${endpoints[@]}"; do
        log_info "测试端点: $endpoint"
        
        # 使用Apache Bench进行测试
        if command -v ab &> /dev/null; then
            log_info "使用Apache Bench测试..."
            ab -n 1000 -c 10 -g "${endpoint//\//_}_ab.dat" "$endpoint" > "${endpoint//\//_}_ab.txt" 2>&1
            
            # 提取关键指标
            local rps=$(grep "Requests per second" "${endpoint//\//_}_ab.txt" | awk '{print $4}')
            local avg_time=$(grep "Time per request" "${endpoint//\//_}_ab.txt" | head -1 | awk '{print $4}')
            local failed=$(grep "Failed requests" "${endpoint//\//_}_ab.txt" | awk '{print $3}')
            
            log_success "Apache Bench结果: RPS=$rps, 平均响应时间=${avg_time}ms, 失败请求=$failed"
        fi
        
        echo ""
    done
}

# 负载测试
run_load_tests() {
    log_info "⚡ 运行负载测试..."
    
    local endpoints=(
        "$BASE_URL/api/auth/health"
        "$BASE_URL/api/subscription/products"
        "$BASE_URL/api/ai/health"
    )
    
    for endpoint in "${endpoints[@]}"; do
        log_info "负载测试端点: $endpoint"
        
        # 使用wrk进行负载测试
        if command -v wrk &> /dev/null; then
            log_info "使用wrk进行负载测试..."
            wrk -t12 -c$CONCURRENT_USERS -d${TEST_DURATION}s --latency "$endpoint" > "${endpoint//\//_}_wrk.txt" 2>&1
            
            # 显示结果摘要
            grep -E "(Requests/sec|Latency|requests in)" "${endpoint//\//_}_wrk.txt" || true
        else
            # 使用Apache Bench作为备选
            log_info "使用Apache Bench进行负载测试..."
            ab -n $((REQUESTS_PER_SECOND * TEST_DURATION)) -c $CONCURRENT_USERS "$endpoint" > "${endpoint//\//_}_load.txt" 2>&1
            
            # 提取关键指标
            local rps=$(grep "Requests per second" "${endpoint//\//_}_load.txt" | awk '{print $4}')
            local avg_time=$(grep "Time per request" "${endpoint//\//_}_load.txt" | head -1 | awk '{print $4}')
            local p95_time=$(grep "95%" "${endpoint//\//_}_load.txt" | awk '{print $2}')
            
            log_success "负载测试结果: RPS=$rps, 平均响应时间=${avg_time}ms, 95%响应时间=${p95_time}ms"
        fi
        
        echo ""
    done
}

# 压力测试
run_stress_tests() {
    log_info "💥 运行压力测试..."
    
    local endpoint="$BASE_URL/api/auth/health"
    local stress_levels=(100 ************)
    
    for concurrent in "${stress_levels[@]}"; do
        log_info "压力测试: $concurrent 并发用户"
        
        if command -v ab &> /dev/null; then
            ab -n $((concurrent * 10)) -c $concurrent "$endpoint" > "stress_${concurrent}.txt" 2>&1
            
            # 检查是否有失败请求
            local failed=$(grep "Failed requests" "stress_${concurrent}.txt" | awk '{print $3}')
            local rps=$(grep "Requests per second" "stress_${concurrent}.txt" | awk '{print $4}')
            
            if [ "$failed" -eq 0 ]; then
                log_success "压力测试通过: ${concurrent}并发, RPS=$rps, 失败=0"
            else
                log_warning "压力测试警告: ${concurrent}并发, RPS=$rps, 失败=$failed"
            fi
        fi
    done
}

# 数据库性能测试
test_database_performance() {
    log_info "🗄️ 测试数据库性能..."
    
    # 测试认证API（涉及数据库查询）
    local auth_endpoint="$BASE_URL/api/auth/health"
    
    log_info "测试数据库连接性能..."
    if command -v ab &> /dev/null; then
        ab -n 500 -c 20 "$auth_endpoint" > "db_performance.txt" 2>&1
        
        local rps=$(grep "Requests per second" "db_performance.txt" | awk '{print $4}')
        local avg_time=$(grep "Time per request" "db_performance.txt" | head -1 | awk '{print $4}')
        
        log_success "数据库性能: RPS=$rps, 平均响应时间=${avg_time}ms"
        
        # 检查是否满足性能要求
        if (( $(echo "$rps > 100" | bc -l) )); then
            log_success "数据库性能测试通过"
        else
            log_warning "数据库性能可能需要优化"
        fi
    fi
}

# AI服务性能测试
test_ai_performance() {
    log_info "🤖 测试AI服务性能..."
    
    # 创建测试数据
    local test_data='{"title":"CRISPR基因编辑技术","content":"研究人员开发出新的基因编辑工具"}'
    
    # 测试AI分类API
    log_info "测试AI分类性能..."
    
    # 创建临时文件用于POST数据
    echo "$test_data" > /tmp/ai_test_data.json
    
    if command -v ab &> /dev/null; then
        ab -n 100 -c 5 -p /tmp/ai_test_data.json -T "application/json" "$BASE_URL/api/ai/classify" > "ai_performance.txt" 2>&1
        
        local rps=$(grep "Requests per second" "ai_performance.txt" | awk '{print $4}')
        local avg_time=$(grep "Time per request" "ai_performance.txt" | head -1 | awk '{print $4}')
        
        log_success "AI服务性能: RPS=$rps, 平均响应时间=${avg_time}ms"
        
        # AI服务通常响应时间较长，设置更宽松的标准
        if (( $(echo "$avg_time < 5000" | bc -l) )); then
            log_success "AI服务性能测试通过"
        else
            log_warning "AI服务响应时间较长，可能需要优化"
        fi
    fi
    
    # 清理临时文件
    rm -f /tmp/ai_test_data.json
}

# 内存和CPU监控
monitor_resources() {
    log_info "📊 监控系统资源..."
    
    # 检查Docker容器资源使用情况
    if command -v docker &> /dev/null; then
        log_info "Docker容器资源使用情况:"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" | head -10
    fi
    
    # 检查系统资源
    log_info "系统资源使用情况:"
    echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
    echo "内存使用率: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
    echo "磁盘使用率: $(df -h / | awk 'NR==2{print $5}')"
}

# 生成性能报告
generate_performance_report() {
    log_info "📋 生成性能测试报告..."
    
    local report_file="performance_report_$(date +%Y%m%d_%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>生科云码平台性能测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f9ff; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #e5e7eb; border-radius: 8px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f9fafb; border-radius: 4px; }
        .success { color: #059669; }
        .warning { color: #d97706; }
        .error { color: #dc2626; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #e5e7eb; padding: 8px; text-align: left; }
        th { background: #f9fafb; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧬 生科云码平台性能测试报告</h1>
        <p>测试时间: $(date)</p>
        <p>测试配置: ${CONCURRENT_USERS}并发用户, ${TEST_DURATION}秒测试时长</p>
    </div>
    
    <div class="section">
        <h2>📊 测试摘要</h2>
        <div class="metric">
            <strong>基准测试:</strong> <span class="success">完成</span>
        </div>
        <div class="metric">
            <strong>负载测试:</strong> <span class="success">完成</span>
        </div>
        <div class="metric">
            <strong>压力测试:</strong> <span class="success">完成</span>
        </div>
        <div class="metric">
            <strong>AI性能测试:</strong> <span class="success">完成</span>
        </div>
    </div>
    
    <div class="section">
        <h2>🎯 性能指标</h2>
        <table>
            <tr>
                <th>端点</th>
                <th>平均RPS</th>
                <th>平均响应时间(ms)</th>
                <th>95%响应时间(ms)</th>
                <th>状态</th>
            </tr>
EOF

    # 添加测试结果到报告
    for file in *_ab.txt; do
        if [ -f "$file" ]; then
            local endpoint=$(echo "$file" | sed 's/_ab.txt//' | sed 's/_/\//g')
            local rps=$(grep "Requests per second" "$file" | awk '{print $4}' || echo "N/A")
            local avg_time=$(grep "Time per request" "$file" | head -1 | awk '{print $4}' || echo "N/A")
            local p95_time=$(grep "95%" "$file" | awk '{print $2}' || echo "N/A")
            
            cat >> "$report_file" << EOF
            <tr>
                <td>$endpoint</td>
                <td>$rps</td>
                <td>$avg_time</td>
                <td>$p95_time</td>
                <td><span class="success">正常</span></td>
            </tr>
EOF
        fi
    done
    
    cat >> "$report_file" << EOF
        </table>
    </div>
    
    <div class="section">
        <h2>💡 优化建议</h2>
        <ul>
            <li>监控数据库连接池使用情况</li>
            <li>考虑启用Redis缓存</li>
            <li>优化AI模型推理性能</li>
            <li>配置CDN加速静态资源</li>
            <li>启用Gzip压缩</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>📈 详细测试数据</h2>
        <p>详细的测试数据文件:</p>
        <ul>
EOF

    for file in *.txt *.dat; do
        if [ -f "$file" ]; then
            echo "            <li><a href=\"$file\">$file</a></li>" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF
        </ul>
    </div>
</body>
</html>
EOF

    log_success "性能测试报告已生成: $report_file"
}

# 清理测试文件
cleanup_test_files() {
    log_info "🧹 清理测试文件..."
    
    # 保留报告文件，删除临时测试文件
    find . -name "*.txt" -not -name "performance_report*" -delete 2>/dev/null || true
    find . -name "*.dat" -delete 2>/dev/null || true
    
    log_success "测试文件清理完成"
}

# 主函数
main() {
    log_info "🚀 开始生科云码平台性能测试..."
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --concurrent)
                CONCURRENT_USERS="$2"
                shift 2
                ;;
            --duration)
                TEST_DURATION="$2"
                shift 2
                ;;
            --base-url)
                BASE_URL="$2"
                shift 2
                ;;
            --skip-wait)
                SKIP_WAIT=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --concurrent NUM    并发用户数 (默认: $CONCURRENT_USERS)"
                echo "  --duration SEC      测试持续时间 (默认: $TEST_DURATION)"
                echo "  --base-url URL      基础URL (默认: $BASE_URL)"
                echo "  --skip-wait         跳过服务等待"
                echo "  -h, --help          显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行测试步骤
    check_dependencies
    
    if [ "$SKIP_WAIT" != true ]; then
        wait_for_services
    fi
    
    run_baseline_tests
    run_load_tests
    run_stress_tests
    test_database_performance
    test_ai_performance
    monitor_resources
    generate_performance_report
    cleanup_test_files
    
    log_success "🎉 性能测试完成！"
    log_info "查看性能报告: performance_report_*.html"
}

# 错误处理
trap 'log_error "性能测试过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
