# Create basic project structure

Write-Host "Creating basic project structure..." -ForegroundColor Green

# Create directories
Write-Host "Creating directories..." -ForegroundColor Blue
New-Item -ItemType Directory -Path "services/ai-services/newsletter-ai" -Force | Out-Null
New-Item -ItemType Directory -Path "services/ai-services/scholar-ai" -Force | Out-Null
New-Item -ItemType Directory -Path "services/ai-services/primer-ai" -Force | Out-Null
New-Item -ItemType Directory -Path "services/ai-services/protein-ai" -Force | Out-Null
New-Item -ItemType Directory -Path "services/ai-services/gene-editing-ai" -Force | Out-Null
New-Item -ItemType Directory -Path "services/ai-services/metabolic-ai" -Force | Out-Null

# Create basic main.py files
Write-Host "Creating AI service files..." -ForegroundColor Blue

$services = @(
    @{name="newsletter-ai"; port=9001; display="Newsletter AI"},
    @{name="scholar-ai"; port=9002; display="Scholar AI"},
    @{name="primer-ai"; port=9003; display="Primer AI"},
    @{name="protein-ai"; port=9004; display="Protein AI"},
    @{name="gene-editing-ai"; port=9005; display="Gene Editing AI"},
    @{name="metabolic-ai"; port=9006; display="Metabolic AI"}
)

foreach ($service in $services) {
    $content = @"
#!/usr/bin/env python3
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="$($service.display)")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"message": "Welcome to $($service.display)", "service": "$($service.name)"}

@app.get("/health")
def health():
    return {"status": "healthy", "service": "$($service.name)"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=$($service.port))
"@
    
    Set-Content -Path "services/ai-services/$($service.name)/main.py" -Value $content -Encoding UTF8
    
    $requirements = @"
fastapi==0.104.1
uvicorn[standard]==0.24.0
"@
    
    Set-Content -Path "services/ai-services/$($service.name)/requirements.txt" -Value $requirements -Encoding UTF8
    
    Write-Host "  Created $($service.name)" -ForegroundColor Green
}

Write-Host ""
Write-Host "Basic structure created successfully!" -ForegroundColor Green
Write-Host "Now you can run in Codespaces: ./start-dev.sh" -ForegroundColor Cyan
