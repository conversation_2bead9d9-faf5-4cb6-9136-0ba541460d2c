"""
生物科学数据源爬虫服务
支持多种数据源：RSS、API、网页抓取
"""

import asyncio
import aiohttp
import feedparser
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import re
from loguru import logger
import json

from app.core.config import settings
from app.core.redis_client import redis_client
from app.models.article import Article
from app.schemas.article import ArticleCreate

class CrawlerService:
    """爬虫服务类"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.data_sources = self._load_data_sources()
        self.crawl_interval = 300  # 5分钟
        self.max_concurrent = 10
        
    def _load_data_sources(self) -> List[Dict[str, Any]]:
        """加载生物科学数据源配置"""
        return [
            {
                "id": "pubmed_rss",
                "name": "PubMed",
                "type": "rss",
                "url": "https://pubmed.ncbi.nlm.nih.gov/rss/search/1QHBVXiNDExOjEqeA1SqSQ1VUysOTVAuS83P1y9IzS3WT8yrBAA--/?limit=100&utm_campaign=pubmed-2&fc=20210506031353",
                "category": "医学研究",
                "priority": 1,
                "active": True,
                "headers": {
                    "User-Agent": "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
                }
            },
            {
                "id": "nature_news",
                "name": "Nature News",
                "type": "rss",
                "url": "https://www.nature.com/nature.rss",
                "category": "科学期刊",
                "priority": 1,
                "active": True,
                "headers": {
                    "User-Agent": "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
                }
            },
            {
                "id": "science_news",
                "name": "Science Magazine",
                "type": "rss",
                "url": "https://www.science.org/rss/news_current.xml",
                "category": "科学期刊",
                "priority": 1,
                "active": True,
                "headers": {
                    "User-Agent": "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
                }
            },
            {
                "id": "cell_news",
                "name": "Cell",
                "type": "rss",
                "url": "https://www.cell.com/cell/current.rss",
                "category": "生物学期刊",
                "priority": 1,
                "active": True,
                "headers": {
                    "User-Agent": "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
                }
            },
            {
                "id": "nejm_news",
                "name": "New England Journal of Medicine",
                "type": "rss",
                "url": "https://www.nejm.org/action/showFeed?type=etoc&feed=rss",
                "category": "医学期刊",
                "priority": 1,
                "active": True,
                "headers": {
                    "User-Agent": "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
                }
            },
            {
                "id": "lancet_news",
                "name": "The Lancet",
                "type": "rss",
                "url": "https://www.thelancet.com/rssfeed/lancet_current.xml",
                "category": "医学期刊",
                "priority": 1,
                "active": True,
                "headers": {
                    "User-Agent": "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
                }
            },
            {
                "id": "fda_news",
                "name": "FDA News",
                "type": "rss",
                "url": "https://www.fda.gov/about-fda/contact-fda/stay-informed/rss-feeds/press-announcements/rss.xml",
                "category": "监管机构",
                "priority": 2,
                "active": True,
                "headers": {
                    "User-Agent": "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
                }
            },
            {
                "id": "biorxiv",
                "name": "bioRxiv",
                "type": "rss",
                "url": "https://connect.biorxiv.org/biorxiv_xml.php?subject=all",
                "category": "预印本",
                "priority": 2,
                "active": True,
                "headers": {
                    "User-Agent": "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
                }
            },
            {
                "id": "medrxiv",
                "name": "medRxiv",
                "type": "rss",
                "url": "https://connect.medrxiv.org/medrxiv_xml.php?subject=all",
                "category": "医学预印本",
                "priority": 2,
                "active": True,
                "headers": {
                    "User-Agent": "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
                }
            },
            {
                "id": "frontiers_biotech",
                "name": "Frontiers in Biotechnology",
                "type": "rss",
                "url": "https://www.frontiersin.org/journals/bioengineering-and-biotechnology/rss",
                "category": "生物技术",
                "priority": 2,
                "active": True,
                "headers": {
                    "User-Agent": "Mozilla/5.0 (compatible; BioCloude-Crawler/1.0)"
                }
            }
        ]
    
    async def start_crawling(self):
        """启动爬虫服务"""
        logger.info("🕷️ 启动爬虫服务...")
        
        # 创建HTTP会话
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(timeout=timeout)
        
        try:
            while True:
                await self.crawl_all_sources()
                await asyncio.sleep(self.crawl_interval)
        except Exception as e:
            logger.error(f"爬虫服务异常: {e}")
        finally:
            if self.session:
                await self.session.close()
    
    async def crawl_all_sources(self):
        """爬取所有数据源"""
        logger.info("🔄 开始爬取所有数据源...")
        
        active_sources = [source for source in self.data_sources if source.get("active", True)]
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        # 创建爬取任务
        tasks = []
        for source in active_sources:
            task = asyncio.create_task(self._crawl_source_with_semaphore(semaphore, source))
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        total_articles = 0
        successful_sources = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"爬取源 {active_sources[i]['name']} 失败: {result}")
            else:
                total_articles += result
                successful_sources += 1
        
        logger.info(f"✅ 爬取完成: {successful_sources}/{len(active_sources)} 个源成功, 共获取 {total_articles} 篇文章")
        
        # 更新统计信息
        await self._update_stats(total_articles, successful_sources)
    
    async def _crawl_source_with_semaphore(self, semaphore: asyncio.Semaphore, source: Dict[str, Any]) -> int:
        """使用信号量限制的爬取方法"""
        async with semaphore:
            return await self._crawl_source(source)
    
    async def _crawl_source(self, source: Dict[str, Any]) -> int:
        """爬取单个数据源"""
        source_name = source.get("name", "Unknown")
        source_type = source.get("type", "rss")
        
        try:
            logger.info(f"🔍 开始爬取: {source_name}")
            
            if source_type == "rss":
                articles = await self._crawl_rss_source(source)
            elif source_type == "api":
                articles = await self._crawl_api_source(source)
            else:
                logger.warning(f"不支持的数据源类型: {source_type}")
                return 0
            
            # 处理和保存文章
            saved_count = await self._process_articles(articles, source)
            
            logger.info(f"✅ {source_name}: 获取 {len(articles)} 篇, 保存 {saved_count} 篇新文章")
            return saved_count
            
        except Exception as e:
            logger.error(f"❌ 爬取 {source_name} 失败: {e}")
            return 0
    
    async def _crawl_rss_source(self, source: Dict[str, Any]) -> List[Dict[str, Any]]:
        """爬取RSS数据源"""
        url = source["url"]
        headers = source.get("headers", {})
        
        async with self.session.get(url, headers=headers) as response:
            if response.status != 200:
                raise Exception(f"HTTP {response.status}: {await response.text()}")
            
            content = await response.text()
            
        # 解析RSS
        feed = feedparser.parse(content)
        
        if feed.bozo:
            logger.warning(f"RSS解析警告: {feed.bozo_exception}")
        
        articles = []
        for entry in feed.entries:
            article = self._parse_rss_entry(entry, source)
            if article:
                articles.append(article)
        
        return articles
    
    def _parse_rss_entry(self, entry: Any, source: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析RSS条目"""
        try:
            # 提取基本信息
            title = getattr(entry, 'title', '').strip()
            if not title:
                return None
            
            # 提取摘要
            summary = ""
            if hasattr(entry, 'summary'):
                summary = self._clean_html(entry.summary)
            elif hasattr(entry, 'description'):
                summary = self._clean_html(entry.description)
            
            # 提取链接
            link = getattr(entry, 'link', '')
            
            # 提取发布时间
            publish_time = None
            if hasattr(entry, 'published_parsed') and entry.published_parsed:
                publish_time = datetime(*entry.published_parsed[:6])
            elif hasattr(entry, 'updated_parsed') and entry.updated_parsed:
                publish_time = datetime(*entry.updated_parsed[:6])
            else:
                publish_time = datetime.utcnow()
            
            # 提取作者
            author = ""
            if hasattr(entry, 'author'):
                author = entry.author
            elif hasattr(entry, 'authors') and entry.authors:
                author = ", ".join([a.get('name', '') for a in entry.authors])
            
            # 生成唯一ID
            article_id = self._generate_article_id(title, link, source['id'])
            
            # 提取标签
            tags = []
            if hasattr(entry, 'tags'):
                tags = [tag.term for tag in entry.tags if hasattr(tag, 'term')]
            
            # 分类文章
            category = self._classify_article(title, summary, source)
            
            return {
                "id": article_id,
                "title": title,
                "summary": summary[:500] if summary else "",  # 限制摘要长度
                "content": "",  # RSS通常不包含完整内容
                "category": category,
                "source": source["name"],
                "author": author,
                "publish_time": publish_time,
                "url": link,
                "tags": tags,
                "source_id": source["id"],
                "priority": self._calculate_priority(title, summary, source),
                "raw_data": {
                    "rss_entry": str(entry)[:1000]  # 保存原始数据的一部分
                }
            }
            
        except Exception as e:
            logger.error(f"解析RSS条目失败: {e}")
            return None
    
    def _clean_html(self, html_content: str) -> str:
        """清理HTML内容"""
        if not html_content:
            return ""
        
        # 使用BeautifulSoup清理HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        text = soup.get_text()
        
        # 清理多余的空白字符
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _generate_article_id(self, title: str, url: str, source_id: str) -> str:
        """生成文章唯一ID"""
        content = f"{title}|{url}|{source_id}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _classify_article(self, title: str, summary: str, source: Dict[str, Any]) -> str:
        """简单的文章分类"""
        content = f"{title} {summary}".lower()
        
        # 基因相关
        if any(keyword in content for keyword in ['gene', 'genetic', 'genome', 'crispr', 'dna', 'rna', '基因', '遗传']):
            return "基因治疗"
        
        # 医疗器械
        if any(keyword in content for keyword in ['device', 'diagnostic', 'medical device', 'fda approval', '医疗器械', '诊断']):
            return "医疗器械"
        
        # 疫苗
        if any(keyword in content for keyword in ['vaccine', 'vaccination', 'immunization', '疫苗', '免疫']):
            return "疫苗研发"
        
        # 干细胞/再生医学
        if any(keyword in content for keyword in ['stem cell', 'regenerative', 'tissue engineering', '干细胞', '再生医学']):
            return "再生医学"
        
        # 抗感染
        if any(keyword in content for keyword in ['antibiotic', 'antimicrobial', 'infection', 'bacteria', '抗生素', '感染']):
            return "抗感染"
        
        # 免疫治疗
        if any(keyword in content for keyword in ['immunotherapy', 'immune', 'antibody', '免疫治疗', '抗体']):
            return "免疫治疗"
        
        # 默认分类
        return source.get("category", "其他")
    
    def _calculate_priority(self, title: str, summary: str, source: Dict[str, Any]) -> str:
        """计算文章优先级"""
        content = f"{title} {summary}".lower()
        
        # 高优先级关键词
        high_priority_keywords = [
            'breakthrough', 'first', 'novel', 'revolutionary', 'breakthrough',
            'fda approval', 'clinical trial', 'phase iii', 'breakthrough therapy',
            '突破', '首个', '新型', '革命性', 'FDA批准', '临床试验'
        ]
        
        # 中优先级关键词
        medium_priority_keywords = [
            'study', 'research', 'development', 'treatment', 'therapy',
            '研究', '开发', '治疗', '疗法'
        ]
        
        if any(keyword in content for keyword in high_priority_keywords):
            return "high"
        elif any(keyword in content for keyword in medium_priority_keywords):
            return "medium"
        else:
            return "low"
    
    async def _crawl_api_source(self, source: Dict[str, Any]) -> List[Dict[str, Any]]:
        """爬取API数据源"""
        # 这里可以实现特定API的爬取逻辑
        # 例如PubMed API、CrossRef API等
        return []
    
    async def _process_articles(self, articles: List[Dict[str, Any]], source: Dict[str, Any]) -> int:
        """处理和保存文章"""
        if not articles:
            return 0
        
        saved_count = 0
        
        for article_data in articles:
            try:
                # 检查文章是否已存在
                article_id = article_data["id"]
                exists = await redis_client.exists(f"article:{article_id}")
                
                if not exists:
                    # 保存到Redis（临时存储）
                    await redis_client.setex(
                        f"article:{article_id}",
                        86400 * 7,  # 7天过期
                        json.dumps(article_data, default=str, ensure_ascii=False)
                    )
                    
                    # 添加到处理队列
                    await redis_client.lpush("article_processing_queue", article_id)
                    
                    saved_count += 1
                    
            except Exception as e:
                logger.error(f"保存文章失败: {e}")
        
        return saved_count
    
    async def _update_stats(self, total_articles: int, successful_sources: int):
        """更新统计信息"""
        try:
            now = datetime.utcnow()
            today = now.strftime("%Y-%m-%d")
            
            # 更新总体统计
            await redis_client.hset("newsletter:stats", mapping={
                "last_update": now.isoformat(),
                "active_sources": successful_sources,
                "total_sources": len(self.data_sources)
            })
            
            # 更新今日统计
            await redis_client.hincrby("newsletter:stats", "today_articles", total_articles)
            await redis_client.hincrby("newsletter:stats", "total_articles", total_articles)
            
            # 更新处理队列长度
            queue_length = await redis_client.llen("article_processing_queue")
            await redis_client.hset("newsletter:stats", "processing_queue", queue_length)
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    async def get_source_status(self) -> List[Dict[str, Any]]:
        """获取数据源状态"""
        status_list = []
        
        for source in self.data_sources:
            try:
                # 从Redis获取最后更新时间
                last_update = await redis_client.hget(f"source:{source['id']}", "last_update")
                article_count = await redis_client.hget(f"source:{source['id']}", "article_count")
                
                status_list.append({
                    "id": source["id"],
                    "name": source["name"],
                    "category": source.get("category", ""),
                    "active": source.get("active", True),
                    "priority": source.get("priority", 3),
                    "last_update": last_update,
                    "article_count": int(article_count) if article_count else 0,
                    "status": "active" if source.get("active", True) else "inactive"
                })
                
            except Exception as e:
                logger.error(f"获取源 {source['name']} 状态失败: {e}")
                status_list.append({
                    "id": source["id"],
                    "name": source["name"],
                    "status": "error"
                })
        
        return status_list
