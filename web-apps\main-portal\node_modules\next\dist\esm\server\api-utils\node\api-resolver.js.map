{"version": 3, "sources": ["../../../../src/server/api-utils/node/api-resolver.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextApiRequest, NextApiResponse } from '../../../shared/lib/utils'\nimport type { PageConfig, ResponseLimit } from '../../../types'\nimport type { __ApiPreviewProps } from '../.'\nimport type { CookieSerializeOptions } from 'next/dist/compiled/cookie'\nimport type { ServerOnInstrumentationRequestError } from '../../app-render/types'\n\nimport bytes from 'next/dist/compiled/bytes'\nimport { generateETag } from '../../lib/etag'\nimport { sendEtagResponse } from '../../send-payload'\nimport { Stream } from 'stream'\nimport isError from '../../../lib/is-error'\nimport { isResSent } from '../../../shared/lib/utils'\nimport { interopDefault } from '../../../lib/interop-default'\nimport {\n  setLazyProp,\n  sendStatusCode,\n  redirect,\n  clearPreviewData,\n  sendError,\n  A<PERSON><PERSON><PERSON>r,\n  COOKIE_NAME_PRERENDER_BYPASS,\n  COOKIE_NAME_PRERENDER_DATA,\n  RESPONSE_LIMIT_DEFAULT,\n} from './../index'\nimport { getCookieParser } from './../get-cookie-parser'\nimport {\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n} from '../../../lib/constants'\nimport { tryGetPreviewData } from './try-get-preview-data'\nimport { parseBody } from './parse-body'\n\ntype RevalidateFn = (config: {\n  urlPath: string\n  revalidateHeaders: { [key: string]: string | string[] }\n  opts: { unstable_onlyGenerated?: boolean }\n}) => Promise<void>\n\ntype ApiContext = __ApiPreviewProps & {\n  trustHostHeader?: boolean\n  allowedRevalidateHeaderKeys?: string[]\n  hostname?: string\n  revalidate?: RevalidateFn\n  multiZoneDraftMode?: boolean\n  dev: boolean\n}\n\nfunction getMaxContentLength(responseLimit?: ResponseLimit) {\n  if (responseLimit && typeof responseLimit !== 'boolean') {\n    return bytes.parse(responseLimit)\n  }\n  return RESPONSE_LIMIT_DEFAULT\n}\n\n/**\n * Send `any` body to response\n * @param req request object\n * @param res response object\n * @param body of response\n */\nfunction sendData(req: NextApiRequest, res: NextApiResponse, body: any): void {\n  if (body === null || body === undefined) {\n    res.end()\n    return\n  }\n\n  // strip irrelevant headers/body\n  if (res.statusCode === 204 || res.statusCode === 304) {\n    res.removeHeader('Content-Type')\n    res.removeHeader('Content-Length')\n    res.removeHeader('Transfer-Encoding')\n\n    if (process.env.NODE_ENV === 'development' && body) {\n      console.warn(\n        `A body was attempted to be set with a 204 statusCode for ${req.url}, this is invalid and the body was ignored.\\n` +\n          `See more info here https://nextjs.org/docs/messages/invalid-api-status-body`\n      )\n    }\n    res.end()\n    return\n  }\n\n  const contentType = res.getHeader('Content-Type')\n\n  if (body instanceof Stream) {\n    if (!contentType) {\n      res.setHeader('Content-Type', 'application/octet-stream')\n    }\n    body.pipe(res)\n    return\n  }\n\n  const isJSONLike = ['object', 'number', 'boolean'].includes(typeof body)\n  const stringifiedBody = isJSONLike ? JSON.stringify(body) : body\n  const etag = generateETag(stringifiedBody)\n  if (sendEtagResponse(req, res, etag)) {\n    return\n  }\n\n  if (Buffer.isBuffer(body)) {\n    if (!contentType) {\n      res.setHeader('Content-Type', 'application/octet-stream')\n    }\n    res.setHeader('Content-Length', body.length)\n    res.end(body)\n    return\n  }\n\n  if (isJSONLike) {\n    res.setHeader('Content-Type', 'application/json; charset=utf-8')\n  }\n\n  res.setHeader('Content-Length', Buffer.byteLength(stringifiedBody))\n  res.end(stringifiedBody)\n}\n\n/**\n * Send `JSON` object\n * @param res response object\n * @param jsonBody of data\n */\nfunction sendJson(res: NextApiResponse, jsonBody: any): void {\n  // Set header to application/json\n  res.setHeader('Content-Type', 'application/json; charset=utf-8')\n\n  // Use send to handle request\n  res.send(JSON.stringify(jsonBody))\n}\n\nfunction isValidData(str: any): str is string {\n  return typeof str === 'string' && str.length >= 16\n}\n\nfunction setDraftMode<T>(\n  res: NextApiResponse<T>,\n  options: {\n    enable: boolean\n    previewModeId?: string\n  }\n): NextApiResponse<T> {\n  if (!isValidData(options.previewModeId)) {\n    throw new Error('invariant: invalid previewModeId')\n  }\n  const expires = options.enable ? undefined : new Date(0)\n  // To delete a cookie, set `expires` to a date in the past:\n  // https://tools.ietf.org/html/rfc6265#section-4.1.1\n  // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, options.previewModeId, {\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      expires,\n    }),\n  ])\n  return res\n}\n\nfunction setPreviewData<T>(\n  res: NextApiResponse<T>,\n  data: object | string, // TODO: strict runtime type checking\n  options: {\n    maxAge?: number\n    path?: string\n  } & __ApiPreviewProps\n): NextApiResponse<T> {\n  if (!isValidData(options.previewModeId)) {\n    throw new Error('invariant: invalid previewModeId')\n  }\n  if (!isValidData(options.previewModeEncryptionKey)) {\n    throw new Error('invariant: invalid previewModeEncryptionKey')\n  }\n  if (!isValidData(options.previewModeSigningKey)) {\n    throw new Error('invariant: invalid previewModeSigningKey')\n  }\n\n  const jsonwebtoken =\n    require('next/dist/compiled/jsonwebtoken') as typeof import('next/dist/compiled/jsonwebtoken')\n  const { encryptWithSecret } =\n    require('../../crypto-utils') as typeof import('../../crypto-utils')\n  const payload = jsonwebtoken.sign(\n    {\n      data: encryptWithSecret(\n        Buffer.from(options.previewModeEncryptionKey),\n        JSON.stringify(data)\n      ),\n    },\n    options.previewModeSigningKey,\n    {\n      algorithm: 'HS256',\n      ...(options.maxAge !== undefined\n        ? { expiresIn: options.maxAge }\n        : undefined),\n    }\n  )\n\n  // limit preview mode cookie to 2KB since we shouldn't store too much\n  // data here and browsers drop cookies over 4KB\n  if (payload.length > 2048) {\n    throw new Error(\n      `Preview data is limited to 2KB currently, reduce how much data you are storing as preview data to continue`\n    )\n  }\n\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, options.previewModeId, {\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.maxAge !== undefined\n        ? ({ maxAge: options.maxAge } as CookieSerializeOptions)\n        : undefined),\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n    serialize(COOKIE_NAME_PRERENDER_DATA, payload, {\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.maxAge !== undefined\n        ? ({ maxAge: options.maxAge } as CookieSerializeOptions)\n        : undefined),\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n  ])\n  return res\n}\n\nasync function revalidate(\n  urlPath: string,\n  opts: {\n    unstable_onlyGenerated?: boolean\n  },\n  req: IncomingMessage,\n  context: ApiContext\n) {\n  if (typeof urlPath !== 'string' || !urlPath.startsWith('/')) {\n    throw new Error(\n      `Invalid urlPath provided to revalidate(), must be a path e.g. /blog/post-1, received ${urlPath}`\n    )\n  }\n  const revalidateHeaders: HeadersInit = {\n    [PRERENDER_REVALIDATE_HEADER]: context.previewModeId,\n    ...(opts.unstable_onlyGenerated\n      ? {\n          [PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER]: '1',\n        }\n      : {}),\n  }\n  const allowedRevalidateHeaderKeys = [\n    ...(context.allowedRevalidateHeaderKeys || []),\n  ]\n\n  if (context.trustHostHeader || context.dev) {\n    allowedRevalidateHeaderKeys.push('cookie')\n  }\n\n  if (context.trustHostHeader) {\n    allowedRevalidateHeaderKeys.push('x-vercel-protection-bypass')\n  }\n\n  for (const key of Object.keys(req.headers)) {\n    if (allowedRevalidateHeaderKeys.includes(key)) {\n      revalidateHeaders[key] = req.headers[key] as string\n    }\n  }\n\n  try {\n    if (context.trustHostHeader) {\n      const res = await fetch(`https://${req.headers.host}${urlPath}`, {\n        method: 'HEAD',\n        headers: revalidateHeaders,\n      })\n      // we use the cache header to determine successful revalidate as\n      // a non-200 status code can be returned from a successful revalidate\n      // e.g. notFound: true returns 404 status code but is successful\n      const cacheHeader =\n        res.headers.get('x-vercel-cache') || res.headers.get('x-nextjs-cache')\n\n      if (\n        cacheHeader?.toUpperCase() !== 'REVALIDATED' &&\n        res.status !== 200 &&\n        !(res.status === 404 && opts.unstable_onlyGenerated)\n      ) {\n        throw new Error(`Invalid response ${res.status}`)\n      }\n    } else if (context.revalidate) {\n      await context.revalidate({\n        urlPath,\n        revalidateHeaders,\n        opts,\n      })\n    } else {\n      throw new Error(\n        `Invariant: required internal revalidate method not passed to api-utils`\n      )\n    }\n  } catch (err: unknown) {\n    throw new Error(\n      `Failed to revalidate ${urlPath}: ${isError(err) ? err.message : err}`\n    )\n  }\n}\n\nexport async function apiResolver(\n  req: IncomingMessage,\n  res: ServerResponse,\n  query: any,\n  resolverModule: any,\n  apiContext: ApiContext,\n  propagateError: boolean,\n  dev?: boolean,\n  page?: string,\n  onError?: ServerOnInstrumentationRequestError\n): Promise<void> {\n  const apiReq = req as NextApiRequest\n  const apiRes = res as NextApiResponse\n\n  try {\n    if (!resolverModule) {\n      res.statusCode = 404\n      res.end('Not Found')\n      return\n    }\n    const config: PageConfig = resolverModule.config || {}\n    const bodyParser = config.api?.bodyParser !== false\n    const responseLimit = config.api?.responseLimit ?? true\n    const externalResolver = config.api?.externalResolver || false\n\n    // Parsing of cookies\n    setLazyProp({ req: apiReq }, 'cookies', getCookieParser(req.headers))\n    // Parsing query string\n    apiReq.query = query\n    // Parsing preview data\n    setLazyProp({ req: apiReq }, 'previewData', () =>\n      tryGetPreviewData(req, res, apiContext, !!apiContext.multiZoneDraftMode)\n    )\n    // Checking if preview mode is enabled\n    setLazyProp({ req: apiReq }, 'preview', () =>\n      apiReq.previewData !== false ? true : undefined\n    )\n    // Set draftMode to the same value as preview\n    setLazyProp({ req: apiReq }, 'draftMode', () => apiReq.preview)\n\n    // Parsing of body\n    if (bodyParser && !apiReq.body) {\n      apiReq.body = await parseBody(\n        apiReq,\n        config.api && config.api.bodyParser && config.api.bodyParser.sizeLimit\n          ? config.api.bodyParser.sizeLimit\n          : '1mb'\n      )\n    }\n\n    let contentLength = 0\n    const maxContentLength = getMaxContentLength(responseLimit)\n    const writeData = apiRes.write\n    const endResponse = apiRes.end\n    apiRes.write = (...args: any[2]) => {\n      contentLength += Buffer.byteLength(args[0] || '')\n      return writeData.apply(apiRes, args)\n    }\n    apiRes.end = (...args: any[2]) => {\n      if (args.length && typeof args[0] !== 'function') {\n        contentLength += Buffer.byteLength(args[0] || '')\n      }\n\n      if (responseLimit && contentLength >= maxContentLength) {\n        console.warn(\n          `API response for ${req.url} exceeds ${bytes.format(\n            maxContentLength\n          )}. API Routes are meant to respond quickly. https://nextjs.org/docs/messages/api-routes-response-size-limit`\n        )\n      }\n\n      return endResponse.apply(apiRes, args)\n    }\n    apiRes.status = (statusCode) => sendStatusCode(apiRes, statusCode)\n    apiRes.send = (data) => sendData(apiReq, apiRes, data)\n    apiRes.json = (data) => sendJson(apiRes, data)\n    apiRes.redirect = (statusOrUrl: number | string, url?: string) =>\n      redirect(apiRes, statusOrUrl, url)\n    apiRes.setDraftMode = (options = { enable: true }) =>\n      setDraftMode(apiRes, Object.assign({}, apiContext, options))\n    apiRes.setPreviewData = (data, options = {}) =>\n      setPreviewData(apiRes, data, Object.assign({}, apiContext, options))\n    apiRes.clearPreviewData = (options = {}) =>\n      clearPreviewData(apiRes, options)\n    apiRes.revalidate = (\n      urlPath: string,\n      opts?: {\n        unstable_onlyGenerated?: boolean\n      }\n    ) => revalidate(urlPath, opts || {}, req, apiContext)\n\n    const resolver = interopDefault(resolverModule)\n    let wasPiped = false\n\n    if (process.env.NODE_ENV !== 'production') {\n      // listen for pipe event and don't show resolve warning\n      res.once('pipe', () => (wasPiped = true))\n    }\n\n    const apiRouteResult = await resolver(req, res)\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof apiRouteResult !== 'undefined') {\n        if (apiRouteResult instanceof Response) {\n          throw new Error(\n            'API route returned a Response object in the Node.js runtime, this is not supported. Please use `runtime: \"edge\"` instead: https://nextjs.org/docs/api-routes/edge-api-routes'\n          )\n        }\n        console.warn(\n          `API handler should not return a value, received ${typeof apiRouteResult}.`\n        )\n      }\n\n      if (!externalResolver && !isResSent(res) && !wasPiped) {\n        console.warn(\n          `API resolved without sending a response for ${req.url}, this may result in stalled requests.`\n        )\n      }\n    }\n  } catch (err) {\n    onError?.(err, req, {\n      routerKind: 'Pages Router',\n      routePath: page || '',\n      routeType: 'route',\n      revalidateReason: undefined,\n    })\n\n    if (err instanceof ApiError) {\n      sendError(apiRes, err.statusCode, err.message)\n    } else {\n      if (dev) {\n        if (isError(err)) {\n          err.page = page\n        }\n        throw err\n      }\n\n      console.error(err)\n      if (propagateError) {\n        throw err\n      }\n      sendError(apiRes, 500, 'Internal Server Error')\n    }\n  }\n}\n"], "names": ["bytes", "generateETag", "sendEtagResponse", "Stream", "isError", "isResSent", "interopDefault", "setLazyProp", "sendStatusCode", "redirect", "clearPreviewData", "sendError", "ApiError", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "RESPONSE_LIMIT_DEFAULT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "tryGetPreviewData", "parseBody", "getMaxContentLength", "responseLimit", "parse", "sendData", "req", "res", "body", "undefined", "end", "statusCode", "removeHeader", "process", "env", "NODE_ENV", "console", "warn", "url", "contentType", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pipe", "isJSONLike", "includes", "stringifiedBody", "JSON", "stringify", "etag", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "byteLength", "<PERSON><PERSON><PERSON>", "jsonBody", "send", "isValidData", "str", "setDraftMode", "options", "previewModeId", "Error", "expires", "enable", "Date", "serialize", "require", "previous", "Array", "isArray", "httpOnly", "sameSite", "secure", "path", "setPreviewData", "data", "previewModeEncryptionKey", "previewModeSigningKey", "jsonwebtoken", "encryptWithSecret", "payload", "sign", "from", "algorithm", "maxAge", "expiresIn", "revalidate", "url<PERSON><PERSON>", "opts", "context", "startsWith", "revalidateHeaders", "unstable_onlyGenerated", "allowedRevalidateHeaderKeys", "trustHostHeader", "dev", "push", "key", "Object", "keys", "headers", "fetch", "host", "method", "cacheHeader", "get", "toUpperCase", "status", "err", "message", "apiResolver", "query", "resolverModule", "apiContext", "propagateError", "page", "onError", "apiReq", "apiRes", "config", "<PERSON><PERSON><PERSON><PERSON>", "api", "externalResolver", "multiZoneDraftMode", "previewData", "preview", "sizeLimit", "contentLength", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writeData", "write", "endResponse", "args", "apply", "format", "json", "statusOrUrl", "assign", "resolver", "wasPiped", "once", "apiRouteResult", "Response", "routerKind", "routePath", "routeType", "revalidateReason", "error"], "mappings": "AAOA,OAAOA,WAAW,2BAA0B;AAC5C,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,qBAAoB;AACrD,SAASC,MAAM,QAAQ,SAAQ;AAC/B,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,SAAS,QAAQ,4BAA2B;AACrD,SAASC,cAAc,QAAQ,+BAA8B;AAC7D,SACEC,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,EAChBC,SAAS,EACTC,QAAQ,EACRC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,sBAAsB,QACjB,aAAY;AACnB,SAASC,eAAe,QAAQ,yBAAwB;AACxD,SACEC,2BAA2B,EAC3BC,0CAA0C,QACrC,yBAAwB;AAC/B,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,SAAS,QAAQ,eAAc;AAiBxC,SAASC,oBAAoBC,aAA6B;IACxD,IAAIA,iBAAiB,OAAOA,kBAAkB,WAAW;QACvD,OAAOtB,MAAMuB,KAAK,CAACD;IACrB;IACA,OAAOP;AACT;AAEA;;;;;CAKC,GACD,SAASS,SAASC,GAAmB,EAAEC,GAAoB,EAAEC,IAAS;IACpE,IAAIA,SAAS,QAAQA,SAASC,WAAW;QACvCF,IAAIG,GAAG;QACP;IACF;IAEA,gCAAgC;IAChC,IAAIH,IAAII,UAAU,KAAK,OAAOJ,IAAII,UAAU,KAAK,KAAK;QACpDJ,IAAIK,YAAY,CAAC;QACjBL,IAAIK,YAAY,CAAC;QACjBL,IAAIK,YAAY,CAAC;QAEjB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBP,MAAM;YAClDQ,QAAQC,IAAI,CACV,CAAC,yDAAyD,EAAEX,IAAIY,GAAG,CAAC,6CAA6C,CAAC,GAChH,CAAC,2EAA2E,CAAC;QAEnF;QACAX,IAAIG,GAAG;QACP;IACF;IAEA,MAAMS,cAAcZ,IAAIa,SAAS,CAAC;IAElC,IAAIZ,gBAAgBxB,QAAQ;QAC1B,IAAI,CAACmC,aAAa;YAChBZ,IAAIc,SAAS,CAAC,gBAAgB;QAChC;QACAb,KAAKc,IAAI,CAACf;QACV;IACF;IAEA,MAAMgB,aAAa;QAAC;QAAU;QAAU;KAAU,CAACC,QAAQ,CAAC,OAAOhB;IACnE,MAAMiB,kBAAkBF,aAAaG,KAAKC,SAAS,CAACnB,QAAQA;IAC5D,MAAMoB,OAAO9C,aAAa2C;IAC1B,IAAI1C,iBAAiBuB,KAAKC,KAAKqB,OAAO;QACpC;IACF;IAEA,IAAIC,OAAOC,QAAQ,CAACtB,OAAO;QACzB,IAAI,CAACW,aAAa;YAChBZ,IAAIc,SAAS,CAAC,gBAAgB;QAChC;QACAd,IAAIc,SAAS,CAAC,kBAAkBb,KAAKuB,MAAM;QAC3CxB,IAAIG,GAAG,CAACF;QACR;IACF;IAEA,IAAIe,YAAY;QACdhB,IAAIc,SAAS,CAAC,gBAAgB;IAChC;IAEAd,IAAIc,SAAS,CAAC,kBAAkBQ,OAAOG,UAAU,CAACP;IAClDlB,IAAIG,GAAG,CAACe;AACV;AAEA;;;;CAIC,GACD,SAASQ,SAAS1B,GAAoB,EAAE2B,QAAa;IACnD,iCAAiC;IACjC3B,IAAIc,SAAS,CAAC,gBAAgB;IAE9B,6BAA6B;IAC7Bd,IAAI4B,IAAI,CAACT,KAAKC,SAAS,CAACO;AAC1B;AAEA,SAASE,YAAYC,GAAQ;IAC3B,OAAO,OAAOA,QAAQ,YAAYA,IAAIN,MAAM,IAAI;AAClD;AAEA,SAASO,aACP/B,GAAuB,EACvBgC,OAGC;IAED,IAAI,CAACH,YAAYG,QAAQC,aAAa,GAAG;QACvC,MAAM,qBAA6C,CAA7C,IAAIC,MAAM,qCAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA4C;IACpD;IACA,MAAMC,UAAUH,QAAQI,MAAM,GAAGlC,YAAY,IAAImC,KAAK;IACtD,2DAA2D;IAC3D,oDAAoD;IACpD,wEAAwE;IACxE,MAAM,EAAEC,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAWxC,IAAIa,SAAS,CAAC;IAC/Bb,IAAIc,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAO0B,aAAa,WACpB;YAACA;SAAS,GACVC,MAAMC,OAAO,CAACF,YACZA,WACA,EAAE;QACRF,UAAUnD,8BAA8B6C,QAAQC,aAAa,EAAE;YAC7DU,UAAU;YACVC,UAAUtC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DqC,QAAQvC,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCsC,MAAM;YACNX;QACF;KACD;IACD,OAAOnC;AACT;AAEA,SAAS+C,eACP/C,GAAuB,EACvBgD,IAAqB,EACrBhB,OAGqB;IAErB,IAAI,CAACH,YAAYG,QAAQC,aAAa,GAAG;QACvC,MAAM,qBAA6C,CAA7C,IAAIC,MAAM,qCAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA4C;IACpD;IACA,IAAI,CAACL,YAAYG,QAAQiB,wBAAwB,GAAG;QAClD,MAAM,qBAAwD,CAAxD,IAAIf,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D;IACA,IAAI,CAACL,YAAYG,QAAQkB,qBAAqB,GAAG;QAC/C,MAAM,qBAAqD,CAArD,IAAIhB,MAAM,6CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAoD;IAC5D;IAEA,MAAMiB,eACJZ,QAAQ;IACV,MAAM,EAAEa,iBAAiB,EAAE,GACzBb,QAAQ;IACV,MAAMc,UAAUF,aAAaG,IAAI,CAC/B;QACEN,MAAMI,kBACJ9B,OAAOiC,IAAI,CAACvB,QAAQiB,wBAAwB,GAC5C9B,KAAKC,SAAS,CAAC4B;IAEnB,GACAhB,QAAQkB,qBAAqB,EAC7B;QACEM,WAAW;QACX,GAAIxB,QAAQyB,MAAM,KAAKvD,YACnB;YAAEwD,WAAW1B,QAAQyB,MAAM;QAAC,IAC5BvD,SAAS;IACf;IAGF,qEAAqE;IACrE,+CAA+C;IAC/C,IAAImD,QAAQ7B,MAAM,GAAG,MAAM;QACzB,MAAM,qBAEL,CAFK,IAAIU,MACR,CAAC,0GAA0G,CAAC,GADxG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM,EAAEI,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAWxC,IAAIa,SAAS,CAAC;IAC/Bb,IAAIc,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAO0B,aAAa,WACpB;YAACA;SAAS,GACVC,MAAMC,OAAO,CAACF,YACZA,WACA,EAAE;QACRF,UAAUnD,8BAA8B6C,QAAQC,aAAa,EAAE;YAC7DU,UAAU;YACVC,UAAUtC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DqC,QAAQvC,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCsC,MAAM;YACN,GAAId,QAAQyB,MAAM,KAAKvD,YAClB;gBAAEuD,QAAQzB,QAAQyB,MAAM;YAAC,IAC1BvD,SAAS;YACb,GAAI8B,QAAQc,IAAI,KAAK5C,YAChB;gBAAE4C,MAAMd,QAAQc,IAAI;YAAC,IACtB5C,SAAS;QACf;QACAoC,UAAUlD,4BAA4BiE,SAAS;YAC7CV,UAAU;YACVC,UAAUtC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DqC,QAAQvC,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCsC,MAAM;YACN,GAAId,QAAQyB,MAAM,KAAKvD,YAClB;gBAAEuD,QAAQzB,QAAQyB,MAAM;YAAC,IAC1BvD,SAAS;YACb,GAAI8B,QAAQc,IAAI,KAAK5C,YAChB;gBAAE4C,MAAMd,QAAQc,IAAI;YAAC,IACtB5C,SAAS;QACf;KACD;IACD,OAAOF;AACT;AAEA,eAAe2D,WACbC,OAAe,EACfC,IAEC,EACD9D,GAAoB,EACpB+D,OAAmB;IAEnB,IAAI,OAAOF,YAAY,YAAY,CAACA,QAAQG,UAAU,CAAC,MAAM;QAC3D,MAAM,qBAEL,CAFK,IAAI7B,MACR,CAAC,qFAAqF,EAAE0B,SAAS,GAD7F,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,MAAMI,oBAAiC;QACrC,CAACzE,4BAA4B,EAAEuE,QAAQ7B,aAAa;QACpD,GAAI4B,KAAKI,sBAAsB,GAC3B;YACE,CAACzE,2CAA2C,EAAE;QAChD,IACA,CAAC,CAAC;IACR;IACA,MAAM0E,8BAA8B;WAC9BJ,QAAQI,2BAA2B,IAAI,EAAE;KAC9C;IAED,IAAIJ,QAAQK,eAAe,IAAIL,QAAQM,GAAG,EAAE;QAC1CF,4BAA4BG,IAAI,CAAC;IACnC;IAEA,IAAIP,QAAQK,eAAe,EAAE;QAC3BD,4BAA4BG,IAAI,CAAC;IACnC;IAEA,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACzE,IAAI0E,OAAO,EAAG;QAC1C,IAAIP,4BAA4BjD,QAAQ,CAACqD,MAAM;YAC7CN,iBAAiB,CAACM,IAAI,GAAGvE,IAAI0E,OAAO,CAACH,IAAI;QAC3C;IACF;IAEA,IAAI;QACF,IAAIR,QAAQK,eAAe,EAAE;YAC3B,MAAMnE,MAAM,MAAM0E,MAAM,CAAC,QAAQ,EAAE3E,IAAI0E,OAAO,CAACE,IAAI,GAAGf,SAAS,EAAE;gBAC/DgB,QAAQ;gBACRH,SAAST;YACX;YACA,gEAAgE;YAChE,qEAAqE;YACrE,gEAAgE;YAChE,MAAMa,cACJ7E,IAAIyE,OAAO,CAACK,GAAG,CAAC,qBAAqB9E,IAAIyE,OAAO,CAACK,GAAG,CAAC;YAEvD,IACED,CAAAA,+BAAAA,YAAaE,WAAW,QAAO,iBAC/B/E,IAAIgF,MAAM,KAAK,OACf,CAAEhF,CAAAA,IAAIgF,MAAM,KAAK,OAAOnB,KAAKI,sBAAsB,AAAD,GAClD;gBACA,MAAM,qBAA2C,CAA3C,IAAI/B,MAAM,CAAC,iBAAiB,EAAElC,IAAIgF,MAAM,EAAE,GAA1C,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0C;YAClD;QACF,OAAO,IAAIlB,QAAQH,UAAU,EAAE;YAC7B,MAAMG,QAAQH,UAAU,CAAC;gBACvBC;gBACAI;gBACAH;YACF;QACF,OAAO;YACL,MAAM,qBAEL,CAFK,IAAI3B,MACR,CAAC,sEAAsE,CAAC,GADpE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF,EAAE,OAAO+C,KAAc;QACrB,MAAM,qBAEL,CAFK,IAAI/C,MACR,CAAC,qBAAqB,EAAE0B,QAAQ,EAAE,EAAElF,QAAQuG,OAAOA,IAAIC,OAAO,GAAGD,KAAK,GADlE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAEA,OAAO,eAAeE,YACpBpF,GAAoB,EACpBC,GAAmB,EACnBoF,KAAU,EACVC,cAAmB,EACnBC,UAAsB,EACtBC,cAAuB,EACvBnB,GAAa,EACboB,IAAa,EACbC,OAA6C;IAE7C,MAAMC,SAAS3F;IACf,MAAM4F,SAAS3F;IAEf,IAAI;YAOiB4F,aACGA,cACGA;QARzB,IAAI,CAACP,gBAAgB;YACnBrF,IAAII,UAAU,GAAG;YACjBJ,IAAIG,GAAG,CAAC;YACR;QACF;QACA,MAAMyF,SAAqBP,eAAeO,MAAM,IAAI,CAAC;QACrD,MAAMC,aAAaD,EAAAA,cAAAA,OAAOE,GAAG,qBAAVF,YAAYC,UAAU,MAAK;QAC9C,MAAMjG,gBAAgBgG,EAAAA,eAAAA,OAAOE,GAAG,qBAAVF,aAAYhG,aAAa,KAAI;QACnD,MAAMmG,mBAAmBH,EAAAA,eAAAA,OAAOE,GAAG,qBAAVF,aAAYG,gBAAgB,KAAI;QAEzD,qBAAqB;QACrBlH,YAAY;YAAEkB,KAAK2F;QAAO,GAAG,WAAWpG,gBAAgBS,IAAI0E,OAAO;QACnE,uBAAuB;QACvBiB,OAAON,KAAK,GAAGA;QACf,uBAAuB;QACvBvG,YAAY;YAAEkB,KAAK2F;QAAO,GAAG,eAAe,IAC1CjG,kBAAkBM,KAAKC,KAAKsF,YAAY,CAAC,CAACA,WAAWU,kBAAkB;QAEzE,sCAAsC;QACtCnH,YAAY;YAAEkB,KAAK2F;QAAO,GAAG,WAAW,IACtCA,OAAOO,WAAW,KAAK,QAAQ,OAAO/F;QAExC,6CAA6C;QAC7CrB,YAAY;YAAEkB,KAAK2F;QAAO,GAAG,aAAa,IAAMA,OAAOQ,OAAO;QAE9D,kBAAkB;QAClB,IAAIL,cAAc,CAACH,OAAOzF,IAAI,EAAE;YAC9ByF,OAAOzF,IAAI,GAAG,MAAMP,UAClBgG,QACAE,OAAOE,GAAG,IAAIF,OAAOE,GAAG,CAACD,UAAU,IAAID,OAAOE,GAAG,CAACD,UAAU,CAACM,SAAS,GAClEP,OAAOE,GAAG,CAACD,UAAU,CAACM,SAAS,GAC/B;QAER;QAEA,IAAIC,gBAAgB;QACpB,MAAMC,mBAAmB1G,oBAAoBC;QAC7C,MAAM0G,YAAYX,OAAOY,KAAK;QAC9B,MAAMC,cAAcb,OAAOxF,GAAG;QAC9BwF,OAAOY,KAAK,GAAG,CAAC,GAAGE;YACjBL,iBAAiB9E,OAAOG,UAAU,CAACgF,IAAI,CAAC,EAAE,IAAI;YAC9C,OAAOH,UAAUI,KAAK,CAACf,QAAQc;QACjC;QACAd,OAAOxF,GAAG,GAAG,CAAC,GAAGsG;YACf,IAAIA,KAAKjF,MAAM,IAAI,OAAOiF,IAAI,CAAC,EAAE,KAAK,YAAY;gBAChDL,iBAAiB9E,OAAOG,UAAU,CAACgF,IAAI,CAAC,EAAE,IAAI;YAChD;YAEA,IAAI7G,iBAAiBwG,iBAAiBC,kBAAkB;gBACtD5F,QAAQC,IAAI,CACV,CAAC,iBAAiB,EAAEX,IAAIY,GAAG,CAAC,SAAS,EAAErC,MAAMqI,MAAM,CACjDN,kBACA,0GAA0G,CAAC;YAEjH;YAEA,OAAOG,YAAYE,KAAK,CAACf,QAAQc;QACnC;QACAd,OAAOX,MAAM,GAAG,CAAC5E,aAAetB,eAAe6G,QAAQvF;QACvDuF,OAAO/D,IAAI,GAAG,CAACoB,OAASlD,SAAS4F,QAAQC,QAAQ3C;QACjD2C,OAAOiB,IAAI,GAAG,CAAC5D,OAAStB,SAASiE,QAAQ3C;QACzC2C,OAAO5G,QAAQ,GAAG,CAAC8H,aAA8BlG,MAC/C5B,SAAS4G,QAAQkB,aAAalG;QAChCgF,OAAO5D,YAAY,GAAG,CAACC,UAAU;YAAEI,QAAQ;QAAK,CAAC,GAC/CL,aAAa4D,QAAQpB,OAAOuC,MAAM,CAAC,CAAC,GAAGxB,YAAYtD;QACrD2D,OAAO5C,cAAc,GAAG,CAACC,MAAMhB,UAAU,CAAC,CAAC,GACzCe,eAAe4C,QAAQ3C,MAAMuB,OAAOuC,MAAM,CAAC,CAAC,GAAGxB,YAAYtD;QAC7D2D,OAAO3G,gBAAgB,GAAG,CAACgD,UAAU,CAAC,CAAC,GACrChD,iBAAiB2G,QAAQ3D;QAC3B2D,OAAOhC,UAAU,GAAG,CAClBC,SACAC,OAGGF,WAAWC,SAASC,QAAQ,CAAC,GAAG9D,KAAKuF;QAE1C,MAAMyB,WAAWnI,eAAeyG;QAChC,IAAI2B,WAAW;QAEf,IAAI1G,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,uDAAuD;YACvDR,IAAIiH,IAAI,CAAC,QAAQ,IAAOD,WAAW;QACrC;QAEA,MAAME,iBAAiB,MAAMH,SAAShH,KAAKC;QAE3C,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,IAAI,OAAO0G,mBAAmB,aAAa;gBACzC,IAAIA,0BAA0BC,UAAU;oBACtC,MAAM,qBAEL,CAFK,IAAIjF,MACR,iLADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACAzB,QAAQC,IAAI,CACV,CAAC,gDAAgD,EAAE,OAAOwG,eAAe,CAAC,CAAC;YAE/E;YAEA,IAAI,CAACnB,oBAAoB,CAACpH,UAAUqB,QAAQ,CAACgH,UAAU;gBACrDvG,QAAQC,IAAI,CACV,CAAC,4CAA4C,EAAEX,IAAIY,GAAG,CAAC,sCAAsC,CAAC;YAElG;QACF;IACF,EAAE,OAAOsE,KAAK;QACZQ,2BAAAA,QAAUR,KAAKlF,KAAK;YAClBqH,YAAY;YACZC,WAAW7B,QAAQ;YACnB8B,WAAW;YACXC,kBAAkBrH;QACpB;QAEA,IAAI+E,eAAe/F,UAAU;YAC3BD,UAAU0G,QAAQV,IAAI7E,UAAU,EAAE6E,IAAIC,OAAO;QAC/C,OAAO;YACL,IAAId,KAAK;gBACP,IAAI1F,QAAQuG,MAAM;oBAChBA,IAAIO,IAAI,GAAGA;gBACb;gBACA,MAAMP;YACR;YAEAxE,QAAQ+G,KAAK,CAACvC;YACd,IAAIM,gBAAgB;gBAClB,MAAMN;YACR;YACAhG,UAAU0G,QAAQ,KAAK;QACzB;IACF;AACF"}