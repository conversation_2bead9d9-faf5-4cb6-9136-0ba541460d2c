package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Email     string    `json:"email" gorm:"uniqueIndex;not null"`
	Username  string    `json:"username" gorm:"uniqueIndex"`
	Phone     string    `json:"phone" gorm:"uniqueIndex"`
	Password  string    `json:"-" gorm:"not null"` // 密码不在JSON中返回
	AvatarURL string    `json:"avatar_url"`
	
	// 腾讯云CIAM相关
	CIAMUserID string `json:"ciam_user_id" gorm:"uniqueIndex"`
	
	// 用户状态
	IsActive    bool `json:"is_active" gorm:"default:true"`
	IsVerified  bool `json:"is_verified" gorm:"default:false"`
	IsLocked    bool `json:"is_locked" gorm:"default:false"`
	
	// 登录相关
	LastLoginAt    *time.Time `json:"last_login_at"`
	LoginAttempts  int        `json:"-" gorm:"default:0"`
	LockedUntil    *time.Time `json:"-"`
	
	// 验证相关
	EmailVerifiedAt *time.Time `json:"email_verified_at"`
	PhoneVerifiedAt *time.Time `json:"phone_verified_at"`
	
	// 时间戳
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联
	Sessions      []Session      `json:"-" gorm:"foreignKey:UserID"`
	Subscriptions []Subscription `json:"subscriptions,omitempty" gorm:"foreignKey:UserID"`
	Orders        []Order        `json:"-" gorm:"foreignKey:UserID"`
}

// BeforeCreate GORM钩子：创建前
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// Session 用户会话模型
type Session struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID       uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	ProductID    *uuid.UUID `json:"product_id" gorm:"type:uuid;index"`
	SessionToken string    `json:"-" gorm:"uniqueIndex;not null"`
	RefreshToken string    `json:"-" gorm:"uniqueIndex"`
	
	// 会话信息
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
	
	// 时间相关
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// BeforeCreate GORM钩子：创建前
func (s *Session) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (Session) TableName() string {
	return "user_sessions"
}

// IsExpired 检查会话是否过期
func (s *Session) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// Subscription 订阅模型（简化版，完整版在subscription服务中）
type Subscription struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key"`
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	ProductID uuid.UUID `json:"product_id" gorm:"type:uuid;not null;index"`
	Status    string    `json:"status" gorm:"default:'active'"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Subscription) TableName() string {
	return "subscriptions"
}

// Order 订单模型（简化版，完整版在payment服务中）
type Order struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key"`
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Amount    float64   `json:"amount" gorm:"not null"`
	Status    string    `json:"status" gorm:"default:'pending'"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Order) TableName() string {
	return "orders"
}

// UserProfile 用户资料
type UserProfile struct {
	UserID      uuid.UUID `json:"user_id" gorm:"type:uuid;primary_key"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	Company     string    `json:"company"`
	Position    string    `json:"position"`
	Bio         string    `json:"bio"`
	Website     string    `json:"website"`
	Location    string    `json:"location"`
	Timezone    string    `json:"timezone" gorm:"default:'Asia/Shanghai'"`
	Language    string    `json:"language" gorm:"default:'zh-CN'"`
	
	// 偏好设置
	EmailNotifications bool `json:"email_notifications" gorm:"default:true"`
	SMSNotifications   bool `json:"sms_notifications" gorm:"default:false"`
	PushNotifications  bool `json:"push_notifications" gorm:"default:true"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (UserProfile) TableName() string {
	return "user_profiles"
}

// LoginAttempt 登录尝试记录
type LoginAttempt struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Email     string    `json:"email" gorm:"index"`
	IPAddress string    `json:"ip_address" gorm:"index"`
	UserAgent string    `json:"user_agent"`
	Success   bool      `json:"success"`
	FailReason string   `json:"fail_reason"`
	CreatedAt time.Time `json:"created_at" gorm:"index"`
}

// BeforeCreate GORM钩子：创建前
func (la *LoginAttempt) BeforeCreate(tx *gorm.DB) error {
	if la.ID == uuid.Nil {
		la.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (LoginAttempt) TableName() string {
	return "login_attempts"
}

// PasswordReset 密码重置记录
type PasswordReset struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Token     string    `json:"-" gorm:"uniqueIndex;not null"`
	Used      bool      `json:"used" gorm:"default:false"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
	
	// 关联
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// BeforeCreate GORM钩子：创建前
func (pr *PasswordReset) BeforeCreate(tx *gorm.DB) error {
	if pr.ID == uuid.Nil {
		pr.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (PasswordReset) TableName() string {
	return "password_resets"
}

// IsExpired 检查重置令牌是否过期
func (pr *PasswordReset) IsExpired() bool {
	return time.Now().After(pr.ExpiresAt)
}

// EmailVerification 邮箱验证记录
type EmailVerification struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Email     string    `json:"email" gorm:"not null"`
	Token     string    `json:"-" gorm:"uniqueIndex;not null"`
	Used      bool      `json:"used" gorm:"default:false"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
	
	// 关联
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// BeforeCreate GORM钩子：创建前
func (ev *EmailVerification) BeforeCreate(tx *gorm.DB) error {
	if ev.ID == uuid.Nil {
		ev.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (EmailVerification) TableName() string {
	return "email_verifications"
}

// IsExpired 检查验证令牌是否过期
func (ev *EmailVerification) IsExpired() bool {
	return time.Now().After(ev.ExpiresAt)
}

// UserRole 用户角色
type UserRole struct {
	ID     uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Role   string    `json:"role" gorm:"not null"` // admin, user, moderator等
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// BeforeCreate GORM钩子：创建前
func (ur *UserRole) BeforeCreate(tx *gorm.DB) error {
	if ur.ID == uuid.Nil {
		ur.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (UserRole) TableName() string {
	return "user_roles"
}

// Permission 权限模型
type Permission struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"uniqueIndex;not null"`
	Description string    `json:"description"`
	Resource    string    `json:"resource" gorm:"not null"` // 资源名称
	Action      string    `json:"action" gorm:"not null"`   // 操作名称
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// BeforeCreate GORM钩子：创建前
func (p *Permission) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (Permission) TableName() string {
	return "permissions"
}

// RolePermission 角色权限关联
type RolePermission struct {
	RoleID       string    `json:"role_id" gorm:"primaryKey"`
	PermissionID uuid.UUID `json:"permission_id" gorm:"type:uuid;primaryKey"`
	
	CreatedAt time.Time `json:"created_at"`
	
	// 关联
	Permission Permission `json:"permission,omitempty" gorm:"foreignKey:PermissionID"`
}

// TableName 指定表名
func (RolePermission) TableName() string {
	return "role_permissions"
}
