package config

import (
	"os"
	"strconv"
	"time"
)

// Config 应用配置
type Config struct {
	// 服务配置
	HTTPPort int
	GRPCPort int
	LogLevel string

	// 数据库配置
	DatabaseURL string
	RedisURL    string

	// JWT配置
	JWTSecret           string
	AccessTokenExpiry   time.Duration
	RefreshTokenExpiry  time.Duration
	TokenIssuer         string

	// 腾讯云CIAM配置
	CIAMClientID     string
	CIAMClientSecret string
	CIAMEndpoint     string

	// 安全配置
	PasswordMinLength int
	MaxLoginAttempts  int
	LockoutDuration   time.Duration

	// 会话配置
	SessionTimeout    time.Duration
	MaxSessionsPerUser int

	// 限流配置
	RateLimitEnabled bool
	RateLimitRPS     int

	// CORS配置
	AllowedOrigins []string
	AllowedMethods []string
	AllowedHeaders []string

	// 邮件配置
	SMTPHost     string
	SMTPPort     int
	SMTPUsername string
	SMTPPassword string
	SMTPFrom     string

	// 短信配置
	SMSProvider   string
	SMSAccessKey  string
	SMSSecretKey  string
	SMSSignName   string
	SMSTemplateID string
}

// Load 加载配置
func Load() *Config {
	return &Config{
		// 服务配置
		HTTPPort: getEnvAsInt("HTTP_PORT", 8001),
		GRPCPort: getEnvAsInt("GRPC_PORT", 9001),
		LogLevel: getEnv("LOG_LEVEL", "info"),

		// 数据库配置
		DatabaseURL: getEnv("DATABASE_URL", "postgres://biocloude:biocloude123@localhost:5432/biocloude?sslmode=disable"),
		RedisURL:    getEnv("REDIS_URL", "redis://localhost:6379/0"),

		// JWT配置
		JWTSecret:           getEnv("JWT_SECRET", "your-super-secret-jwt-key-change-in-production"),
		AccessTokenExpiry:   getEnvAsDuration("ACCESS_TOKEN_EXPIRY", "15m"),
		RefreshTokenExpiry:  getEnvAsDuration("REFRESH_TOKEN_EXPIRY", "7d"),
		TokenIssuer:         getEnv("TOKEN_ISSUER", "biocloude-auth"),

		// 腾讯云CIAM配置
		CIAMClientID:     getEnv("CIAM_CLIENT_ID", ""),
		CIAMClientSecret: getEnv("CIAM_CLIENT_SECRET", ""),
		CIAMEndpoint:     getEnv("CIAM_ENDPOINT", "https://your-tenant.ciam.tencentcloudapi.com"),

		// 安全配置
		PasswordMinLength: getEnvAsInt("PASSWORD_MIN_LENGTH", 8),
		MaxLoginAttempts:  getEnvAsInt("MAX_LOGIN_ATTEMPTS", 5),
		LockoutDuration:   getEnvAsDuration("LOCKOUT_DURATION", "15m"),

		// 会话配置
		SessionTimeout:     getEnvAsDuration("SESSION_TIMEOUT", "24h"),
		MaxSessionsPerUser: getEnvAsInt("MAX_SESSIONS_PER_USER", 5),

		// 限流配置
		RateLimitEnabled: getEnvAsBool("RATE_LIMIT_ENABLED", true),
		RateLimitRPS:     getEnvAsInt("RATE_LIMIT_RPS", 100),

		// CORS配置
		AllowedOrigins: getEnvAsSlice("ALLOWED_ORIGINS", []string{"*"}),
		AllowedMethods: getEnvAsSlice("ALLOWED_METHODS", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}),
		AllowedHeaders: getEnvAsSlice("ALLOWED_HEADERS", []string{"*"}),

		// 邮件配置
		SMTPHost:     getEnv("SMTP_HOST", "smtp.qq.com"),
		SMTPPort:     getEnvAsInt("SMTP_PORT", 587),
		SMTPUsername: getEnv("SMTP_USERNAME", ""),
		SMTPPassword: getEnv("SMTP_PASSWORD", ""),
		SMTPFrom:     getEnv("SMTP_FROM", "<EMAIL>"),

		// 短信配置
		SMSProvider:   getEnv("SMS_PROVIDER", "tencent"),
		SMSAccessKey:  getEnv("SMS_ACCESS_KEY", ""),
		SMSSecretKey:  getEnv("SMS_SECRET_KEY", ""),
		SMSSignName:   getEnv("SMS_SIGN_NAME", "生科云码"),
		SMSTemplateID: getEnv("SMS_TEMPLATE_ID", ""),
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsBool 获取环境变量并转换为布尔值
func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// getEnvAsDuration 获取环境变量并转换为时间间隔
func getEnvAsDuration(key string, defaultValue string) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	if duration, err := time.ParseDuration(defaultValue); err == nil {
		return duration
	}
	return time.Hour // 默认1小时
}

// getEnvAsSlice 获取环境变量并转换为字符串切片
func getEnvAsSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		// 这里可以实现字符串分割逻辑
		// 简单起见，直接返回单个值的切片
		return []string{value}
	}
	return defaultValue
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 这里可以添加配置验证逻辑
	return nil
}

// IsDevelopment 是否为开发环境
func (c *Config) IsDevelopment() bool {
	return getEnv("ENVIRONMENT", "development") == "development"
}

// IsProduction 是否为生产环境
func (c *Config) IsProduction() bool {
	return getEnv("ENVIRONMENT", "development") == "production"
}
