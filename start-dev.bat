@echo off
REM 生科云码平台开发环境启动脚本 (Windows版本)
REM 启动所有前端和后端应用服务

setlocal enabledelayedexpansion

echo 🚀 启动生科云码平台开发环境...

REM 检查基础服务
echo 📋 检查基础服务状态...

REM 检查PostgreSQL
netstat -an | findstr :5432 >nul
if %errorlevel% neq 0 (
    echo ❌ PostgreSQL 未运行，请先启动基础服务
    echo 💡 在 Codespaces 中，基础服务应该自动启动
    pause
    exit /b 1
)
echo ✅ PostgreSQL 运行正常

REM 检查Redis
netstat -an | findstr :6379 >nul
if %errorlevel% neq 0 (
    echo ⚠️ Redis 未运行，某些功能可能受限
) else (
    echo ✅ Redis 运行正常
)

REM 创建PID目录
if not exist ".pids" mkdir .pids

echo 📦 检查并安装依赖...

REM 检查Go服务依赖
if exist "services\auth-service" (
    echo 🔧 检查认证服务依赖...
    cd services\auth-service
    if not exist "vendor" (
        if exist "go.mod" (
            echo 安装认证服务依赖...
            go mod download
            go mod tidy
        )
    )
    cd ..\..
)

if exist "services\subscription-service" (
    echo 🔧 检查订阅服务依赖...
    cd services\subscription-service
    if not exist "vendor" (
        if exist "go.mod" (
            echo 安装订阅服务依赖...
            go mod download
            go mod tidy
        )
    )
    cd ..\..
)

REM 检查前端依赖
if exist "web-apps\main-portal" (
    echo 🌐 检查主站门户依赖...
    cd web-apps\main-portal
    if not exist "node_modules" (
        if exist "package.json" (
            echo 安装主站门户依赖...
            npm install
        )
    )
    cd ..\..
)

if exist "web-apps\newsletter-app" (
    echo 🌐 检查资讯处理AI应用依赖...
    cd web-apps\newsletter-app
    if not exist "node_modules" (
        if exist "package.json" (
            echo 安装资讯处理AI应用依赖...
            npm install
        )
    )
    cd ..\..
)

if exist "web-apps\scholar-app" (
    echo 🌐 检查文献阅读AI应用依赖...
    cd web-apps\scholar-app
    if not exist "node_modules" (
        if exist "package.json" (
            echo 安装文献阅读AI应用依赖...
            npm install
        )
    )
    cd ..\..
)

echo 🔧 启动后端服务...

REM 启动认证服务
if exist "services\auth-service" (
    echo 启动认证服务 (端口 8001)...
    cd services\auth-service
    start "认证服务" cmd /k "go run main.go"
    cd ..\..
    timeout /t 2 /nobreak >nul
)

REM 启动订阅服务
if exist "services\subscription-service" (
    echo 启动订阅服务 (端口 8002)...
    cd services\subscription-service
    start "订阅服务" cmd /k "go run main.go"
    cd ..\..
    timeout /t 2 /nobreak >nul
)

echo 🤖 启动AI服务...

REM 启动AI服务
set ai_services=newsletter-ai scholar-ai primer-ai protein-ai gene-editing-ai metabolic-ai
set ai_ports=9001 9002 9003 9004 9005 9006

set /a index=0
for %%s in (%ai_services%) do (
    set /a index+=1
    if exist "services\ai-services\%%s" (
        echo 启动 %%s...
        cd services\ai-services\%%s
        start "%%s" cmd /k "python main.py"
        cd ..\..\..
        timeout /t 1 /nobreak >nul
    )
)

echo 🌐 启动前端应用...

REM 启动主站门户
if exist "web-apps\main-portal" (
    echo 启动主站门户 (端口 3000)...
    cd web-apps\main-portal
    start "主站门户" cmd /k "npm run dev"
    cd ..\..
    timeout /t 3 /nobreak >nul
)

REM 启动资讯处理AI应用
if exist "web-apps\newsletter-app" (
    echo 启动资讯处理AI应用 (端口 3001)...
    cd web-apps\newsletter-app
    start "资讯处理AI" cmd /k "npm run dev"
    cd ..\..
    timeout /t 3 /nobreak >nul
)

REM 启动文献阅读AI应用
if exist "web-apps\scholar-app" (
    echo 启动文献阅读AI应用 (端口 3002)...
    cd web-apps\scholar-app
    start "文献阅读AI" cmd /k "npm run dev"
    cd ..\..
    timeout /t 3 /nobreak >nul
)

echo.
echo 🎉 生科云码平台开发环境启动完成！
echo.
echo 📱 前端应用:
echo   🏠 主站门户:     http://localhost:3000
echo   📰 资讯处理AI:   http://localhost:3001
echo   📚 文献阅读AI:   http://localhost:3002
echo.
echo 🔧 API服务:
echo   🔐 认证服务:     http://localhost:8001
echo   💳 订阅服务:     http://localhost:8002
echo   🤖 资讯AI:       http://localhost:9001
echo   📖 文献AI:       http://localhost:9002
echo   🧬 引物AI:       http://localhost:9003
echo   🔬 蛋白质AI:     http://localhost:9004
echo   ✂️ 基因编辑AI:   http://localhost:9005
echo   ⚗️ 代谢工程AI:   http://localhost:9006
echo.
echo 🛠️ 开发工具:
echo   📧 MailHog:      http://localhost:8025
echo   💾 MinIO:        http://localhost:9090
echo   📊 Prometheus:   http://localhost:9091
echo   📈 Grafana:      http://localhost:3030
echo.
echo ⚡ 快捷命令:
echo   停止所有服务:    stop-dev.bat
echo   查看服务状态:    status-dev.bat
echo   运行测试:        test-dev.bat
echo.
echo 💡 提示: 前端应用支持热重载，修改代码后会自动刷新
echo 💡 提示: 按任意键关闭此窗口，服务将继续在后台运行
echo.

pause
