# 创建生科云码平台项目结构 (PowerShell版本)

Write-Host "🏗️ 创建生科云码平台项目结构..." -ForegroundColor Green

# 创建目录结构
function Create-Directories {
    Write-Host "📁 创建目录结构..." -ForegroundColor Blue
    
    # 后端服务目录
    New-Item -ItemType Directory -Path "services/auth-service" -Force | Out-Null
    New-Item -ItemType Directory -Path "services/subscription-service" -Force | Out-Null
    
    # AI 服务目录
    $aiServices = @("newsletter-ai", "scholar-ai", "primer-ai", "protein-ai", "gene-editing-ai", "metabolic-ai")
    foreach ($service in $aiServices) {
        New-Item -ItemType Directory -Path "services/ai-services/$service" -Force | Out-Null
    }
    
    # 前端应用目录
    New-Item -ItemType Directory -Path "web-apps/main-portal/pages" -Force | Out-Null
    New-Item -ItemType Directory -Path "web-apps/newsletter-app/pages" -Force | Out-Null
    New-Item -ItemType Directory -Path "web-apps/scholar-app/pages" -Force | Out-Null
    
    # 其他目录
    New-Item -ItemType Directory -Path "database/migrations" -Force | Out-Null
    New-Item -ItemType Directory -Path "database/seeds" -Force | Out-Null
    New-Item -ItemType Directory -Path "docs" -Force | Out-Null
    New-Item -ItemType Directory -Path "scripts" -Force | Out-Null
    New-Item -ItemType Directory -Path "k8s" -Force | Out-Null
    New-Item -ItemType Directory -Path "logs" -Force | Out-Null
    New-Item -ItemType Directory -Path ".pids" -Force | Out-Null
    
    Write-Host "✅ 目录结构创建完成" -ForegroundColor Green
}

# 创建 AI 服务文件
function Create-AIServices {
    Write-Host "🤖 创建 AI 服务文件..." -ForegroundColor Blue
    
    $aiServices = @(
        @{Name="newsletter-ai"; Port=9001; DisplayName="资讯处理AI"},
        @{Name="scholar-ai"; Port=9002; DisplayName="文献阅读AI"},
        @{Name="primer-ai"; Port=9003; DisplayName="引物设计AI"},
        @{Name="protein-ai"; Port=9004; DisplayName="蛋白质设计AI"},
        @{Name="gene-editing-ai"; Port=9005; DisplayName="基因编辑AI"},
        @{Name="metabolic-ai"; Port=9006; DisplayName="代谢工程AI"}
    )
    
    foreach ($service in $aiServices) {
        Write-Host "  创建 $($service.Name)..." -ForegroundColor Cyan
        
        # 创建 main.py
        $mainPyContent = @"
#!/usr/bin/env python3
"""
$($service.DisplayName) 服务
端口: $($service.Port)
"""

import asyncio
import logging
from datetime import datetime
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="$($service.DisplayName)",
    description="生科云码平台 - $($service.DisplayName) 服务",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class HealthResponse(BaseModel):
    status: str
    service: str
    timestamp: str
    version: str

class StatsResponse(BaseModel):
    service: str
    uptime: str
    requests_count: int
    status: str

# 全局变量
start_time = datetime.now()
requests_count = 0

@app.middleware("http")
async def count_requests(request, call_next):
    global requests_count
    requests_count += 1
    response = await call_next(request)
    return response

@app.get("/")
async def root():
    return {
        "message": "欢迎使用 $($service.DisplayName)",
        "service": "$($service.Name)",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    return HealthResponse(
        status="healthy",
        service="$($service.Name)",
        timestamp=datetime.now().isoformat(),
        version="1.0.0"
    )

@app.get("/api/v1/stats", response_model=StatsResponse)
async def get_stats():
    """获取服务统计信息"""
    uptime = datetime.now() - start_time
    return StatsResponse(
        service="$($service.Name)",
        uptime=str(uptime),
        requests_count=requests_count,
        status="running"
    )

@app.post("/api/v1/process")
async def process_data(data: dict):
    """处理数据的主要端点"""
    try:
        # 这里是具体的 AI 处理逻辑
        # 目前返回模拟结果
        result = {
            "service": "$($service.Name)",
            "input": data,
            "output": f"由 $($service.DisplayName) 处理的结果",
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }
        
        logger.info(f"处理请求: {data}")
        return result
        
    except Exception as e:
        logger.error(f"处理请求时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    logger.info(f"启动 $($service.DisplayName) 服务在端口 $($service.Port)")
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=$($service.Port),
        log_level="info"
    )
"@
        
        Set-Content -Path "services/ai-services/$($service.Name)/main.py" -Value $mainPyContent -Encoding UTF8
        
        # 创建 requirements.txt
        $requirementsContent = @"
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6
aiofiles==23.2.1
httpx==0.25.2
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
loguru==0.7.2
asyncpg==0.29.0
redis==5.0.1
"@
        
        Set-Content -Path "services/ai-services/$($service.Name)/requirements.txt" -Value $requirementsContent -Encoding UTF8
    }
    
    Write-Host "✅ AI 服务文件创建完成" -ForegroundColor Green
}

# 创建前端应用
function Create-FrontendApps {
    Write-Host "🌐 创建前端应用..." -ForegroundColor Blue
    
    # 主站门户
    $mainPortalPackage = @"
{
  "name": "biocloude-main-portal",
  "version": "1.0.0",
  "description": "生科云码平台主站门户",
  "scripts": {
    "dev": "next dev -p 3000",
    "build": "next build",
    "start": "next start -p 3000",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "18.2.0",
    "react-dom": "18.2.0"
  },
  "devDependencies": {
    "@types/node": "20.8.0",
    "@types/react": "18.2.0",
    "@types/react-dom": "18.2.0",
    "eslint": "8.51.0",
    "eslint-config-next": "14.0.0",
    "typescript": "5.2.0"
  }
}
"@
    
    Set-Content -Path "web-apps/main-portal/package.json" -Value $mainPortalPackage -Encoding UTF8
    
    $mainPortalIndex = @"
export default function Home() {
  return (
    <div style={{ padding: '2rem', fontFamily: 'Arial, sans-serif' }}>
      <h1>🧬 生科云码平台</h1>
      <p>欢迎使用生科云码平台主站门户</p>
      <p>🚧 开发中...</p>
      <div>
        <h2>AI 服务</h2>
        <ul>
          <li><a href="http://localhost:3001">📰 资讯处理AI</a></li>
          <li><a href="http://localhost:3002">📚 文献阅读AI</a></li>
        </ul>
      </div>
    </div>
  )
}
"@
    
    Set-Content -Path "web-apps/main-portal/pages/index.js" -Value $mainPortalIndex -Encoding UTF8
    
    Write-Host "✅ 前端应用创建完成" -ForegroundColor Green
}

# 主函数
function Main {
    Write-Host "🏗️ 开始创建生科云码平台项目结构..." -ForegroundColor Yellow
    
    Create-Directories
    Create-AIServices
    Create-FrontendApps
    
    Write-Host ""
    Write-Host "🎉 项目结构创建完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 创建的内容:" -ForegroundColor Yellow
    Write-Host "  📁 services/ai-services/ - 6个AI服务 (Python)" -ForegroundColor White
    Write-Host "  📁 web-apps/ - 前端应用 (Next.js)" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 下一步:" -ForegroundColor Cyan
    Write-Host "  1. 在 Codespaces 中运行: ./start-dev.sh" -ForegroundColor White
    Write-Host "  2. 访问应用: http://localhost:3000" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 提示: 所有服务都是基础框架，具体功能需要进一步开发" -ForegroundColor Yellow
}

# 执行主函数
Main
