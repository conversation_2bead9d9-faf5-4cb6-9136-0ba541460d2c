# 🪟 Windows 本地环境设置指南

## 🚨 当前问题诊断

您当前在 Windows 本地环境中运行，但是：
- ❌ Docker 未安装或未运行
- ❌ PostgreSQL 等基础服务未启动
- ❌ 缺少 Linux 环境依赖

## 🎯 解决方案

### 方案1: 使用 GitHub Codespaces (强烈推荐) 🌟

这是最简单、最可靠的方式：

1. **访问 GitHub 仓库**
2. **点击 Code → Codespaces → Create codespace**
3. **等待环境自动配置**
4. **运行 `./start-dev.sh`**

**优势：**
- ✅ 零配置，开箱即用
- ✅ 所有依赖自动安装
- ✅ 统一的开发环境
- ✅ 强大的云端资源

### 方案2: 安装 Docker Desktop

如果您想在本地开发：

#### 步骤1: 安装 Docker Desktop
1. 访问 https://www.docker.com/products/docker-desktop/
2. 下载并安装 Docker Desktop for Windows
3. 启动 Docker Desktop
4. 等待 Docker 完全启动（系统托盘图标变为绿色）

#### 步骤2: 验证 Docker 安装
```powershell
# 检查 Docker 版本
docker --version

# 检查 Docker 状态
docker ps
```

#### 步骤3: 启动基础服务
```powershell
# 进入项目目录
cd C:\Users\<USER>\Documents\augment-projects\biocloude

# 启动基础服务
docker-compose -f .devcontainer/docker-compose.yml up -d

# 等待服务启动
Start-Sleep -Seconds 30

# 检查服务状态
docker ps
```

#### 步骤4: 启动应用服务
```powershell
# 使用 PowerShell 脚本
.\start-dev.ps1

# 或使用批处理文件
start-dev.bat
```

### 方案3: 使用 WSL2 + Docker

#### 步骤1: 启用 WSL2
```powershell
# 以管理员身份运行 PowerShell
wsl --install

# 重启计算机
```

#### 步骤2: 安装 Docker Desktop with WSL2
1. 安装 Docker Desktop
2. 在设置中启用 "Use the WSL 2 based engine"
3. 重启 Docker Desktop

#### 步骤3: 在 WSL2 中运行
```bash
# 进入 WSL2
wsl

# 导航到项目目录
cd /mnt/c/Users/<USER>/Documents/augment-projects/biocloude

# 运行 Linux 脚本
./start-dev.sh
```

## 🔧 快速修复命令

### 如果您已经安装了 Docker Desktop

```powershell
# 1. 启动 Docker Desktop（手动打开应用）

# 2. 等待 Docker 启动完成，然后运行：
cd C:\Users\<USER>\Documents\augment-projects\biocloude

# 3. 启动基础服务
docker-compose -f .devcontainer/docker-compose.yml up -d

# 4. 等待 PostgreSQL 启动（约 2-3 分钟）
Write-Host "等待 PostgreSQL 启动..." -ForegroundColor Yellow
do {
    Start-Sleep -Seconds 5
    $result = docker exec $(docker ps --filter "name=postgres" --format "{{.Names}}" | Select-Object -First 1) pg_isready -U biocloude 2>$null
} while ($LASTEXITCODE -ne 0)
Write-Host "PostgreSQL 已启动！" -ForegroundColor Green

# 5. 启动应用服务
.\start-dev.ps1
```

## 🚀 推荐的开发环境选择

### 🥇 GitHub Codespaces
- **最佳选择**：零配置，云端开发
- **适合**：所有开发者，特别是新手
- **优势**：无需本地安装，统一环境

### 🥈 WSL2 + Docker Desktop
- **次佳选择**：接近 Linux 的体验
- **适合**：熟悉 Linux 的开发者
- **优势**：本地开发，Linux 兼容性好

### 🥉 Windows + Docker Desktop
- **备选方案**：纯 Windows 环境
- **适合**：必须在 Windows 下开发的场景
- **劣势**：配置复杂，兼容性问题

## 📋 环境对比

| 特性 | Codespaces | WSL2 + Docker | Windows + Docker |
|------|------------|---------------|------------------|
| 配置难度 | ⭐ 极简 | ⭐⭐ 简单 | ⭐⭐⭐ 复杂 |
| 启动速度 | ⭐⭐⭐ 快 | ⭐⭐ 中等 | ⭐ 慢 |
| 兼容性 | ⭐⭐⭐ 完美 | ⭐⭐⭐ 很好 | ⭐⭐ 一般 |
| 资源消耗 | ⭐⭐⭐ 无 | ⭐⭐ 中等 | ⭐ 高 |
| 网络访问 | ⭐⭐⭐ 优秀 | ⭐⭐⭐ 很好 | ⭐⭐ 一般 |

## 🆘 故障排除

### Docker Desktop 无法启动
1. **重启计算机**
2. **以管理员身份运行 Docker Desktop**
3. **检查 Hyper-V 是否启用**
4. **更新 Windows 到最新版本**

### WSL2 问题
```powershell
# 更新 WSL2
wsl --update

# 重启 WSL2
wsl --shutdown
wsl
```

### 端口冲突
```powershell
# 查看端口占用
netstat -ano | findstr :5432
netstat -ano | findstr :3000

# 停止占用端口的进程
Stop-Process -Id <PID> -Force
```

## 💡 最终建议

**🌟 强烈建议使用 GitHub Codespaces！**

原因：
1. **零配置** - 无需安装任何软件
2. **统一环境** - 所有开发者使用相同配置
3. **云端资源** - 强大的计算能力
4. **即开即用** - 3分钟内可以开始开发
5. **避免问题** - 无需处理本地环境问题

### 立即使用 Codespaces：
1. 访问 GitHub 仓库
2. 点击 **Code** → **Codespaces** → **Create codespace**
3. 等待初始化完成
4. 运行 `./start-dev.sh`
5. 开始开发！

---

🎯 **选择最适合您的方案，开始您的生科云码平台开发之旅！**
