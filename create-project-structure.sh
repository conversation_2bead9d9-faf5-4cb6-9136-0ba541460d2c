#!/bin/bash

# 创建生科云码平台项目结构

echo "🏗️ 创建生科云码平台项目结构..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    # 后端服务目录
    mkdir -p services/auth-service
    mkdir -p services/subscription-service
    
    # AI 服务目录
    mkdir -p services/ai-services/newsletter-ai
    mkdir -p services/ai-services/scholar-ai
    mkdir -p services/ai-services/primer-ai
    mkdir -p services/ai-services/protein-ai
    mkdir -p services/ai-services/gene-editing-ai
    mkdir -p services/ai-services/metabolic-ai
    
    # 前端应用目录
    mkdir -p web-apps/main-portal
    mkdir -p web-apps/newsletter-app
    mkdir -p web-apps/scholar-app
    
    # 其他目录
    mkdir -p database/migrations
    mkdir -p database/seeds
    mkdir -p docs
    mkdir -p scripts
    mkdir -p k8s
    mkdir -p logs
    mkdir -p .pids
    
    log_success "目录结构创建完成"
}

# 创建 AI 服务的 main.py 文件
create_ai_services() {
    log_info "创建 AI 服务文件..."
    
    # AI 服务列表
    ai_services=("newsletter-ai" "scholar-ai" "primer-ai" "protein-ai" "gene-editing-ai" "metabolic-ai")
    ai_ports=(9001 9002 9003 9004 9005 9006)
    ai_names=("资讯处理AI" "文献阅读AI" "引物设计AI" "蛋白质设计AI" "基因编辑AI" "代谢工程AI")
    
    for i in "${!ai_services[@]}"; do
        local service="${ai_services[$i]}"
        local port="${ai_ports[$i]}"
        local name="${ai_names[$i]}"
        
        log_info "创建 $service..."
        
        # 创建 main.py
        cat > "services/ai-services/$service/main.py" << EOF
#!/usr/bin/env python3
"""
$name 服务
端口: $port
"""

import asyncio
import logging
from datetime import datetime
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="$name",
    description="生科云码平台 - $name 服务",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class HealthResponse(BaseModel):
    status: str
    service: str
    timestamp: str
    version: str

class StatsResponse(BaseModel):
    service: str
    uptime: str
    requests_count: int
    status: str

# 全局变量
start_time = datetime.now()
requests_count = 0

@app.middleware("http")
async def count_requests(request, call_next):
    global requests_count
    requests_count += 1
    response = await call_next(request)
    return response

@app.get("/")
async def root():
    return {
        "message": "欢迎使用 $name",
        "service": "$service",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    return HealthResponse(
        status="healthy",
        service="$service",
        timestamp=datetime.now().isoformat(),
        version="1.0.0"
    )

@app.get("/api/v1/stats", response_model=StatsResponse)
async def get_stats():
    """获取服务统计信息"""
    uptime = datetime.now() - start_time
    return StatsResponse(
        service="$service",
        uptime=str(uptime),
        requests_count=requests_count,
        status="running"
    )

@app.post("/api/v1/process")
async def process_data(data: dict):
    """处理数据的主要端点"""
    try:
        # 这里是具体的 AI 处理逻辑
        # 目前返回模拟结果
        result = {
            "service": "$service",
            "input": data,
            "output": f"由 $name 处理的结果",
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }
        
        logger.info(f"处理请求: {data}")
        return result
        
    except Exception as e:
        logger.error(f"处理请求时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    logger.info(f"启动 $name 服务在端口 $port")
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=$port,
        log_level="info"
    )
EOF
        
        # 创建 requirements.txt
        cat > "services/ai-services/$service/requirements.txt" << EOF
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6
aiofiles==23.2.1
httpx==0.25.2
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
loguru==0.7.2
asyncpg==0.29.0
redis==5.0.1
EOF
        
        # 创建 README.md
        cat > "services/ai-services/$service/README.md" << EOF
# $name

## 描述
$name 是生科云码平台的核心 AI 服务之一，提供专业的生物科技 AI 功能。

## 端口
- 服务端口: $port
- 健康检查: http://localhost:$port/health
- API 文档: http://localhost:$port/docs

## 启动方式
\`\`\`bash
cd services/ai-services/$service
python main.py
\`\`\`

## API 端点
- \`GET /\` - 服务信息
- \`GET /health\` - 健康检查
- \`GET /api/v1/stats\` - 服务统计
- \`POST /api/v1/process\` - 数据处理

## 开发状态
🚧 开发中 - 基础框架已完成，具体 AI 功能待实现
EOF
        
        log_success "$service 创建完成"
    done
}

# 创建后端服务文件
create_backend_services() {
    log_info "创建后端服务文件..."
    
    # 认证服务
    log_info "创建认证服务..."
    cat > "services/auth-service/main.go" << 'EOF'
package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

type HealthResponse struct {
	Status    string `json:"status"`
	Service   string `json:"service"`
	Timestamp string `json:"timestamp"`
	Version   string `json:"version"`
}

type StatsResponse struct {
	Service      string `json:"service"`
	Uptime       string `json:"uptime"`
	RequestCount int    `json:"requests_count"`
	Status       string `json:"status"`
}

var (
	startTime    = time.Now()
	requestCount = 0
)

func main() {
	// 设置 Gin 模式
	gin.SetMode(gin.ReleaseMode)
	
	// 创建路由
	r := gin.Default()
	
	// 添加 CORS 中间件
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	
	// 请求计数中间件
	r.Use(func(c *gin.Context) {
		requestCount++
		c.Next()
	})
	
	// 根路径
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "欢迎使用生科云码平台认证服务",
			"service": "auth-service",
			"version": "1.0.0",
			"docs":    "/docs",
		})
	})
	
	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		response := HealthResponse{
			Status:    "healthy",
			Service:   "auth-service",
			Timestamp: time.Now().Format(time.RFC3339),
			Version:   "1.0.0",
		}
		c.JSON(http.StatusOK, response)
	})
	
	// 统计信息
	r.GET("/api/v1/stats", func(c *gin.Context) {
		uptime := time.Since(startTime)
		response := StatsResponse{
			Service:      "auth-service",
			Uptime:       uptime.String(),
			RequestCount: requestCount,
			Status:       "running",
		}
		c.JSON(http.StatusOK, response)
	})
	
	// API 路由组
	api := r.Group("/api/v1")
	{
		// 认证相关路由
		auth := api.Group("/auth")
		{
			auth.POST("/register", handleRegister)
			auth.POST("/login", handleLogin)
			auth.POST("/logout", handleLogout)
			auth.GET("/profile", handleProfile)
		}
	}
	
	// 启动服务
	port := ":8001"
	log.Printf("认证服务启动在端口 %s", port)
	log.Fatal(http.ListenAndServe(port, r))
}

func handleRegister(c *gin.Context) {
	// 模拟注册逻辑
	c.JSON(http.StatusOK, gin.H{
		"message": "注册功能开发中",
		"status":  "success",
	})
}

func handleLogin(c *gin.Context) {
	// 模拟登录逻辑
	c.JSON(http.StatusOK, gin.H{
		"message": "登录功能开发中",
		"status":  "success",
	})
}

func handleLogout(c *gin.Context) {
	// 模拟登出逻辑
	c.JSON(http.StatusOK, gin.H{
		"message": "登出功能开发中",
		"status":  "success",
	})
}

func handleProfile(c *gin.Context) {
	// 模拟用户信息获取
	c.JSON(http.StatusOK, gin.H{
		"message": "用户信息功能开发中",
		"status":  "success",
	})
}
EOF
    
    # 创建 go.mod
    cat > "services/auth-service/go.mod" << 'EOF'
module auth-service

go 1.21

require (
	github.com/gin-contrib/cors v1.4.0
	github.com/gin-gonic/gin v1.9.1
)
EOF
    
    # 订阅服务
    log_info "创建订阅服务..."
    cat > "services/subscription-service/main.go" << 'EOF'
package main

import (
	"log"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

type HealthResponse struct {
	Status    string `json:"status"`
	Service   string `json:"service"`
	Timestamp string `json:"timestamp"`
	Version   string `json:"version"`
}

var (
	startTime    = time.Now()
	requestCount = 0
)

func main() {
	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()
	
	// CORS 中间件
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	
	r.Use(func(c *gin.Context) {
		requestCount++
		c.Next()
	})
	
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "欢迎使用生科云码平台订阅服务",
			"service": "subscription-service",
			"version": "1.0.0",
		})
	})
	
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, HealthResponse{
			Status:    "healthy",
			Service:   "subscription-service",
			Timestamp: time.Now().Format(time.RFC3339),
			Version:   "1.0.0",
		})
	})
	
	api := r.Group("/api/v1")
	{
		api.GET("/products", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "产品列表功能开发中",
				"status":  "success",
			})
		})
	}
	
	port := ":8002"
	log.Printf("订阅服务启动在端口 %s", port)
	log.Fatal(http.ListenAndServe(port, r))
}
EOF
    
    cat > "services/subscription-service/go.mod" << 'EOF'
module subscription-service

go 1.21

require (
	github.com/gin-contrib/cors v1.4.0
	github.com/gin-gonic/gin v1.9.1
)
EOF
    
    log_success "后端服务创建完成"
}

# 创建前端应用
create_frontend_apps() {
    log_info "创建前端应用..."
    
    # 主站门户
    log_info "创建主站门户..."
    cat > "web-apps/main-portal/package.json" << 'EOF'
{
  "name": "biocloude-main-portal",
  "version": "1.0.0",
  "description": "生科云码平台主站门户",
  "scripts": {
    "dev": "next dev -p 3000",
    "build": "next build",
    "start": "next start -p 3000",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "18.2.0",
    "react-dom": "18.2.0"
  },
  "devDependencies": {
    "@types/node": "20.8.0",
    "@types/react": "18.2.0",
    "@types/react-dom": "18.2.0",
    "eslint": "8.51.0",
    "eslint-config-next": "14.0.0",
    "typescript": "5.2.0"
  }
}
EOF
    
    # 创建简单的 Next.js 页面
    mkdir -p web-apps/main-portal/pages
    cat > "web-apps/main-portal/pages/index.js" << 'EOF'
export default function Home() {
  return (
    <div style={{ padding: '2rem', fontFamily: 'Arial, sans-serif' }}>
      <h1>🧬 生科云码平台</h1>
      <p>欢迎使用生科云码平台主站门户</p>
      <p>🚧 开发中...</p>
      <div>
        <h2>AI 服务</h2>
        <ul>
          <li><a href="http://localhost:3001">📰 资讯处理AI</a></li>
          <li><a href="http://localhost:3002">📚 文献阅读AI</a></li>
        </ul>
      </div>
    </div>
  )
}
EOF
    
    # 资讯处理AI应用
    log_info "创建资讯处理AI应用..."
    cat > "web-apps/newsletter-app/package.json" << 'EOF'
{
  "name": "biocloude-newsletter-app",
  "version": "1.0.0",
  "description": "生科云码平台资讯处理AI应用",
  "scripts": {
    "dev": "next dev -p 3001",
    "build": "next build",
    "start": "next start -p 3001"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "18.2.0",
    "react-dom": "18.2.0"
  }
}
EOF
    
    mkdir -p web-apps/newsletter-app/pages
    cat > "web-apps/newsletter-app/pages/index.js" << 'EOF'
export default function NewsletterApp() {
  return (
    <div style={{ padding: '2rem', fontFamily: 'Arial, sans-serif' }}>
      <h1>📰 资讯处理AI</h1>
      <p>生物科技资讯智能处理平台</p>
      <p>🚧 开发中...</p>
    </div>
  )
}
EOF
    
    # 文献阅读AI应用
    log_info "创建文献阅读AI应用..."
    cat > "web-apps/scholar-app/package.json" << 'EOF'
{
  "name": "biocloude-scholar-app",
  "version": "1.0.0",
  "description": "生科云码平台文献阅读AI应用",
  "scripts": {
    "dev": "next dev -p 3002",
    "build": "next build",
    "start": "next start -p 3002"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "18.2.0",
    "react-dom": "18.2.0"
  }
}
EOF
    
    mkdir -p web-apps/scholar-app/pages
    cat > "web-apps/scholar-app/pages/index.js" << 'EOF'
export default function ScholarApp() {
  return (
    <div style={{ padding: '2rem', fontFamily: 'Arial, sans-serif' }}>
      <h1>📚 文献阅读AI</h1>
      <p>生物科技文献智能阅读平台</p>
      <p>🚧 开发中...</p>
    </div>
  )
}
EOF
    
    log_success "前端应用创建完成"
}

# 主函数
main() {
    log_info "🏗️ 开始创建生科云码平台项目结构..."
    
    create_directories
    create_ai_services
    create_backend_services
    create_frontend_apps
    
    log_success "🎉 项目结构创建完成！"
    
    echo ""
    echo "📋 创建的内容："
    echo "  📁 services/auth-service/ - 认证服务 (Go)"
    echo "  📁 services/subscription-service/ - 订阅服务 (Go)"
    echo "  📁 services/ai-services/ - 6个AI服务 (Python)"
    echo "  📁 web-apps/ - 3个前端应用 (Next.js)"
    echo ""
    echo "🚀 下一步："
    echo "  1. 安装依赖: 脚本会自动处理"
    echo "  2. 启动服务: ./start-dev.sh"
    echo "  3. 访问应用: http://localhost:3000"
    echo ""
    echo "💡 提示: 所有服务都是基础框架，具体功能需要进一步开发"
}

# 执行主函数
main "$@"
