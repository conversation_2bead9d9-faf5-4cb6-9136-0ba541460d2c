// 新闻文章类型定义
export interface NewsArticle {
  id: string | number
  title: string
  summary: string
  content?: string
  category: string
  source: string
  author?: string
  publishTime: string
  priority: 'high' | 'medium' | 'low'
  tags: string[]
  readTime: string
  image?: string
  url: string
  views?: number
  likes?: number
  bookmarked?: boolean
}

// 新闻分类
export interface NewsCategory {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  count?: number
}

// 用户订阅配置
export interface UserSubscription {
  id: string
  userId: string
  keywords: string[]
  categories: string[]
  sources: string[]
  frequency: 'realtime' | 'hourly' | 'daily' | 'weekly'
  emailNotification: boolean
  smsNotification: boolean
  pushNotification: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 数据源配置
export interface DataSource {
  id: string
  name: string
  url: string
  type: 'rss' | 'api' | 'scraper'
  category: string
  status: 'active' | 'inactive' | 'error'
  lastUpdate: string
  articleCount: number
  updateFrequency: number // 分钟
  priority: number
  config?: Record<string, any>
}

// 用户信息
export interface User {
  id: string
  email: string
  username?: string
  avatar?: string
  plan: 'free' | 'pro' | 'enterprise'
  subscriptions: UserSubscription[]
  preferences: UserPreferences
  createdAt: string
  lastLoginAt: string
}

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  timezone: string
  emailDigest: boolean
  digestFrequency: 'daily' | 'weekly'
  digestTime: string // HH:mm format
  autoMarkAsRead: boolean
  showImages: boolean
  compactView: boolean
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// 搜索参数
export interface SearchParams {
  query?: string
  category?: string
  source?: string
  tags?: string[]
  dateFrom?: string
  dateTo?: string
  priority?: string
  sortBy?: 'date' | 'relevance' | 'popularity'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// 统计数据
export interface Statistics {
  totalArticles: number
  todayArticles: number
  activeSubscriptions: number
  filterAccuracy: number
  pushAccuracy: number
  topCategories: Array<{
    category: string
    count: number
    percentage: number
  }>
  topSources: Array<{
    source: string
    count: number
    percentage: number
  }>
  dailyStats: Array<{
    date: string
    articles: number
    reads: number
    clicks: number
  }>
}

// 通知类型
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
  actionUrl?: string
  actionText?: string
}

// AI分析结果
export interface AIAnalysis {
  sentiment: 'positive' | 'negative' | 'neutral'
  confidence: number
  keywords: Array<{
    word: string
    score: number
    category?: string
  }>
  summary: string
  relevanceScore: number
  trendingScore: number
  categories: Array<{
    category: string
    confidence: number
  }>
}

// 过滤器配置
export interface FilterConfig {
  id: string
  name: string
  description?: string
  rules: FilterRule[]
  isActive: boolean
  priority: number
  createdAt: string
  updatedAt: string
}

export interface FilterRule {
  field: 'title' | 'content' | 'source' | 'category' | 'tags'
  operator: 'contains' | 'equals' | 'startsWith' | 'endsWith' | 'regex'
  value: string
  caseSensitive: boolean
  action: 'include' | 'exclude' | 'highlight' | 'priority'
}

// 导出配置
export interface ExportConfig {
  format: 'json' | 'csv' | 'xml' | 'pdf'
  fields: string[]
  filters: SearchParams
  dateRange: {
    from: string
    to: string
  }
}

// Webhook配置
export interface WebhookConfig {
  id: string
  name: string
  url: string
  events: string[]
  headers?: Record<string, string>
  isActive: boolean
  secret?: string
  retryCount: number
  timeout: number
  createdAt: string
  lastTriggered?: string
}

// 分析报告
export interface AnalyticsReport {
  id: string
  title: string
  type: 'daily' | 'weekly' | 'monthly' | 'custom'
  dateRange: {
    from: string
    to: string
  }
  metrics: {
    totalArticles: number
    uniqueSources: number
    topCategories: Array<{ name: string; count: number }>
    topKeywords: Array<{ word: string; frequency: number }>
    sentimentDistribution: {
      positive: number
      negative: number
      neutral: number
    }
    engagementMetrics: {
      views: number
      clicks: number
      shares: number
      bookmarks: number
    }
  }
  generatedAt: string
}
