# 🧬 生科云码 (BioCloude) - AI+生物科技创新平台

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-1.0.0-green.svg)](package.json)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/biocloude/platform)

> 专注于"AI+生物科技"创新应用开发，为生物科学及其相关领域构建以客户为中心的AI自动化工作流

## 🌟 项目概述

生科云码是一个专业的生物科技AI平台，提供6个AI即服务产品，覆盖从资讯处理到基因编辑的全流程AI解决方案。

### 🎯 核心产品

1. **📰 资讯处理AI** - 智能简讯订阅过滤配置系统
2. **📚 文献阅读AI** - 基于Semantic Scholar API的智能文献检索
3. **🧬 引物AI综合体** - 分子生物学引物全生命周期AI系统
4. **🔬 蛋白质设计AI** - 从头设计具有特定功能的新型蛋白质
5. **✂️ 基因编辑AI** - 智能设计CRISPR gRNAs和基因编辑工具
6. **⚗️ 代谢工程AI** - 设计和优化微生物细胞工厂的代谢途径

## 🏗️ 技术架构

### 前端技术栈
- **框架**: React 18 + Next.js 15
- **样式**: Tailwind CSS + Radix UI
- **状态管理**: Zustand
- **数据获取**: TanStack Query
- **表单处理**: React Hook Form + Zod

### 后端技术栈
- **语言**: Go (Gin) + Python (FastAPI)
- **数据库**: PostgreSQL + Redis
- **消息队列**: Redis Streams
- **AI/ML**: OpenAI GPT + Transformers + spaCy
- **认证**: JWT + 腾讯云CIAM

### 基础设施
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes (腾讯云TKE)
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **CI/CD**: GitHub Actions

## 🚀 快速开始

### ⚡ 遇到脚本执行问题？

如果遇到 `./start-dev.sh: No such file or directory` 错误：

```bash
# 一键修复
chmod +x *.sh && ./start-dev.sh

# 或使用 bash 运行
bash start-dev.sh
```

📖 详细解决方案请查看 [QUICK_FIX.md](QUICK_FIX.md)

### 🌐 在线开发 (推荐)

**使用 GitHub Codespaces 一键启动完整开发环境：**

1. 点击仓库页面的 **Code** 按钮
2. 选择 **Codespaces** 标签
3. 点击 **Create codespace on main**
4. 等待环境自动配置完成 (约3-5分钟)
5. 运行 `./start-dev.sh` 启动所有服务

**Codespaces 包含：**
- ✅ 完整的开发环境 (Node.js, Go, Python)
- ✅ 预配置的数据库和缓存服务
- ✅ VS Code 扩展和调试配置
- ✅ 监控和日志工具
- ✅ 一键启动脚本

> 📖 详细说明请查看 [.devcontainer/README.md](.devcontainer/README.md)

### 💻 本地开发

#### 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+
- Go 1.21+
- Python 3.11+
- kubectl (可选，用于Kubernetes部署)

#### 本地安装

1. **克隆项目**
```bash
git clone https://github.com/biocloude/platform.git
cd platform
```

2. **启动开发环境**
```bash
# 启动基础设施
docker-compose up -d postgres redis

# 启动后端服务
cd services/auth-service && go run main.go &
cd services/subscription-service && go run main.go &
cd services/ai-services/newsletter-ai && python -m uvicorn app.main:app --reload &

# 启动前端应用
cd web-apps/main-portal && npm install && npm run dev &
cd web-apps/newsletter-app && npm install && npm run dev &
```

3. **访问应用**
- 主站门户: http://localhost:3000
- 资讯处理AI: http://localhost:3001
- API文档: http://localhost:9001/docs

### 生产部署

1. **使用部署脚本**
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

2. **手动部署**
```bash
# 构建镜像
docker-compose build

# 部署到Kubernetes
kubectl apply -f k8s/infrastructure/
kubectl apply -f k8s/deployments/
```

## 📁 项目结构

```
biocloude/
├── web-apps/                    # 前端应用
│   ├── main-portal/            # 主站门户 (www.biocloude.cn)
│   └── newsletter-app/         # 资讯处理AI (ivdnewsletter.biocloude.cn)
├── services/                   # 后端服务
│   ├── auth-service/          # 用户认证服务 (Go)
│   ├── subscription-service/  # 订阅管理服务 (Go)
│   └── ai-services/           # AI服务
│       └── newsletter-ai/     # 资讯处理AI (Python)
├── shared/                    # 共享代码
│   ├── proto/                # gRPC协议定义
│   └── utils/                # 工具函数
├── database/                 # 数据库相关
│   ├── migrations/          # 数据库迁移
│   └── seeds/              # 初始数据
├── k8s/                     # Kubernetes配置
│   ├── infrastructure/     # 基础设施
│   ├── deployments/       # 应用部署
│   └── monitoring/        # 监控配置
├── scripts/                # 部署和工具脚本
├── docs/                  # 项目文档
└── docker-compose.yml    # 本地开发环境
```

## 🧪 测试

### 运行测试
```bash
# 运行集成测试
chmod +x scripts/test.sh
./scripts/test.sh

# 运行单元测试
cd services/auth-service && go test ./...
cd services/ai-services/newsletter-ai && python -m pytest
```

### 测试覆盖
- ✅ 健康检查测试
- ✅ 认证功能测试
- ✅ 订阅功能测试
- ✅ AI功能测试
- ✅ 数据库连接测试
- ✅ 性能测试
- ✅ 安全测试
- ✅ 监控测试

## 📊 监控和日志

### 监控指标
- 应用性能指标 (APM)
- 业务指标 (用户活跃度、订阅转化率)
- 基础设施指标 (CPU、内存、网络)
- AI模型指标 (准确率、响应时间)

### 日志管理
- 结构化日志 (JSON格式)
- 集中式日志收集 (ELK Stack)
- 日志级别管理
- 敏感信息脱敏

## 🔒 安全

### 安全措施
- JWT令牌认证
- HTTPS强制重定向
- CORS配置
- 输入验证和SQL注入防护
- 限流和DDoS防护
- 敏感数据加密存储

### 合规性
- GDPR数据保护
- 医疗数据安全标准
- API访问审计日志

## 🌐 API文档

### 主要API端点

#### 认证服务 (auth-service)
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/refresh` - 刷新令牌

#### 订阅服务 (subscription-service)
- `GET /api/v1/products` - 获取产品列表
- `POST /api/v1/subscriptions` - 创建订阅
- `GET /api/v1/subscriptions` - 获取用户订阅
- `PUT /api/v1/subscriptions/:id` - 更新订阅配置

#### AI服务 (newsletter-ai)
- `GET /api/v1/articles` - 获取文章列表
- `POST /api/v1/classify` - 文章分类
- `POST /api/v1/extract-keywords` - 关键词提取
- `GET /api/v1/stats` - 获取统计信息

## 🤝 贡献指南

### 开发流程
1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

### 代码规范
- Go: 遵循Go官方代码规范
- Python: 遵循PEP 8规范
- TypeScript: 使用ESLint + Prettier
- 提交信息: 遵循Conventional Commits规范

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- **官网**: https://www.biocloude.cn
- **邮箱**: <EMAIL>
- **技术支持**: <EMAIL>
- **商务合作**: <EMAIL>

## 🙏 致谢

感谢以下开源项目和技术社区的支持：
- [Next.js](https://nextjs.org/) - React框架
- [Gin](https://gin-gonic.com/) - Go Web框架
- [FastAPI](https://fastapi.tiangolo.com/) - Python API框架
- [PostgreSQL](https://www.postgresql.org/) - 数据库
- [Redis](https://redis.io/) - 缓存和消息队列
- [Kubernetes](https://kubernetes.io/) - 容器编排

---

<div align="center">
  <strong>🧬 生科云码 - 让AI赋能生物科技创新 🚀</strong>
</div>
