package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/robfig/cron/v3"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"github.com/biocloude/platform/services/subscription-service/internal/config"
	"github.com/biocloude/platform/services/subscription-service/internal/database"
	"github.com/biocloude/platform/services/subscription-service/internal/handlers"
	"github.com/biocloude/platform/services/subscription-service/internal/middleware"
	"github.com/biocloude/platform/services/subscription-service/internal/repository"
	"github.com/biocloude/platform/services/subscription-service/internal/service"
	pb "github.com/biocloude/platform/shared/proto/subscription"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		logrus.Warn("未找到.env文件，使用系统环境变量")
	}

	// 初始化配置
	cfg := config.Load()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.LogLevel)
	if err != nil {
		level = logrus.InfoLevel
	}
	logrus.SetLevel(level)
	logrus.SetFormatter(&logrus.JSONFormatter{})

	logrus.Info("🚀 启动生科云码订阅管理服务...")

	// 初始化数据库
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		logrus.Fatalf("❌ 数据库初始化失败: %v", err)
	}
	logrus.Info("✅ 数据库连接成功")

	// 初始化Redis
	redisClient, err := database.InitializeRedis(cfg.RedisURL)
	if err != nil {
		logrus.Fatalf("❌ Redis初始化失败: %v", err)
	}
	logrus.Info("✅ Redis连接成功")

	// 初始化仓库层
	productRepo := repository.NewProductRepository(db)
	subscriptionRepo := repository.NewSubscriptionRepository(db)
	orderRepo := repository.NewOrderRepository(db)
	usageRepo := repository.NewUsageRepository(db)
	invitationRepo := repository.NewInvitationRepository(db)

	// 初始化服务层
	productService := service.NewProductService(productRepo, cfg)
	subscriptionService := service.NewSubscriptionService(subscriptionRepo, productRepo, orderRepo, cfg)
	usageService := service.NewUsageService(usageRepo, cfg)
	invitationService := service.NewInvitationService(invitationRepo, cfg)
	billingService := service.NewBillingService(subscriptionRepo, orderRepo, usageRepo, cfg)

	// 初始化处理器
	productHandler := handlers.NewProductHandler(productService)
	subscriptionHandler := handlers.NewSubscriptionHandler(subscriptionService, productService)
	usageHandler := handlers.NewUsageHandler(usageService)
	invitationHandler := handlers.NewInvitationHandler(invitationService)
	billingHandler := handlers.NewBillingHandler(billingService)

	// 启动定时任务
	startCronJobs(subscriptionService, billingService)

	// 启动gRPC服务器
	go startGRPCServer(subscriptionService, cfg.GRPCPort)

	// 启动HTTP服务器
	startHTTPServer(
		productHandler,
		subscriptionHandler,
		usageHandler,
		invitationHandler,
		billingHandler,
		cfg.HTTPPort,
	)
}

func startGRPCServer(subscriptionService *service.SubscriptionService, port int) {
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		logrus.Fatalf("❌ gRPC服务器监听失败: %v", err)
	}

	s := grpc.NewServer()
	pb.RegisterSubscriptionServiceServer(s, subscriptionService)

	logrus.Infof("🔗 gRPC服务器启动在端口 %d", port)
	if err := s.Serve(lis); err != nil {
		logrus.Fatalf("❌ gRPC服务器启动失败: %v", err)
	}
}

func startHTTPServer(
	productHandler *handlers.ProductHandler,
	subscriptionHandler *handlers.SubscriptionHandler,
	usageHandler *handlers.UsageHandler,
	invitationHandler *handlers.InvitationHandler,
	billingHandler *handlers.BillingHandler,
	port int,
) {
	// 设置Gin模式
	if os.Getenv("ENVIRONMENT") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// 中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())
	router.Use(middleware.RequestID())

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "subscription-service",
			"timestamp": time.Now().UTC(),
			"version":   "1.0.0",
		})
	})

	// API路由
	v1 := router.Group("/api/v1")
	{
		// 产品管理
		products := v1.Group("/products")
		{
			products.GET("", productHandler.ListProducts)
			products.GET("/:id", productHandler.GetProduct)
			products.POST("", middleware.AuthRequired(), productHandler.CreateProduct)
			products.PUT("/:id", middleware.AuthRequired(), productHandler.UpdateProduct)
			products.DELETE("/:id", middleware.AuthRequired(), productHandler.DeleteProduct)
			products.GET("/:id/pricing-plans", productHandler.GetPricingPlans)
		}

		// 订阅管理
		subscriptions := v1.Group("/subscriptions")
		subscriptions.Use(middleware.AuthRequired())
		{
			subscriptions.GET("", subscriptionHandler.ListSubscriptions)
			subscriptions.GET("/:id", subscriptionHandler.GetSubscription)
			subscriptions.POST("", subscriptionHandler.CreateSubscription)
			subscriptions.PUT("/:id", subscriptionHandler.UpdateSubscription)
			subscriptions.DELETE("/:id", subscriptionHandler.CancelSubscription)
			subscriptions.POST("/:id/renew", subscriptionHandler.RenewSubscription)
			subscriptions.GET("/:id/history", subscriptionHandler.GetSubscriptionHistory)
			
			// 资讯订阅配置
			subscriptions.GET("/:id/newsletter-config", subscriptionHandler.GetNewsletterConfig)
			subscriptions.PUT("/:id/newsletter-config", subscriptionHandler.UpdateNewsletterConfig)
		}

		// API使用统计
		usage := v1.Group("/usage")
		usage.Use(middleware.AuthRequired())
		{
			usage.POST("/record", usageHandler.RecordUsage)
			usage.GET("/stats", usageHandler.GetUsageStats)
			usage.GET("/daily", usageHandler.GetDailyUsage)
			usage.GET("/monthly", usageHandler.GetMonthlyUsage)
		}

		// 邀请返佣
		invitations := v1.Group("/invitations")
		invitations.Use(middleware.AuthRequired())
		{
			invitations.GET("", invitationHandler.ListInvitations)
			invitations.POST("", invitationHandler.CreateInvitation)
			invitations.GET("/:code", invitationHandler.GetInvitationByCode)
			invitations.POST("/:code/accept", invitationHandler.AcceptInvitation)
		}

		// 计费管理
		billing := v1.Group("/billing")
		billing.Use(middleware.AuthRequired())
		{
			billing.GET("/invoices", billingHandler.ListInvoices)
			billing.GET("/invoices/:id", billingHandler.GetInvoice)
			billing.POST("/invoices/:id/pay", billingHandler.PayInvoice)
			billing.GET("/usage-billing", billingHandler.GetUsageBilling)
		}

		// 管理员接口
		admin := v1.Group("/admin")
		admin.Use(middleware.AuthRequired(), middleware.AdminRequired())
		{
			admin.GET("/subscriptions", subscriptionHandler.AdminListSubscriptions)
			admin.GET("/revenue", billingHandler.GetRevenueStats)
			admin.GET("/metrics", subscriptionHandler.GetMetrics)
		}
	}

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: router,
	}

	// 启动服务器
	go func() {
		logrus.Infof("🌐 HTTP服务器启动在端口 %d", port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("❌ HTTP服务器启动失败: %v", err)
		}
	}()

	// 优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logrus.Info("🔄 正在关闭服务器...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logrus.Fatalf("❌ 服务器强制关闭: %v", err)
	}

	logrus.Info("✅ 服务器已关闭")
}

func startCronJobs(subscriptionService *service.SubscriptionService, billingService *service.BillingService) {
	c := cron.New()

	// 每天检查订阅过期
	c.AddFunc("0 0 * * *", func() {
		logrus.Info("🔄 开始检查订阅过期...")
		if err := subscriptionService.CheckExpiredSubscriptions(); err != nil {
			logrus.Errorf("检查订阅过期失败: %v", err)
		}
	})

	// 每天生成账单
	c.AddFunc("0 1 * * *", func() {
		logrus.Info("🔄 开始生成每日账单...")
		if err := billingService.GenerateDailyBills(); err != nil {
			logrus.Errorf("生成每日账单失败: %v", err)
		}
	})

	// 每月生成月度报告
	c.AddFunc("0 2 1 * *", func() {
		logrus.Info("🔄 开始生成月度报告...")
		if err := billingService.GenerateMonthlyReports(); err != nil {
			logrus.Errorf("生成月度报告失败: %v", err)
		}
	})

	c.Start()
	logrus.Info("✅ 定时任务启动完成")
}
