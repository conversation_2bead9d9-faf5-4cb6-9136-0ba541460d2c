package main

import (
	"log"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

type HealthResponse struct {
	Status    string `json:"status"`
	Service   string `json:"service"`
	Timestamp string `json:"timestamp"`
	Version   string `json:"version"`
}

var (
	startTime    = time.Now()
	requestCount = 0
)

func main() {
	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()
	
	// CORS 中间件
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	
	r.Use(func(c *gin.Context) {
		requestCount++
		c.Next()
	})
	
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "欢迎使用生科云码平台订阅服务",
			"service": "subscription-service",
			"version": "1.0.0",
		})
	})
	
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, HealthResponse{
			Status:    "healthy",
			Service:   "subscription-service",
			Timestamp: time.Now().Format(time.RFC3339),
			Version:   "1.0.0",
		})
	})
	
	api := r.Group("/api/v1")
	{
		api.GET("/products", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "产品列表功能开发中",
				"status":  "success",
			})
		})
	}
	
	port := ":8002"
	log.Printf("订阅服务启动在端口 %s", port)
	log.Fatal(http.ListenAndServe(port, r))
}
