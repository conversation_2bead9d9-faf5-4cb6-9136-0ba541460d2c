import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'IVD Newsletter AI - 生物医学资讯智能处理平台',
  description: '专为生物医学领域打造的智能资讯处理平台，提供精准的资讯筛选、个性化推送和深度分析服务',
  keywords: ['生物医学', '资讯处理', 'AI', '智能推送', 'IVD', '医疗器械', '生物科技'],
  authors: [{ name: '生科云码' }],
  creator: '生科云码',
  publisher: '生科云码',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://ivdnewsletter.biocloude.cn'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'IVD Newsletter AI - 生物医学资讯智能处理平台',
    description: '专为生物医学领域打造的智能资讯处理平台，提供精准的资讯筛选、个性化推送和深度分析服务',
    url: 'https://ivdnewsletter.biocloude.cn',
    siteName: 'IVD Newsletter AI',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'IVD Newsletter AI',
      },
    ],
    locale: 'zh_CN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'IVD Newsletter AI - 生物医学资讯智能处理平台',
    description: '专为生物医学领域打造的智能资讯处理平台',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#0ea5e9" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="IVD Newsletter AI" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#0ea5e9" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
      </head>
      <body className={`${inter.className} antialiased bg-gradient-to-br from-bio-50 via-white to-medical-50 min-h-screen`}>
        <div className="relative">
          {/* 背景装饰 */}
          <div className="fixed inset-0 -z-10 overflow-hidden">
            <div className="absolute -top-40 -right-32 w-96 h-96 bg-bio-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-32 w-96 h-96 bg-medical-200/20 rounded-full blur-3xl"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-science-200/10 rounded-full blur-3xl"></div>
          </div>
          
          {children}
        </div>
      </body>
    </html>
  )
}
