# 🚀 生科云码平台部署指南

## 📋 部署前检查清单

### 环境要求
- [x] Kubernetes 1.25+
- [x] Docker 20.10+
- [x] He<PERSON> 3.0+
- [x] kubectl 配置完成
- [x] 域名DNS配置完成
- [x] SSL证书准备就绪

### 服务清单
- [x] **主站门户** (www.biocloude.cn)
- [x] **资讯处理AI** (ivdnewsletter.biocloude.cn)
- [x] **文献阅读AI** (scholar.biocloude.cn)
- [x] **引物AI综合体** (primer.biocloude.cn)
- [x] **蛋白质设计AI** (protein.biocloude.cn)
- [x] **基因编辑AI** (geneedit.biocloude.cn)
- [x] **代谢工程AI** (metabolic.biocloude.cn)

### 后端服务
- [x] **用户认证服务** (auth-service)
- [x] **订阅管理服务** (subscription-service)
- [x] **6个AI微服务** (newsletter-ai, scholar-ai, primer-ai, protein-ai, gene-editing-ai, metabolic-ai)

## 🔧 快速部署

### 1. 一键部署脚本
```bash
# 克隆项目
git clone https://github.com/biocloude/platform.git
cd platform

# 执行一键部署
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

### 2. 手动部署步骤

#### 步骤1: 准备基础设施
```bash
# 创建命名空间和配置
kubectl apply -f k8s/infrastructure/namespace.yaml

# 部署数据库
kubectl apply -f k8s/infrastructure/postgres.yaml
kubectl apply -f k8s/infrastructure/redis.yaml

# 等待基础设施就绪
kubectl wait --for=condition=ready pod -l app=postgres -n biocloude --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n biocloude --timeout=300s
```

#### 步骤2: 数据库初始化
```bash
# 运行数据库迁移
kubectl apply -f k8s/jobs/migration-job.yaml
kubectl wait --for=condition=complete job/database-migration -n biocloude --timeout=300s
```

#### 步骤3: 部署后端服务
```bash
# 部署认证服务
kubectl apply -f k8s/deployments/auth-service-deployment.yaml

# 部署订阅服务
kubectl apply -f k8s/deployments/subscription-service-deployment.yaml

# 部署AI服务
kubectl apply -f k8s/deployments/newsletter-ai-deployment.yaml
kubectl apply -f k8s/deployments/all-ai-services-deployment.yaml

# 等待服务就绪
kubectl wait --for=condition=available deployment/auth-service -n biocloude --timeout=300s
kubectl wait --for=condition=available deployment/subscription-service -n biocloude --timeout=300s
```

#### 步骤4: 部署前端应用
```bash
# 部署主站门户
kubectl apply -f k8s/deployments/main-portal-deployment.yaml

# 部署资讯处理AI应用
kubectl apply -f k8s/deployments/newsletter-app-deployment.yaml

# 等待前端应用就绪
kubectl wait --for=condition=available deployment/main-portal -n biocloude --timeout=300s
kubectl wait --for=condition=available deployment/newsletter-app -n biocloude --timeout=300s
```

## 🧪 测试验证

### 1. 自动化测试
```bash
# 运行集成测试
chmod +x scripts/test.sh
./scripts/test.sh

# 运行性能测试
chmod +x scripts/performance_test.sh
./scripts/performance_test.sh --concurrent 100 --duration 300
```

### 2. 手动验证

#### 健康检查
```bash
# 检查所有Pod状态
kubectl get pods -n biocloude

# 检查服务状态
kubectl get services -n biocloude

# 检查Ingress状态
kubectl get ingress -n biocloude
```

#### API测试
```bash
# 测试认证服务
curl -f https://api.biocloude.cn/auth/health

# 测试订阅服务
curl -f https://api.biocloude.cn/subscription/health

# 测试AI服务
curl -f https://api.biocloude.cn/ai/health
```

#### 前端测试
```bash
# 测试主站
curl -f https://www.biocloude.cn

# 测试资讯处理AI
curl -f https://ivdnewsletter.biocloude.cn
```

## 📊 监控配置

### 1. Prometheus监控
```bash
# 安装Prometheus
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm install prometheus prometheus-community/kube-prometheus-stack -n biocloude
```

### 2. 日志收集
```bash
# 安装ELK Stack
helm repo add elastic https://helm.elastic.co
helm install elasticsearch elastic/elasticsearch -n biocloude
helm install kibana elastic/kibana -n biocloude
helm install filebeat elastic/filebeat -n biocloude
```

## 🔒 安全配置

### 1. 网络安全
- [x] HTTPS强制重定向
- [x] CORS配置
- [x] 限流配置
- [x] 防火墙规则

### 2. 数据安全
- [x] 数据库加密
- [x] 敏感信息脱敏
- [x] 备份策略
- [x] 访问控制

## 🎯 性能优化

### 1. 缓存策略
- [x] Redis缓存配置
- [x] CDN加速
- [x] 数据库查询优化
- [x] 静态资源压缩

### 2. 扩容配置
```bash
# 水平扩容示例
kubectl scale deployment/auth-service --replicas=5 -n biocloude
kubectl scale deployment/newsletter-ai --replicas=3 -n biocloude
```

## 🔄 CI/CD流水线

### GitHub Actions配置
- [x] 自动化测试
- [x] 代码质量检查
- [x] 安全扫描
- [x] 自动部署
- [x] 性能测试

### 部署流程
1. **代码提交** → 触发CI/CD
2. **自动测试** → 单元测试、集成测试
3. **构建镜像** → Docker镜像构建和推送
4. **部署到K8s** → 滚动更新部署
5. **验证测试** → 自动化验证
6. **通知结果** → Slack通知

## 📈 运维管理

### 1. 日常监控
```bash
# 查看资源使用情况
kubectl top pods -n biocloude
kubectl top nodes

# 查看日志
kubectl logs -f deployment/auth-service -n biocloude
kubectl logs -f deployment/newsletter-ai -n biocloude
```

### 2. 故障排查
```bash
# 检查Pod状态
kubectl describe pod <pod-name> -n biocloude

# 查看事件
kubectl get events -n biocloude --sort-by='.lastTimestamp'

# 进入容器调试
kubectl exec -it <pod-name> -n biocloude -- /bin/bash
```

### 3. 备份恢复
```bash
# 数据库备份
kubectl exec -it postgres-pod -n biocloude -- pg_dump -U biocloude biocloude > backup.sql

# 配置备份
kubectl get configmap -n biocloude -o yaml > configmap-backup.yaml
kubectl get secret -n biocloude -o yaml > secret-backup.yaml
```

## 🌐 域名配置

### DNS记录配置
```
# A记录
www.biocloude.cn        → Load Balancer IP
biocloude.cn           → Load Balancer IP
ivdnewsletter.biocloude.cn → Load Balancer IP
scholar.biocloude.cn   → Load Balancer IP
primer.biocloude.cn    → Load Balancer IP
protein.biocloude.cn   → Load Balancer IP
geneedit.biocloude.cn  → Load Balancer IP
metabolic.biocloude.cn → Load Balancer IP

# CNAME记录
api.biocloude.cn       → Load Balancer FQDN
```

## 📞 技术支持

### 联系方式
- **技术支持**: <EMAIL>
- **运维支持**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx

### 文档资源
- **API文档**: https://docs.biocloude.cn
- **用户手册**: https://help.biocloude.cn
- **开发者指南**: https://dev.biocloude.cn

## ✅ 部署完成检查

- [ ] 所有服务健康检查通过
- [ ] 前端应用正常访问
- [ ] API接口响应正常
- [ ] 数据库连接正常
- [ ] 缓存服务正常
- [ ] 监控系统正常
- [ ] 日志收集正常
- [ ] SSL证书有效
- [ ] 域名解析正确
- [ ] 性能测试通过

---

🎉 **恭喜！生科云码平台部署完成！**

平台现已准备好为全球生物科技研究者提供专业的AI服务。
