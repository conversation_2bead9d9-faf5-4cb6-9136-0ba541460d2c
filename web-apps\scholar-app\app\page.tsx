'use client'

import { useState } from 'react'
import { Search, BookOpen, TrendingUp, Users, BarChart3, Network, FileText, Star } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export default function ScholarHomePage() {
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = () => {
    if (searchQuery.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const features = [
    {
      icon: <Search className="h-8 w-8 text-blue-600" />,
      title: "智能文献搜索",
      description: "基于Semantic Scholar的强大搜索引擎，快速找到相关文献"
    },
    {
      icon: <BarChart3 className="h-8 w-8 text-green-600" />,
      title: "文献分析",
      description: "深度分析文献趋势、引用网络和研究热点"
    },
    {
      icon: <Users className="h-8 w-8 text-purple-600" />,
      title: "作者追踪",
      description: "跟踪研究者的学术动态和合作网络"
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-orange-600" />,
      title: "趋势预测",
      description: "基于AI的研究趋势预测和热点发现"
    }
  ]

  const popularTopics = [
    "CRISPR基因编辑",
    "人工智能医疗",
    "癌症免疫治疗",
    "干细胞研究",
    "精准医学",
    "生物信息学",
    "药物发现",
    "基因组学"
  ]

  const recentPapers = [
    {
      title: "CRISPR-Cas9在基因治疗中的最新进展",
      authors: "Zhang, Y., Li, M., Wang, X.",
      year: 2024,
      citations: 156,
      venue: "Nature Biotechnology"
    },
    {
      title: "人工智能在药物发现中的应用",
      authors: "Chen, L., Brown, J., Smith, K.",
      year: 2024,
      citations: 89,
      venue: "Science"
    },
    {
      title: "单细胞测序技术的突破性进展",
      authors: "Liu, H., Davis, R., Wilson, M.",
      year: 2024,
      citations: 234,
      venue: "Cell"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* 导航栏 */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">Scholar AI</span>
              <Badge variant="secondary" className="ml-2">Beta</Badge>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost">功能</Button>
              <Button variant="ghost">定价</Button>
              <Button variant="ghost">帮助</Button>
              <Button>登录</Button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* 英雄区域 */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            智能文献阅读
            <span className="text-blue-600"> AI助手</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            基于Semantic Scholar API的强大文献检索和分析平台，
            为生物医学研究者提供智能化的文献发现和分析工具
          </p>
          
          {/* 搜索框 */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Input
                type="text"
                placeholder="搜索论文、作者或关键词..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="pl-12 pr-4 py-4 text-lg rounded-full border-2 border-gray-300 focus:border-blue-500"
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
              <Button 
                onClick={handleSearch}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full px-6"
              >
                搜索
              </Button>
            </div>
          </div>

          {/* 热门话题 */}
          <div className="mb-12">
            <p className="text-sm text-gray-500 mb-4">热门研究话题：</p>
            <div className="flex flex-wrap justify-center gap-2">
              {popularTopics.map((topic, index) => (
                <Badge 
                  key={index} 
                  variant="outline" 
                  className="cursor-pointer hover:bg-blue-50 hover:border-blue-300"
                  onClick={() => setSearchQuery(topic)}
                >
                  {topic}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* 功能特性 */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            强大的研究工具
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* 最新论文和统计 */}
        <Tabs defaultValue="recent" className="mb-16">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="recent">最新论文</TabsTrigger>
            <TabsTrigger value="trending">热门论文</TabsTrigger>
            <TabsTrigger value="stats">平台统计</TabsTrigger>
          </TabsList>
          
          <TabsContent value="recent" className="mt-8">
            <div className="grid gap-6">
              {recentPapers.map((paper, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600">
                        {paper.title}
                      </h3>
                      <Badge variant="outline">{paper.year}</Badge>
                    </div>
                    <p className="text-gray-600 mb-2">{paper.authors}</p>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>{paper.venue}</span>
                      <div className="flex items-center space-x-4">
                        <span className="flex items-center">
                          <Star className="h-4 w-4 mr-1" />
                          {paper.citations} 引用
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="trending" className="mt-8">
            <div className="text-center py-12">
              <TrendingUp className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">热门论文数据加载中...</p>
            </div>
          </TabsContent>
          
          <TabsContent value="stats" className="mt-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="text-center">
                  <CardTitle className="text-3xl font-bold text-blue-600">2.5M+</CardTitle>
                  <CardDescription>论文数据库</CardDescription>
                </CardHeader>
              </Card>
              <Card>
                <CardHeader className="text-center">
                  <CardTitle className="text-3xl font-bold text-green-600">150K+</CardTitle>
                  <CardDescription>活跃研究者</CardDescription>
                </CardHeader>
              </Card>
              <Card>
                <CardHeader className="text-center">
                  <CardTitle className="text-3xl font-bold text-purple-600">50K+</CardTitle>
                  <CardDescription>每日搜索</CardDescription>
                </CardHeader>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* CTA区域 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-12 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">
            开始您的智能文献研究之旅
          </h2>
          <p className="text-xl mb-8 opacity-90">
            加入数万名研究者，体验AI驱动的文献发现和分析
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="text-blue-600">
              免费试用
            </Button>
            <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-blue-600">
              查看定价
            </Button>
          </div>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="bg-gray-900 text-white py-12 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <BookOpen className="h-6 w-6" />
                <span className="text-lg font-bold">Scholar AI</span>
              </div>
              <p className="text-gray-400">
                智能文献阅读AI助手，为研究者提供专业的文献检索和分析服务。
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">产品</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">文献搜索</a></li>
                <li><a href="#" className="hover:text-white">趋势分析</a></li>
                <li><a href="#" className="hover:text-white">作者追踪</a></li>
                <li><a href="#" className="hover:text-white">API服务</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">支持</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">帮助中心</a></li>
                <li><a href="#" className="hover:text-white">API文档</a></li>
                <li><a href="#" className="hover:text-white">联系我们</a></li>
                <li><a href="#" className="hover:text-white">反馈建议</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">公司</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">关于我们</a></li>
                <li><a href="#" className="hover:text-white">隐私政策</a></li>
                <li><a href="#" className="hover:text-white">服务条款</a></li>
                <li><a href="#" className="hover:text-white">加入我们</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 生科云码. 保留所有权利.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
