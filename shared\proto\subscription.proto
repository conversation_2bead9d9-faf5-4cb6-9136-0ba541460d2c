syntax = "proto3";

package biocloude.subscription;

option go_package = "github.com/biocloude/platform/shared/proto/subscription";

import "google/protobuf/timestamp.proto";

// 订阅管理服务
service SubscriptionService {
  // 创建订阅
  rpc CreateSubscription(CreateSubscriptionRequest) returns (CreateSubscriptionResponse);
  
  // 获取用户订阅
  rpc GetUserSubscriptions(GetUserSubscriptionsRequest) returns (GetUserSubscriptionsResponse);
  
  // 获取订阅详情
  rpc GetSubscription(GetSubscriptionRequest) returns (GetSubscriptionResponse);
  
  // 更新订阅
  rpc UpdateSubscription(UpdateSubscriptionRequest) returns (UpdateSubscriptionResponse);
  
  // 取消订阅
  rpc CancelSubscription(CancelSubscriptionRequest) returns (CancelSubscriptionResponse);
  
  // 检查订阅状态
  rpc CheckSubscriptionStatus(CheckSubscriptionStatusRequest) returns (CheckSubscriptionStatusResponse);
  
  // 获取产品信息
  rpc GetProduct(GetProductRequest) returns (GetProductResponse);
  
  // 获取所有产品
  rpc GetProducts(GetProductsRequest) returns (GetProductsResponse);
  
  // 记录API使用情况
  rpc RecordAPIUsage(RecordAPIUsageRequest) returns (RecordAPIUsageResponse);
  
  // 获取API使用统计
  rpc GetAPIUsageStats(GetAPIUsageStatsRequest) returns (GetAPIUsageStatsResponse);
}

// 创建订阅请求
message CreateSubscriptionRequest {
  string user_id = 1;
  string product_id = 2;
  string billing_cycle = 3; // monthly, yearly
  double price = 4;
}

// 创建订阅响应
message CreateSubscriptionResponse {
  bool success = 1;
  string message = 2;
  Subscription subscription = 3;
}

// 获取用户订阅请求
message GetUserSubscriptionsRequest {
  string user_id = 1;
  string status = 2; // 可选，过滤状态
}

// 获取用户订阅响应
message GetUserSubscriptionsResponse {
  repeated Subscription subscriptions = 1;
}

// 获取订阅请求
message GetSubscriptionRequest {
  string subscription_id = 1;
}

// 获取订阅响应
message GetSubscriptionResponse {
  Subscription subscription = 1;
}

// 更新订阅请求
message UpdateSubscriptionRequest {
  string subscription_id = 1;
  string status = 2;
  bool auto_renew = 3;
  google.protobuf.Timestamp end_date = 4;
}

// 更新订阅响应
message UpdateSubscriptionResponse {
  bool success = 1;
  string message = 2;
  Subscription subscription = 3;
}

// 取消订阅请求
message CancelSubscriptionRequest {
  string subscription_id = 1;
  string reason = 2;
}

// 取消订阅响应
message CancelSubscriptionResponse {
  bool success = 1;
  string message = 2;
}

// 检查订阅状态请求
message CheckSubscriptionStatusRequest {
  string user_id = 1;
  string product_id = 2;
}

// 检查订阅状态响应
message CheckSubscriptionStatusResponse {
  bool has_active_subscription = 1;
  Subscription subscription = 2;
  repeated string available_features = 3;
}

// 获取产品请求
message GetProductRequest {
  string product_id = 1;
}

// 获取产品响应
message GetProductResponse {
  Product product = 1;
}

// 获取所有产品请求
message GetProductsRequest {
  bool active_only = 1;
  string category = 2; // 可选，按分类过滤
}

// 获取所有产品响应
message GetProductsResponse {
  repeated Product products = 1;
}

// 记录API使用请求
message RecordAPIUsageRequest {
  string user_id = 1;
  string product_id = 2;
  string endpoint = 3;
  string method = 4;
  int32 response_time_ms = 5;
  int32 status_code = 6;
  double cost = 7;
}

// 记录API使用响应
message RecordAPIUsageResponse {
  bool success = 1;
  string message = 2;
}

// 获取API使用统计请求
message GetAPIUsageStatsRequest {
  string user_id = 1;
  string product_id = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
}

// 获取API使用统计响应
message GetAPIUsageStatsResponse {
  int32 total_requests = 1;
  double total_cost = 2;
  repeated APIUsageDaily daily_stats = 3;
  repeated APIUsageEndpoint endpoint_stats = 4;
}

// 订阅信息
message Subscription {
  string id = 1;
  string user_id = 2;
  string product_id = 3;
  string status = 4;
  double price = 5;
  string billing_cycle = 6;
  google.protobuf.Timestamp start_date = 7;
  google.protobuf.Timestamp end_date = 8;
  bool auto_renew = 9;
  string metadata = 10; // JSON格式的额外信息
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  Product product = 13;
}

// 产品信息
message Product {
  string id = 1;
  string name = 2;
  string slug = 3;
  string domain = 4;
  string description = 5;
  double base_price = 6;
  string billing_type = 7;
  string features = 8; // JSON格式的功能配置
  bool is_active = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
}

// 每日API使用统计
message APIUsageDaily {
  string date = 1;
  int32 request_count = 2;
  double cost = 3;
}

// 端点API使用统计
message APIUsageEndpoint {
  string endpoint = 1;
  int32 request_count = 2;
  double average_response_time = 3;
  double total_cost = 4;
}
