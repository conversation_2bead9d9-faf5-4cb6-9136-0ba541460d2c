"""
生科云码文献阅读AI服务
基于Semantic Scholar API的智能文献检索和分析系统
"""

import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, HTTPException, Depends, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import aiohttp
import asyncpg
from loguru import logger
import redis.asyncio as redis

# 配置日志
logger.add("logs/scholar_ai.log", rotation="1 day", retention="30 days")

app = FastAPI(
    title="生科云码文献阅读AI",
    description="基于Semantic Scholar API的智能文献检索和分析系统",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置
SEMANTIC_SCHOLAR_API_BASE = "https://api.semanticscholar.org/graph/v1"
REDIS_URL = "redis://redis:6379/1"
DATABASE_URL = "*************************************************/biocloude"

# 全局变量
redis_client: Optional[redis.Redis] = None
db_pool: Optional[asyncpg.Pool] = None

# 数据模型
class PaperSearchRequest(BaseModel):
    query: str = Field(..., description="搜索关键词")
    fields: List[str] = Field(default=["title", "abstract", "authors", "year", "citationCount"], description="返回字段")
    limit: int = Field(default=20, ge=1, le=100, description="返回数量")
    offset: int = Field(default=0, ge=0, description="偏移量")
    year_range: Optional[str] = Field(None, description="年份范围，如'2020-2023'")
    venue: Optional[str] = Field(None, description="期刊或会议名称")
    min_citation_count: Optional[int] = Field(None, description="最小引用数")

class PaperDetailRequest(BaseModel):
    paper_id: str = Field(..., description="论文ID")
    fields: List[str] = Field(default=["title", "abstract", "authors", "year", "citationCount", "references", "citations"], description="返回字段")

class PaperAnalysisRequest(BaseModel):
    paper_ids: List[str] = Field(..., description="论文ID列表")
    analysis_type: str = Field(default="summary", description="分析类型：summary, trends, network")

class AuthorSearchRequest(BaseModel):
    author_name: str = Field(..., description="作者姓名")
    fields: List[str] = Field(default=["name", "affiliations", "paperCount", "citationCount", "hIndex"], description="返回字段")

class RecommendationRequest(BaseModel):
    paper_id: str = Field(..., description="基准论文ID")
    limit: int = Field(default=10, ge=1, le=50, description="推荐数量")
    fields: List[str] = Field(default=["title", "abstract", "authors", "year", "citationCount"], description="返回字段")

# 服务类
class SemanticScholarService:
    """Semantic Scholar API服务"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.api_key = None  # 如果有API密钥
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def search_papers(self, request: PaperSearchRequest) -> Dict[str, Any]:
        """搜索论文"""
        try:
            # 构建查询参数
            params = {
                "query": request.query,
                "fields": ",".join(request.fields),
                "limit": request.limit,
                "offset": request.offset
            }
            
            # 添加过滤条件
            if request.year_range:
                params["year"] = request.year_range
            if request.venue:
                params["venue"] = request.venue
            if request.min_citation_count:
                params["minCitationCount"] = request.min_citation_count
            
            headers = {}
            if self.api_key:
                headers["x-api-key"] = self.api_key
            
            async with self.session.get(
                f"{SEMANTIC_SCHOLAR_API_BASE}/paper/search",
                params=params,
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 缓存结果
                    cache_key = f"search:{hash(str(request.dict()))}"
                    await redis_client.setex(cache_key, 3600, json.dumps(data))
                    
                    return data
                else:
                    raise HTTPException(status_code=response.status, detail="Semantic Scholar API错误")
                    
        except Exception as e:
            logger.error(f"搜索论文失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_paper_details(self, request: PaperDetailRequest) -> Dict[str, Any]:
        """获取论文详情"""
        try:
            # 检查缓存
            cache_key = f"paper:{request.paper_id}"
            cached_data = await redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            
            params = {
                "fields": ",".join(request.fields)
            }
            
            headers = {}
            if self.api_key:
                headers["x-api-key"] = self.api_key
            
            async with self.session.get(
                f"{SEMANTIC_SCHOLAR_API_BASE}/paper/{request.paper_id}",
                params=params,
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 缓存结果
                    await redis_client.setex(cache_key, 7200, json.dumps(data))
                    
                    return data
                else:
                    raise HTTPException(status_code=response.status, detail="论文不存在或API错误")
                    
        except Exception as e:
            logger.error(f"获取论文详情失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def search_authors(self, request: AuthorSearchRequest) -> Dict[str, Any]:
        """搜索作者"""
        try:
            params = {
                "query": request.author_name,
                "fields": ",".join(request.fields)
            }
            
            headers = {}
            if self.api_key:
                headers["x-api-key"] = self.api_key
            
            async with self.session.get(
                f"{SEMANTIC_SCHOLAR_API_BASE}/author/search",
                params=params,
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    raise HTTPException(status_code=response.status, detail="作者搜索失败")
                    
        except Exception as e:
            logger.error(f"搜索作者失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_recommendations(self, request: RecommendationRequest) -> Dict[str, Any]:
        """获取论文推荐"""
        try:
            params = {
                "fields": ",".join(request.fields),
                "limit": request.limit
            }
            
            headers = {}
            if self.api_key:
                headers["x-api-key"] = self.api_key
            
            async with self.session.get(
                f"{SEMANTIC_SCHOLAR_API_BASE}/paper/{request.paper_id}/citations",
                params=params,
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    raise HTTPException(status_code=response.status, detail="推荐获取失败")
                    
        except Exception as e:
            logger.error(f"获取推荐失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

class AnalyticsService:
    """文献分析服务"""
    
    async def analyze_papers(self, request: PaperAnalysisRequest) -> Dict[str, Any]:
        """分析论文"""
        try:
            if request.analysis_type == "summary":
                return await self._generate_summary(request.paper_ids)
            elif request.analysis_type == "trends":
                return await self._analyze_trends(request.paper_ids)
            elif request.analysis_type == "network":
                return await self._analyze_network(request.paper_ids)
            else:
                raise HTTPException(status_code=400, detail="不支持的分析类型")
                
        except Exception as e:
            logger.error(f"分析论文失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def _generate_summary(self, paper_ids: List[str]) -> Dict[str, Any]:
        """生成论文摘要"""
        # 获取论文详情
        papers = []
        async with SemanticScholarService() as scholar:
            for paper_id in paper_ids:
                try:
                    paper = await scholar.get_paper_details(
                        PaperDetailRequest(paper_id=paper_id)
                    )
                    papers.append(paper)
                except:
                    continue
        
        # 分析统计
        total_papers = len(papers)
        total_citations = sum(p.get("citationCount", 0) for p in papers)
        years = [p.get("year") for p in papers if p.get("year")]
        year_range = f"{min(years)}-{max(years)}" if years else "未知"
        
        # 提取关键词（简化版）
        abstracts = [p.get("abstract", "") for p in papers if p.get("abstract")]
        keywords = self._extract_keywords(abstracts)
        
        return {
            "summary": {
                "total_papers": total_papers,
                "total_citations": total_citations,
                "year_range": year_range,
                "avg_citations": total_citations / total_papers if total_papers > 0 else 0,
                "keywords": keywords[:10]
            },
            "papers": papers
        }
    
    async def _analyze_trends(self, paper_ids: List[str]) -> Dict[str, Any]:
        """分析趋势"""
        # 获取论文详情
        papers = []
        async with SemanticScholarService() as scholar:
            for paper_id in paper_ids:
                try:
                    paper = await scholar.get_paper_details(
                        PaperDetailRequest(paper_id=paper_id)
                    )
                    papers.append(paper)
                except:
                    continue
        
        # 按年份统计
        year_stats = {}
        for paper in papers:
            year = paper.get("year")
            if year:
                year_stats[year] = year_stats.get(year, 0) + 1
        
        # 引用趋势
        citation_trends = {}
        for paper in papers:
            year = paper.get("year")
            citations = paper.get("citationCount", 0)
            if year:
                if year not in citation_trends:
                    citation_trends[year] = []
                citation_trends[year].append(citations)
        
        # 计算平均引用数
        avg_citations_by_year = {}
        for year, citations in citation_trends.items():
            avg_citations_by_year[year] = sum(citations) / len(citations)
        
        return {
            "trends": {
                "publication_by_year": year_stats,
                "avg_citations_by_year": avg_citations_by_year,
                "total_papers": len(papers)
            }
        }
    
    async def _analyze_network(self, paper_ids: List[str]) -> Dict[str, Any]:
        """分析引用网络"""
        # 获取论文及其引用关系
        network_data = {
            "nodes": [],
            "edges": []
        }
        
        async with SemanticScholarService() as scholar:
            for paper_id in paper_ids:
                try:
                    paper = await scholar.get_paper_details(
                        PaperDetailRequest(
                            paper_id=paper_id,
                            fields=["title", "authors", "year", "citationCount", "references"]
                        )
                    )
                    
                    # 添加节点
                    network_data["nodes"].append({
                        "id": paper_id,
                        "title": paper.get("title", ""),
                        "year": paper.get("year"),
                        "citations": paper.get("citationCount", 0),
                        "authors": [a.get("name", "") for a in paper.get("authors", [])]
                    })
                    
                    # 添加引用边
                    references = paper.get("references", [])
                    for ref in references[:10]:  # 限制引用数量
                        if ref.get("paperId") in paper_ids:
                            network_data["edges"].append({
                                "source": paper_id,
                                "target": ref.get("paperId"),
                                "type": "cites"
                            })
                            
                except:
                    continue
        
        return {"network": network_data}
    
    def _extract_keywords(self, texts: List[str]) -> List[str]:
        """提取关键词（简化版）"""
        from collections import Counter
        import re
        
        # 合并所有文本
        combined_text = " ".join(texts).lower()
        
        # 简单的关键词提取
        words = re.findall(r'\b[a-z]{4,}\b', combined_text)
        
        # 过滤常见词
        stop_words = {"this", "that", "with", "from", "they", "been", "have", "were", "said", "each", "which", "their", "time", "will", "about", "would", "there", "could", "other", "more", "very", "what", "know", "just", "first", "into", "over", "think", "also", "your", "work", "life", "only", "can", "still", "should", "after", "being", "now", "made", "before", "here", "through", "when", "where", "much", "some", "these", "many", "then", "them", "well", "were"}
        
        filtered_words = [w for w in words if w not in stop_words and len(w) > 3]
        
        # 返回最常见的词
        word_counts = Counter(filtered_words)
        return [word for word, count in word_counts.most_common(20)]

# 初始化服务
scholar_service = SemanticScholarService()
analytics_service = AnalyticsService()

# 启动事件
@app.on_event("startup")
async def startup_event():
    global redis_client, db_pool
    
    # 初始化Redis
    redis_client = redis.from_url(REDIS_URL)
    
    # 初始化数据库连接池
    db_pool = await asyncpg.create_pool(DATABASE_URL)
    
    logger.info("🚀 Scholar AI服务启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    global redis_client, db_pool
    
    if redis_client:
        await redis_client.close()
    
    if db_pool:
        await db_pool.close()
    
    logger.info("👋 Scholar AI服务关闭")

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "scholar-ai",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0"
    }

@app.post("/api/v1/search/papers")
async def search_papers(request: PaperSearchRequest):
    """搜索论文"""
    async with SemanticScholarService() as service:
        result = await service.search_papers(request)
        return JSONResponse(content=result)

@app.post("/api/v1/papers/details")
async def get_paper_details(request: PaperDetailRequest):
    """获取论文详情"""
    async with SemanticScholarService() as service:
        result = await service.get_paper_details(request)
        return JSONResponse(content=result)

@app.post("/api/v1/search/authors")
async def search_authors(request: AuthorSearchRequest):
    """搜索作者"""
    async with SemanticScholarService() as service:
        result = await service.search_authors(request)
        return JSONResponse(content=result)

@app.post("/api/v1/recommendations")
async def get_recommendations(request: RecommendationRequest):
    """获取论文推荐"""
    async with SemanticScholarService() as service:
        result = await service.get_recommendations(request)
        return JSONResponse(content=result)

@app.post("/api/v1/analyze")
async def analyze_papers(request: PaperAnalysisRequest):
    """分析论文"""
    result = await analytics_service.analyze_papers(request)
    return JSONResponse(content=result)

@app.get("/api/v1/stats")
async def get_stats():
    """获取统计信息"""
    try:
        # 从Redis获取统计信息
        stats = {
            "total_searches": await redis_client.get("stats:total_searches") or 0,
            "total_papers_viewed": await redis_client.get("stats:total_papers_viewed") or 0,
            "total_analyses": await redis_client.get("stats:total_analyses") or 0,
            "cache_hit_rate": 0.85,  # 示例数据
            "avg_response_time": 250  # 示例数据
        }
        
        return JSONResponse(content={"stats": stats})
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9002)
