#!/bin/bash

# 修复行结束符脚本
# 将所有文本文件的行结束符统一为 LF

set -e

echo "🔧 修复生科云码平台文件行结束符..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否有 dos2unix 工具
check_dos2unix() {
    if command -v dos2unix &> /dev/null; then
        return 0
    elif command -v sed &> /dev/null; then
        return 1
    else
        log_error "需要 dos2unix 或 sed 工具来修复行结束符"
        exit 1
    fi
}

# 使用 dos2unix 修复文件
fix_with_dos2unix() {
    local file="$1"
    dos2unix "$file" 2>/dev/null
}

# 使用 sed 修复文件
fix_with_sed() {
    local file="$1"
    sed -i 's/\r$//' "$file" 2>/dev/null
}

# 修复单个文件
fix_file() {
    local file="$1"
    
    if [ ! -f "$file" ]; then
        return
    fi
    
    # 检查文件是否包含 CRLF
    if file "$file" | grep -q "CRLF"; then
        log_info "修复文件: $file"
        
        if check_dos2unix; then
            fix_with_dos2unix "$file"
        else
            fix_with_sed "$file"
        fi
        
        log_success "已修复: $file"
    fi
}

# 修复目录下的所有文件
fix_directory() {
    local dir="$1"
    local pattern="$2"
    
    if [ ! -d "$dir" ]; then
        return
    fi
    
    log_info "修复目录: $dir (模式: $pattern)"
    
    find "$dir" -name "$pattern" -type f | while read -r file; do
        fix_file "$file"
    done
}

# 主修复函数
main() {
    log_info "开始修复行结束符..."
    
    # 修复根目录文件
    log_info "修复根目录配置文件..."
    fix_file "README.md"
    fix_file ".gitignore"
    fix_file ".gitattributes"
    fix_file ".env"
    fix_file "docker-compose.yml"
    fix_file "package.json"
    
    # 修复 .devcontainer 目录
    log_info "修复 .devcontainer 目录..."
    fix_directory ".devcontainer" "*"
    
    # 修复 .github 目录
    log_info "修复 .github 目录..."
    fix_directory ".github" "*"
    
    # 修复脚本目录
    log_info "修复 scripts 目录..."
    fix_directory "scripts" "*.sh"
    fix_directory "scripts" "*.py"
    
    # 修复 K8s 配置
    log_info "修复 Kubernetes 配置..."
    fix_directory "k8s" "*.yml"
    fix_directory "k8s" "*.yaml"
    
    # 修复前端应用
    log_info "修复前端应用..."
    fix_directory "web-apps" "*.js"
    fix_directory "web-apps" "*.jsx"
    fix_directory "web-apps" "*.ts"
    fix_directory "web-apps" "*.tsx"
    fix_directory "web-apps" "*.json"
    fix_directory "web-apps" "*.md"
    fix_directory "web-apps" "*.yml"
    fix_directory "web-apps" "*.yaml"
    
    # 修复后端服务
    log_info "修复后端服务..."
    fix_directory "services" "*.go"
    fix_directory "services" "*.py"
    fix_directory "services" "*.json"
    fix_directory "services" "*.yml"
    fix_directory "services" "*.yaml"
    fix_directory "services" "*.md"
    fix_directory "services" "*.txt"
    fix_directory "services" "*.env"
    
    # 修复数据库文件
    log_info "修复数据库文件..."
    fix_directory "database" "*.sql"
    fix_directory "database" "*.md"
    
    # 修复文档
    log_info "修复文档文件..."
    fix_directory "docs" "*.md"
    fix_directory "docs" "*.txt"
    
    log_success "✅ 行结束符修复完成！"
}

# Git 配置修复
fix_git_config() {
    log_info "配置 Git 行结束符处理..."
    
    # 设置 Git 配置
    git config core.autocrlf false
    git config core.eol lf
    
    log_success "Git 配置已更新"
}

# 重新规范化 Git 仓库
renormalize_git() {
    log_info "重新规范化 Git 仓库..."
    
    # 添加 .gitattributes
    git add .gitattributes
    
    # 重新规范化所有文件
    git add --renormalize .
    
    log_success "Git 仓库已重新规范化"
}

# 显示使用帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --fix-only     仅修复文件，不更新 Git 配置"
    echo "  --git-only     仅更新 Git 配置，不修复文件"
    echo "  --help         显示此帮助信息"
    echo ""
    echo "默认行为: 修复文件并更新 Git 配置"
}

# 解析命令行参数
case "${1:-}" in
    --fix-only)
        main
        ;;
    --git-only)
        fix_git_config
        renormalize_git
        ;;
    --help)
        show_help
        ;;
    "")
        main
        fix_git_config
        renormalize_git
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac

echo ""
echo "🎉 行结束符修复完成！"
echo ""
echo "📋 后续步骤："
echo "1. 检查修改: git status"
echo "2. 提交更改: git commit -m 'fix: 统一行结束符为 LF'"
echo "3. 推送更改: git push"
echo ""
echo "💡 提示: 现在所有新文件都会自动使用正确的行结束符"
