# EditorConfig 配置文件
# 确保所有编辑器使用一致的代码风格

root = true

# 所有文件的默认设置
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# Go 文件
[*.go]
indent_style = tab
indent_size = 4

# Python 文件
[*.py]
indent_size = 4

# JavaScript/TypeScript 文件
[*.{js,jsx,ts,tsx}]
indent_size = 2

# JSON 文件
[*.json]
indent_size = 2

# YAML 文件
[*.{yml,yaml}]
indent_size = 2

# Markdown 文件
[*.md]
trim_trailing_whitespace = false

# Makefile
[Makefile]
indent_style = tab

# Shell 脚本
[*.sh]
indent_size = 2

# 批处理文件 (Windows)
[*.{bat,cmd}]
end_of_line = crlf

# PowerShell 脚本
[*.ps1]
end_of_line = crlf

# Docker 文件
[Dockerfile*]
indent_size = 2

# 配置文件
[*.{conf,config,ini,env}]
indent_size = 2

# SQL 文件
[*.sql]
indent_size = 2

# HTML/CSS 文件
[*.{html,css,scss,sass,less}]
indent_size = 2

# XML 文件
[*.xml]
indent_size = 2
