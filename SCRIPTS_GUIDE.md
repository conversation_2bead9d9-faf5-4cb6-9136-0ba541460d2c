# 🚀 生科云码平台脚本使用指南

## 📋 脚本概览

生科云码平台提供了一套完整的开发环境管理脚本，让您能够轻松启动、停止、测试和监控所有服务。

## 🔧 脚本分类

### 🏗️ **环境初始化脚本**
这些脚本在 Codespaces 创建和启动时自动运行，无需手动执行：

- **`.devcontainer/setup.sh`** - 环境初始化
  - 安装依赖包
  - 创建必要目录
  - 配置环境变量
  - 设置Git hooks

- **`.devcontainer/start-services.sh`** - 基础服务启动
  - 启动 PostgreSQL、Redis
  - 启动 MailHog、MinIO
  - 启动 Prometheus、Grafana

### 🎯 **用户操作脚本**
这些脚本需要您手动运行来管理应用服务：

## 🚀 **启动服务**

### `./start-dev.sh` - 启动所有应用服务
```bash
./start-dev.sh
```

**功能：**
- ✅ 检查基础服务状态
- ✅ 安装/更新依赖
- ✅ 启动后端服务 (认证、订阅)
- ✅ 启动AI服务 (6个AI微服务)
- ✅ 启动前端应用 (3个Web应用)
- ✅ 显示访问地址

**启动的服务：**
- 🔐 认证服务 (端口 8001)
- 💳 订阅服务 (端口 8002)
- 🤖 资讯AI (端口 9001)
- 📖 文献AI (端口 9002)
- 🧬 引物AI (端口 9003)
- 🔬 蛋白质AI (端口 9004)
- ✂️ 基因编辑AI (端口 9005)
- ⚗️ 代谢工程AI (端口 9006)
- 🏠 主站门户 (端口 3000)
- 📰 资讯处理AI应用 (端口 3001)
- 📚 文献阅读AI应用 (端口 3002)

## 🛑 **停止服务**

### `./stop-dev.sh` - 停止所有应用服务
```bash
./stop-dev.sh
```

**功能：**
- ✅ 优雅停止所有应用进程
- ✅ 强制杀死未响应的进程
- ✅ 释放所有端口
- ✅ 清理PID文件

## 🔍 **检查状态**

### `./status-dev.sh` - 检查服务状态
```bash
./status-dev.sh
```

**功能：**
- ✅ 检查所有端口占用情况
- ✅ 测试HTTP服务响应
- ✅ 显示PID文件状态
- ✅ 提供访问地址
- ✅ 显示服务统计

**输出示例：**
```
🗄️ 基础服务状态:
  ✅ PostgreSQL (端口 5432) - 运行中
  ✅ Redis (端口 6379) - 运行中

⚙️ 后端服务状态:
  ✅ 认证服务 (端口 8001) - 运行中
  ✅ 订阅服务 (端口 8002) - 运行中

📊 应用服务总计: 11/11
```

## 🧪 **运行测试**

### `./test-dev.sh` - 运行测试套件
```bash
./test-dev.sh
```

**功能：**
- ✅ 数据库连接测试
- ✅ Go服务单元测试
- ✅ Python AI服务测试
- ✅ 前端应用测试
- ✅ API集成测试
- ✅ 前端访问测试
- ✅ 基础性能测试

**测试选项：**
```bash
# 仅运行单元测试
./test-dev.sh --unit-only

# 仅运行API集成测试
./test-dev.sh --api-only

# 显示帮助
./test-dev.sh --help
```

## 🎯 **典型工作流程**

### 🌅 **开始开发**
```bash
# 1. 启动所有服务
./start-dev.sh

# 2. 检查服务状态
./status-dev.sh

# 3. 开始编码...
```

### 🧪 **测试代码**
```bash
# 运行测试
./test-dev.sh

# 或者只运行特定类型的测试
./test-dev.sh --unit-only
```

### 🔄 **重启服务**
```bash
# 停止所有服务
./stop-dev.sh

# 重新启动
./start-dev.sh
```

### 🌙 **结束开发**
```bash
# 停止所有服务
./stop-dev.sh
```

## 🌐 **访问地址**

启动服务后，您可以通过以下地址访问：

### **前端应用**
- 🏠 **主站门户**: http://localhost:3000
- 📰 **资讯处理AI**: http://localhost:3001
- 📚 **文献阅读AI**: http://localhost:3002

### **API服务**
- 🔐 **认证服务**: http://localhost:8001
- 💳 **订阅服务**: http://localhost:8002
- 🤖 **AI服务**: http://localhost:9001-9006

### **开发工具**
- 📧 **MailHog**: http://localhost:8025
- 💾 **MinIO**: http://localhost:9090
- 📊 **Prometheus**: http://localhost:9091
- 📈 **Grafana**: http://localhost:3030

## 🐛 **故障排除**

### **服务启动失败**
```bash
# 检查服务状态
./status-dev.sh

# 查看具体错误
./start-dev.sh

# 停止所有服务后重试
./stop-dev.sh
./start-dev.sh
```

### **端口冲突**
```bash
# 停止所有服务
./stop-dev.sh

# 检查端口是否释放
./status-dev.sh

# 如果端口仍被占用，手动杀死进程
# Linux/macOS: sudo lsof -ti:3000 | xargs kill -9
# Windows: netstat -ano | findstr :3000
```

### **依赖问题**
```bash
# 重新安装依赖
cd services/auth-service && go mod download
cd web-apps/main-portal && npm install
```

## 💡 **最佳实践**

### ✅ **推荐做法**
- 🔄 **定期运行测试** - 确保代码质量
- 📊 **检查服务状态** - 及时发现问题
- 🛑 **开发结束后停止服务** - 节省资源
- 🧪 **提交前运行测试** - 避免破坏性更改

### ❌ **避免做法**
- 🚫 **不要手动杀死进程** - 使用停止脚本
- 🚫 **不要忽略测试失败** - 及时修复问题
- 🚫 **不要同时运行多个实例** - 避免端口冲突

## 📞 **获取帮助**

如果遇到脚本相关问题：

1. 📖 **查看此文档**
2. 🔍 **运行状态检查**: `./status-dev.sh`
3. 🧪 **运行测试诊断**: `./test-dev.sh`
4. 🐛 **创建 GitHub Issue**
5. 💬 **在团队群组询问**

---

🎉 **现在您已经掌握了生科云码平台的所有开发脚本！开始您的开发之旅吧！** 🚀
