"""
AI智能分类和处理服务
功能：
1. 文章智能分类
2. 关键词提取
3. 摘要生成
4. 情感分析
5. 相关性评分
"""

import asyncio
import json
import re
from datetime import datetime
from typing import List, Dict, Optional, Any, Tuple
from collections import Counter
import numpy as np
from loguru import logger

# NLP相关库
try:
    import openai
    from transformers import pipeline, AutoTokenizer, AutoModel
    import torch
    from sentence_transformers import SentenceTransformer
    import spacy
    from textblob import TextBlob
    from langdetect import detect
except ImportError as e:
    logger.warning(f"某些AI库未安装: {e}")

from app.core.config import settings, CLASSIFICATION_KEYWORDS, PRIORITY_KEYWORDS
from app.core.redis_client import redis_client

class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.classification_model = None
        self.embedding_model = None
        self.nlp_model = None
        self.sentiment_analyzer = None
        self.summarizer = None
        self.keyword_extractor = None
        
        # 初始化模型
        asyncio.create_task(self._initialize_models())
        
        # 分类阈值
        self.classification_threshold = 0.6
        self.similarity_threshold = 0.8
        
    async def _initialize_models(self):
        """初始化AI模型"""
        try:
            logger.info("🤖 初始化AI模型...")
            
            # 初始化OpenAI
            if settings.OPENAI_API_KEY:
                openai.api_key = settings.OPENAI_API_KEY
                logger.info("✅ OpenAI API已配置")
            
            # 初始化句子嵌入模型
            try:
                self.embedding_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
                logger.info("✅ 句子嵌入模型加载完成")
            except Exception as e:
                logger.warning(f"句子嵌入模型加载失败: {e}")
            
            # 初始化spaCy中文模型
            try:
                self.nlp_model = spacy.load("zh_core_web_sm")
                logger.info("✅ spaCy中文模型加载完成")
            except Exception as e:
                logger.warning(f"spaCy模型加载失败: {e}")
                # 尝试加载英文模型
                try:
                    self.nlp_model = spacy.load("en_core_web_sm")
                    logger.info("✅ spaCy英文模型加载完成")
                except Exception as e2:
                    logger.warning(f"spaCy英文模型也加载失败: {e2}")
            
            # 初始化情感分析器
            try:
                self.sentiment_analyzer = pipeline(
                    "sentiment-analysis",
                    model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                    return_all_scores=True
                )
                logger.info("✅ 情感分析模型加载完成")
            except Exception as e:
                logger.warning(f"情感分析模型加载失败: {e}")
            
            # 初始化摘要生成器
            try:
                self.summarizer = pipeline(
                    "summarization",
                    model="facebook/bart-large-cnn",
                    max_length=150,
                    min_length=50
                )
                logger.info("✅ 摘要生成模型加载完成")
            except Exception as e:
                logger.warning(f"摘要生成模型加载失败: {e}")
            
            logger.info("🎉 AI模型初始化完成")
            
        except Exception as e:
            logger.error(f"AI模型初始化失败: {e}")
    
    async def start_processing(self):
        """启动AI处理服务"""
        logger.info("🧠 启动AI处理服务...")
        
        try:
            while True:
                # 从队列获取待处理文章
                article_id = await redis_client.brpop("article_processing_queue", timeout=10)
                
                if article_id:
                    _, article_id = article_id
                    article_id = article_id.decode('utf-8')
                    await self._process_article(article_id)
                
        except Exception as e:
            logger.error(f"AI处理服务异常: {e}")
    
    async def _process_article(self, article_id: str):
        """处理单篇文章"""
        try:
            # 从Redis获取文章数据
            article_data = await redis_client.get(f"article:{article_id}")
            if not article_data:
                logger.warning(f"文章 {article_id} 不存在")
                return
            
            article = json.loads(article_data)
            logger.info(f"🔍 处理文章: {article.get('title', '')[:50]}...")
            
            # AI处理
            processed_article = await self._ai_process_article(article)
            
            # 保存处理结果
            await redis_client.setex(
                f"processed_article:{article_id}",
                86400 * 30,  # 30天过期
                json.dumps(processed_article, default=str, ensure_ascii=False)
            )
            
            # 添加到推荐队列
            await redis_client.lpush("recommendation_queue", article_id)
            
            logger.info(f"✅ 文章处理完成: {article_id}")
            
        except Exception as e:
            logger.error(f"处理文章 {article_id} 失败: {e}")
    
    async def _ai_process_article(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """AI处理文章"""
        title = article.get("title", "")
        summary = article.get("summary", "")
        content = f"{title} {summary}"
        
        # 1. 语言检测
        language = self._detect_language(content)
        
        # 2. 智能分类
        category, confidence = await self._classify_article(title, summary)
        
        # 3. 关键词提取
        keywords = await self._extract_keywords(content)
        
        # 4. 情感分析
        sentiment = await self._analyze_sentiment(content)
        
        # 5. 优先级计算
        priority = self._calculate_priority(title, summary, keywords)
        
        # 6. 相关性评分
        relevance_score = self._calculate_relevance_score(title, summary, keywords)
        
        # 7. 趋势评分
        trending_score = await self._calculate_trending_score(keywords)
        
        # 8. 生成摘要（如果需要）
        ai_summary = await self._generate_summary(content) if len(content) > 500 else summary
        
        # 9. 重复检测
        is_duplicate, similar_articles = await self._detect_duplicates(title, summary)
        
        # 更新文章数据
        processed_article = article.copy()
        processed_article.update({
            "ai_category": category,
            "ai_confidence": confidence,
            "ai_keywords": keywords,
            "ai_sentiment": sentiment,
            "ai_priority": priority,
            "ai_relevance_score": relevance_score,
            "ai_trending_score": trending_score,
            "ai_summary": ai_summary,
            "language": language,
            "is_duplicate": is_duplicate,
            "similar_articles": similar_articles,
            "processed_at": datetime.utcnow().isoformat(),
            "processing_version": "1.0"
        })
        
        return processed_article
    
    def _detect_language(self, text: str) -> str:
        """检测文本语言"""
        try:
            return detect(text)
        except:
            return "unknown"
    
    async def _classify_article(self, title: str, summary: str) -> Tuple[str, float]:
        """智能分类文章"""
        content = f"{title} {summary}".lower()
        
        # 基于关键词的分类
        category_scores = {}
        
        for category, keywords in CLASSIFICATION_KEYWORDS.items():
            score = 0
            for keyword in keywords:
                if keyword.lower() in content:
                    # 标题中的关键词权重更高
                    if keyword.lower() in title.lower():
                        score += 2
                    else:
                        score += 1
            
            if score > 0:
                category_scores[category] = score / len(keywords)
        
        # 如果有基于关键词的分类结果
        if category_scores:
            best_category = max(category_scores, key=category_scores.get)
            confidence = min(category_scores[best_category], 1.0)
            
            if confidence >= self.classification_threshold:
                return best_category, confidence
        
        # 使用机器学习模型分类（如果可用）
        if self.embedding_model:
            try:
                ml_category, ml_confidence = await self._ml_classify_article(content)
                if ml_confidence >= self.classification_threshold:
                    return ml_category, ml_confidence
            except Exception as e:
                logger.warning(f"机器学习分类失败: {e}")
        
        # 默认分类
        return "其他", 0.5
    
    async def _ml_classify_article(self, content: str) -> Tuple[str, float]:
        """使用机器学习模型分类"""
        try:
            # 生成文本嵌入
            content_embedding = self.embedding_model.encode([content])
            
            # 计算与各类别的相似度
            category_similarities = {}
            
            for category, keywords in CLASSIFICATION_KEYWORDS.items():
                category_text = " ".join(keywords)
                category_embedding = self.embedding_model.encode([category_text])
                
                # 计算余弦相似度
                similarity = np.dot(content_embedding[0], category_embedding[0]) / (
                    np.linalg.norm(content_embedding[0]) * np.linalg.norm(category_embedding[0])
                )
                category_similarities[category] = similarity
            
            # 找到最相似的类别
            best_category = max(category_similarities, key=category_similarities.get)
            confidence = category_similarities[best_category]
            
            return best_category, confidence
            
        except Exception as e:
            logger.error(f"机器学习分类失败: {e}")
            return "其他", 0.0
    
    async def _extract_keywords(self, content: str) -> List[str]:
        """提取关键词"""
        keywords = []
        
        try:
            # 使用spaCy提取关键词
            if self.nlp_model:
                doc = self.nlp_model(content[:1000])  # 限制长度
                
                # 提取命名实体
                entities = [ent.text for ent in doc.ents if len(ent.text) > 2]
                keywords.extend(entities)
                
                # 提取重要的名词和形容词
                important_tokens = [
                    token.text for token in doc 
                    if token.pos_ in ['NOUN', 'ADJ'] and len(token.text) > 2 and not token.is_stop
                ]
                keywords.extend(important_tokens)
            
            # 基于频率的关键词提取
            words = re.findall(r'\b[a-zA-Z\u4e00-\u9fff]{3,}\b', content.lower())
            word_freq = Counter(words)
            
            # 获取高频词
            frequent_words = [word for word, freq in word_freq.most_common(10) if freq > 1]
            keywords.extend(frequent_words)
            
            # 去重并限制数量
            keywords = list(set(keywords))[:20]
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            # 简单的关键词提取
            words = re.findall(r'\b[a-zA-Z\u4e00-\u9fff]{3,}\b', content.lower())
            keywords = list(set(words))[:10]
        
        return keywords
    
    async def _analyze_sentiment(self, content: str) -> Dict[str, Any]:
        """情感分析"""
        try:
            if self.sentiment_analyzer:
                # 使用Transformers模型
                results = self.sentiment_analyzer(content[:512])  # 限制长度
                
                sentiment_scores = {}
                for result in results[0]:
                    sentiment_scores[result['label'].lower()] = result['score']
                
                # 确定主要情感
                main_sentiment = max(sentiment_scores, key=sentiment_scores.get)
                confidence = sentiment_scores[main_sentiment]
                
                return {
                    "sentiment": main_sentiment,
                    "confidence": confidence,
                    "scores": sentiment_scores
                }
            else:
                # 使用TextBlob作为备选
                blob = TextBlob(content)
                polarity = blob.sentiment.polarity
                
                if polarity > 0.1:
                    sentiment = "positive"
                elif polarity < -0.1:
                    sentiment = "negative"
                else:
                    sentiment = "neutral"
                
                return {
                    "sentiment": sentiment,
                    "confidence": abs(polarity),
                    "polarity": polarity
                }
                
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return {
                "sentiment": "neutral",
                "confidence": 0.5
            }
    
    def _calculate_priority(self, title: str, summary: str, keywords: List[str]) -> str:
        """计算文章优先级"""
        content = f"{title} {summary}".lower()
        
        # 高优先级关键词检查
        high_priority_score = 0
        for keyword in PRIORITY_KEYWORDS.get("high", []):
            if keyword.lower() in content:
                high_priority_score += 2 if keyword.lower() in title.lower() else 1
        
        # 中优先级关键词检查
        medium_priority_score = 0
        for keyword in PRIORITY_KEYWORDS.get("medium", []):
            if keyword.lower() in content:
                medium_priority_score += 1
        
        # 基于关键词的优先级
        keyword_priority_score = 0
        important_keywords = ["breakthrough", "first", "novel", "fda", "approval", "clinical trial"]
        for keyword in keywords:
            if any(imp_kw in keyword.lower() for imp_kw in important_keywords):
                keyword_priority_score += 1
        
        # 计算总分
        total_score = high_priority_score * 3 + medium_priority_score + keyword_priority_score
        
        if total_score >= 6:
            return "high"
        elif total_score >= 3:
            return "medium"
        else:
            return "low"
    
    def _calculate_relevance_score(self, title: str, summary: str, keywords: List[str]) -> float:
        """计算相关性评分"""
        content = f"{title} {summary}".lower()
        
        # 生物医学相关关键词
        biomedical_keywords = [
            "medical", "medicine", "clinical", "patient", "disease", "treatment",
            "therapy", "drug", "pharmaceutical", "biology", "biotech", "genetic",
            "gene", "protein", "cell", "molecular", "research", "study", "trial"
        ]
        
        relevance_score = 0
        total_keywords = len(biomedical_keywords)
        
        for keyword in biomedical_keywords:
            if keyword in content:
                relevance_score += 1
        
        # 标准化评分
        normalized_score = relevance_score / total_keywords
        
        # 基于关键词的额外评分
        keyword_bonus = len([kw for kw in keywords if any(bio_kw in kw.lower() for bio_kw in biomedical_keywords)]) * 0.1
        
        final_score = min(normalized_score + keyword_bonus, 1.0)
        return round(final_score, 3)
    
    async def _calculate_trending_score(self, keywords: List[str]) -> float:
        """计算趋势评分"""
        try:
            # 从Redis获取关键词趋势数据
            trending_score = 0
            
            for keyword in keywords:
                # 获取关键词在过去24小时的出现频率
                key = f"keyword_trend:{keyword.lower()}"
                frequency = await redis_client.get(key)
                
                if frequency:
                    trending_score += int(frequency)
                
                # 更新关键词频率
                await redis_client.incr(key)
                await redis_client.expire(key, 86400)  # 24小时过期
            
            # 标准化评分
            max_possible_score = len(keywords) * 10  # 假设最大频率为10
            normalized_score = min(trending_score / max_possible_score, 1.0) if max_possible_score > 0 else 0
            
            return round(normalized_score, 3)
            
        except Exception as e:
            logger.error(f"计算趋势评分失败: {e}")
            return 0.5
    
    async def _generate_summary(self, content: str) -> str:
        """生成文章摘要"""
        try:
            if self.summarizer and len(content) > 100:
                # 使用Transformers模型生成摘要
                summary = self.summarizer(content[:1024], max_length=150, min_length=50, do_sample=False)
                return summary[0]['summary_text']
            
            elif settings.OPENAI_API_KEY:
                # 使用OpenAI生成摘要
                response = await openai.ChatCompletion.acreate(
                    model=settings.OPENAI_MODEL,
                    messages=[
                        {"role": "system", "content": "你是一个专业的生物医学文章摘要生成器。请为以下文章生成一个简洁的中文摘要，不超过100字。"},
                        {"role": "user", "content": content[:2000]}
                    ],
                    max_tokens=200,
                    temperature=0.3
                )
                return response.choices[0].message.content.strip()
            
            else:
                # 简单的摘要生成：取前两句话
                sentences = re.split(r'[.!?。！？]', content)
                summary_sentences = [s.strip() for s in sentences[:2] if s.strip()]
                return "。".join(summary_sentences) + "。" if summary_sentences else content[:200]
                
        except Exception as e:
            logger.error(f"生成摘要失败: {e}")
            return content[:200] + "..." if len(content) > 200 else content
    
    async def _detect_duplicates(self, title: str, summary: str) -> Tuple[bool, List[str]]:
        """检测重复文章"""
        try:
            if not self.embedding_model:
                return False, []
            
            content = f"{title} {summary}"
            content_embedding = self.embedding_model.encode([content])
            
            # 从Redis获取最近的文章进行比较
            recent_articles = await redis_client.lrange("recent_articles", 0, 100)
            similar_articles = []
            
            for article_data in recent_articles:
                try:
                    article = json.loads(article_data)
                    other_content = f"{article.get('title', '')} {article.get('summary', '')}"
                    other_embedding = self.embedding_model.encode([other_content])
                    
                    # 计算相似度
                    similarity = np.dot(content_embedding[0], other_embedding[0]) / (
                        np.linalg.norm(content_embedding[0]) * np.linalg.norm(other_embedding[0])
                    )
                    
                    if similarity >= self.similarity_threshold:
                        similar_articles.append(article.get('id', ''))
                        
                except Exception as e:
                    continue
            
            is_duplicate = len(similar_articles) > 0
            
            # 将当前文章添加到最近文章列表
            await redis_client.lpush("recent_articles", json.dumps({
                "title": title,
                "summary": summary,
                "timestamp": datetime.utcnow().isoformat()
            }, ensure_ascii=False))
            await redis_client.ltrim("recent_articles", 0, 999)  # 保持列表大小
            
            return is_duplicate, similar_articles
            
        except Exception as e:
            logger.error(f"重复检测失败: {e}")
            return False, []
    
    async def get_classification_stats(self) -> Dict[str, Any]:
        """获取分类统计信息"""
        try:
            stats = {}
            
            # 从Redis获取分类统计
            for category in CLASSIFICATION_KEYWORDS.keys():
                count = await redis_client.get(f"category_count:{category}")
                stats[category] = int(count) if count else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"获取分类统计失败: {e}")
            return {}
    
    async def retrain_model(self, training_data: List[Dict[str, Any]]):
        """重新训练模型（预留接口）"""
        # 这里可以实现模型的重新训练逻辑
        logger.info("模型重训练功能待实现")
        pass
