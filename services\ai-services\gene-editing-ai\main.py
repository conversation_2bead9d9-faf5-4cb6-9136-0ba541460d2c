﻿#!/usr/bin/env python3
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Gene Editing AI")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"message": "Welcome to Gene Editing AI", "service": "gene-editing-ai"}

@app.get("/health")
def health():
    return {"status": "healthy", "service": "gene-editing-ai"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=9005)
