"""
生科云码基因编辑AI
智能设计CRISPR gRNAs和基因编辑工具
"""

import asyncio
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from fastapi import FastAPI, HTTPException, Depends, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
import asyncpg
import redis.asyncio as redis
from loguru import logger
import numpy as np
from Bio.Seq import Seq

# 配置日志
logger.add("logs/gene_editing_ai.log", rotation="1 day", retention="30 days")

app = FastAPI(
    title="生科云码基因编辑AI",
    description="智能设计CRISPR gRNAs和基因编辑工具",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置
REDIS_URL = "redis://redis:6379/4"
DATABASE_URL = "*************************************************/biocloude"

# 全局变量
redis_client: Optional[redis.Redis] = None
db_pool: Optional[asyncpg.Pool] = None

# 数据模型
class CRISPRDesignRequest(BaseModel):
    target_sequence: str = Field(..., description="目标序列")
    pam_sequence: str = Field(default="NGG", description="PAM序列")
    guide_length: int = Field(default=20, ge=17, le=24, description="gRNA长度")
    cas_type: str = Field(default="Cas9", description="Cas蛋白类型")
    editing_type: str = Field(default="knockout", description="编辑类型：knockout, knockin, base_editing")
    off_target_threshold: float = Field(default=0.1, description="脱靶阈值")
    
    @validator('target_sequence')
    def validate_sequence(cls, v):
        if not re.match(r'^[ATCG]+$', v.upper()):
            raise ValueError('序列只能包含A、T、C、G字符')
        return v.upper()

class OffTargetAnalysisRequest(BaseModel):
    guide_rna: str = Field(..., description="gRNA序列")
    genome_sequence: Optional[str] = Field(None, description="基因组序列")
    mismatch_tolerance: int = Field(default=3, description="允许的错配数")
    pam_sequence: str = Field(default="NGG", description="PAM序列")

class BaseEditingRequest(BaseModel):
    target_sequence: str = Field(..., description="目标序列")
    target_position: int = Field(..., description="目标位置")
    edit_type: str = Field(..., description="编辑类型：C_to_T, A_to_G")
    base_editor: str = Field(default="BE3", description="碱基编辑器类型")
    
class PrimeEditingRequest(BaseModel):
    target_sequence: str = Field(..., description="目标序列")
    desired_edit: str = Field(..., description="期望的编辑")
    pbs_length: int = Field(default=13, description="PBS长度")
    rt_template_length: int = Field(default=10, description="RT模板长度")

class HDRTemplateRequest(BaseModel):
    target_sequence: str = Field(..., description="目标序列")
    insertion_sequence: str = Field(..., description="插入序列")
    homology_arm_length: int = Field(default=500, description="同源臂长度")

# CRISPR设计服务
class CRISPRDesignService:
    """CRISPR设计服务"""
    
    def __init__(self):
        self.pam_patterns = {
            "Cas9": "NGG",
            "Cas12a": "TTTV",
            "Cas12b": "TTN",
            "Cas13": "H",  # A, C, or U
            "CasX": "TTCN"
        }
        
        # 脱靶评分权重
        self.mismatch_weights = {
            1: 1.0,   # 种子区域
            2: 0.9,
            3: 0.8,
            4: 0.7,
            5: 0.6,
            6: 0.5,
            7: 0.4,
            8: 0.3,
            9: 0.2,
            10: 0.1
        }
    
    async def design_grnas(self, request: CRISPRDesignRequest) -> Dict[str, Any]:
        """设计gRNAs"""
        try:
            # 查找所有可能的gRNA位点
            candidate_sites = self._find_grna_sites(request)
            
            # 评估每个候选位点
            evaluated_grnas = []
            for site in candidate_sites:
                score = self._evaluate_grna(site, request)
                off_target_score = await self._calculate_off_target_score(site["sequence"])
                
                evaluated_grnas.append({
                    "sequence": site["sequence"],
                    "position": site["position"],
                    "strand": site["strand"],
                    "pam": site["pam"],
                    "on_target_score": score,
                    "off_target_score": off_target_score,
                    "overall_score": score * (1 - off_target_score),
                    "gc_content": self._calculate_gc_content(site["sequence"])
                })
            
            # 排序并返回最佳gRNAs
            best_grnas = sorted(evaluated_grnas, key=lambda x: x['overall_score'], reverse=True)[:20]
            
            return {
                "target_sequence": request.target_sequence,
                "design_parameters": request.dict(),
                "grnas": best_grnas,
                "total_candidates": len(candidate_sites),
                "design_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"gRNA设计失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _find_grna_sites(self, request: CRISPRDesignRequest) -> List[Dict[str, Any]]:
        """查找gRNA位点"""
        sites = []
        sequence = request.target_sequence
        pam_pattern = self.pam_patterns.get(request.cas_type, request.pam_sequence)
        
        # 正向链搜索
        for i in range(len(sequence) - request.guide_length - len(pam_pattern) + 1):
            # 检查PAM序列
            pam_start = i + request.guide_length
            pam_seq = sequence[pam_start:pam_start + len(pam_pattern)]
            
            if self._match_pam(pam_seq, pam_pattern):
                grna_seq = sequence[i:i + request.guide_length]
                sites.append({
                    "sequence": grna_seq,
                    "position": i + 1,
                    "strand": "+",
                    "pam": pam_seq
                })
        
        # 反向链搜索
        rev_comp = str(Seq(sequence).reverse_complement())
        for i in range(len(rev_comp) - request.guide_length - len(pam_pattern) + 1):
            pam_start = i + request.guide_length
            pam_seq = rev_comp[pam_start:pam_start + len(pam_pattern)]
            
            if self._match_pam(pam_seq, pam_pattern):
                grna_seq = rev_comp[i:i + request.guide_length]
                # 转换回原始坐标
                original_pos = len(sequence) - (i + request.guide_length)
                sites.append({
                    "sequence": grna_seq,
                    "position": original_pos,
                    "strand": "-",
                    "pam": pam_seq
                })
        
        return sites
    
    def _match_pam(self, sequence: str, pattern: str) -> bool:
        """匹配PAM序列"""
        if len(sequence) != len(pattern):
            return False
        
        for i, (s, p) in enumerate(zip(sequence, pattern)):
            if p == 'N':  # 任意碱基
                continue
            elif p == 'V':  # A, C, or G
                if s not in 'ACG':
                    return False
            elif p == 'H':  # A, C, or T
                if s not in 'ACT':
                    return False
            elif p != s:
                return False
        
        return True
    
    def _evaluate_grna(self, site: Dict[str, Any], request: CRISPRDesignRequest) -> float:
        """评估gRNA质量"""
        sequence = site["sequence"]
        score = 100.0
        
        # GC含量评分
        gc_content = self._calculate_gc_content(sequence)
        if 40 <= gc_content <= 60:
            score += 10
        else:
            score -= abs(gc_content - 50) * 0.2
        
        # 避免连续相同碱基
        for base in 'ATCG':
            if base * 4 in sequence:  # 连续4个相同碱基
                score -= 20
        
        # 种子区域评分（后8个碱基）
        seed_region = sequence[-8:]
        seed_gc = self._calculate_gc_content(seed_region)
        if 25 <= seed_gc <= 75:
            score += 5
        
        # 避免T开头（转录效率低）
        if sequence.startswith('T'):
            score -= 5
        
        # 位置评分（靠近目标位置更好）
        target_center = len(request.target_sequence) // 2
        distance_penalty = abs(site["position"] - target_center) * 0.01
        score -= distance_penalty
        
        return max(score, 0.0)
    
    async def _calculate_off_target_score(self, grna_sequence: str) -> float:
        """计算脱靶评分"""
        # 简化的脱靶评分
        # 实际应用中需要与基因组数据库比对
        
        # 基于序列复杂度的简单评估
        complexity = len(set(grna_sequence)) / 4.0  # 碱基多样性
        repeat_penalty = 0
        
        # 检查重复序列
        for i in range(len(grna_sequence) - 2):
            triplet = grna_sequence[i:i+3]
            if grna_sequence.count(triplet) > 1:
                repeat_penalty += 0.1
        
        off_target_risk = repeat_penalty + (1 - complexity) * 0.5
        return min(off_target_risk, 1.0)
    
    def _calculate_gc_content(self, sequence: str) -> float:
        """计算GC含量"""
        gc_count = sequence.count('G') + sequence.count('C')
        return (gc_count / len(sequence)) * 100

class OffTargetAnalysisService:
    """脱靶分析服务"""
    
    async def analyze_off_targets(self, request: OffTargetAnalysisRequest) -> Dict[str, Any]:
        """分析脱靶效应"""
        try:
            # 模拟脱靶分析
            off_targets = self._find_potential_off_targets(request)
            
            # 评估脱靶风险
            risk_assessment = self._assess_off_target_risk(off_targets)
            
            return {
                "guide_rna": request.guide_rna,
                "off_targets": off_targets,
                "risk_assessment": risk_assessment,
                "total_off_targets": len(off_targets),
                "high_risk_sites": len([ot for ot in off_targets if ot["risk"] == "high"]),
                "analysis_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"脱靶分析失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _find_potential_off_targets(self, request: OffTargetAnalysisRequest) -> List[Dict[str, Any]]:
        """查找潜在脱靶位点"""
        # 模拟脱靶位点发现
        off_targets = []
        
        # 生成一些模拟的脱靶位点
        for i in range(np.random.randint(5, 20)):
            # 生成与原始gRNA相似的序列
            off_target_seq = self._generate_similar_sequence(
                request.guide_rna, 
                request.mismatch_tolerance
            )
            
            mismatches = self._count_mismatches(request.guide_rna, off_target_seq)
            
            off_targets.append({
                "sequence": off_target_seq,
                "chromosome": f"chr{np.random.randint(1, 23)}",
                "position": np.random.randint(1000000, 100000000),
                "strand": np.random.choice(["+", "-"]),
                "mismatches": mismatches,
                "score": self._calculate_off_target_score(request.guide_rna, off_target_seq),
                "risk": self._classify_risk(mismatches)
            })
        
        return sorted(off_targets, key=lambda x: x["score"], reverse=True)
    
    def _generate_similar_sequence(self, original: str, max_mismatches: int) -> str:
        """生成相似序列"""
        sequence = list(original)
        num_mismatches = np.random.randint(1, max_mismatches + 1)
        
        positions = np.random.choice(len(sequence), num_mismatches, replace=False)
        
        for pos in positions:
            original_base = sequence[pos]
            new_base = np.random.choice([b for b in 'ATCG' if b != original_base])
            sequence[pos] = new_base
        
        return ''.join(sequence)
    
    def _count_mismatches(self, seq1: str, seq2: str) -> int:
        """计算错配数"""
        return sum(1 for a, b in zip(seq1, seq2) if a != b)
    
    def _calculate_off_target_score(self, guide: str, target: str) -> float:
        """计算脱靶评分"""
        if len(guide) != len(target):
            return 0.0
        
        score = 1.0
        for i, (g, t) in enumerate(zip(guide, target)):
            if g != t:
                # 种子区域（后12个碱基）权重更高
                if i >= len(guide) - 12:
                    score *= 0.1
                else:
                    score *= 0.5
        
        return score
    
    def _classify_risk(self, mismatches: int) -> str:
        """分类风险等级"""
        if mismatches <= 2:
            return "high"
        elif mismatches <= 4:
            return "medium"
        else:
            return "low"
    
    def _assess_off_target_risk(self, off_targets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """评估总体脱靶风险"""
        if not off_targets:
            return {"overall_risk": "low", "recommendation": "安全使用"}
        
        high_risk_count = len([ot for ot in off_targets if ot["risk"] == "high"])
        medium_risk_count = len([ot for ot in off_targets if ot["risk"] == "medium"])
        
        if high_risk_count > 5:
            overall_risk = "high"
            recommendation = "建议重新设计gRNA"
        elif high_risk_count > 0 or medium_risk_count > 10:
            overall_risk = "medium"
            recommendation = "建议进一步验证"
        else:
            overall_risk = "low"
            recommendation = "可以安全使用"
        
        return {
            "overall_risk": overall_risk,
            "recommendation": recommendation,
            "high_risk_sites": high_risk_count,
            "medium_risk_sites": medium_risk_count,
            "total_sites": len(off_targets)
        }

class BaseEditingService:
    """碱基编辑服务"""
    
    async def design_base_editing(self, request: BaseEditingRequest) -> Dict[str, Any]:
        """设计碱基编辑"""
        try:
            # 查找合适的gRNA位点
            suitable_sites = self._find_base_editing_sites(request)
            
            # 评估编辑效率
            for site in suitable_sites:
                site["editing_efficiency"] = self._predict_editing_efficiency(site, request)
                site["editing_window"] = self._calculate_editing_window(site, request.base_editor)
            
            # 排序并返回最佳位点
            best_sites = sorted(suitable_sites, key=lambda x: x["editing_efficiency"], reverse=True)[:10]
            
            return {
                "target_sequence": request.target_sequence,
                "target_position": request.target_position,
                "edit_type": request.edit_type,
                "base_editor": request.base_editor,
                "suitable_sites": best_sites,
                "design_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"碱基编辑设计失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _find_base_editing_sites(self, request: BaseEditingRequest) -> List[Dict[str, Any]]:
        """查找碱基编辑位点"""
        sites = []
        sequence = request.target_sequence
        target_pos = request.target_position - 1  # 转换为0基索引
        
        # 搜索合适的gRNA位点
        for i in range(max(0, target_pos - 30), min(len(sequence) - 23, target_pos + 30)):
            grna_seq = sequence[i:i+20]
            pam_seq = sequence[i+20:i+23]
            
            if pam_seq.endswith('GG'):  # 简化的PAM检查
                # 检查目标位置是否在编辑窗口内
                relative_pos = target_pos - i
                if 4 <= relative_pos <= 8:  # 典型的编辑窗口
                    sites.append({
                        "grna_sequence": grna_seq,
                        "pam_sequence": pam_seq,
                        "position": i + 1,
                        "strand": "+",
                        "target_in_window": True,
                        "distance_to_target": abs(relative_pos - 6)  # 最佳位置是第6位
                    })
        
        return sites
    
    def _predict_editing_efficiency(self, site: Dict[str, Any], request: BaseEditingRequest) -> float:
        """预测编辑效率"""
        base_efficiency = 0.8
        
        # 距离目标位置的影响
        distance_penalty = site["distance_to_target"] * 0.1
        efficiency = base_efficiency - distance_penalty
        
        # GC含量影响
        gc_content = (site["grna_sequence"].count('G') + site["grna_sequence"].count('C')) / 20
        if 0.4 <= gc_content <= 0.6:
            efficiency += 0.1
        
        # 目标碱基上下文影响
        target_context = self._get_target_context(site, request)
        if target_context in ['AC', 'TC']:  # C->T编辑的优选上下文
            efficiency += 0.15
        
        return min(efficiency, 1.0)
    
    def _calculate_editing_window(self, site: Dict[str, Any], base_editor: str) -> Dict[str, Any]:
        """计算编辑窗口"""
        # 不同碱基编辑器的编辑窗口
        windows = {
            "BE3": {"start": 4, "end": 8, "optimal": 6},
            "BE4max": {"start": 4, "end": 8, "optimal": 6},
            "ABE7.10": {"start": 4, "end": 8, "optimal": 6},
            "ABE8e": {"start": 4, "end": 8, "optimal": 6}
        }
        
        return windows.get(base_editor, {"start": 4, "end": 8, "optimal": 6})
    
    def _get_target_context(self, site: Dict[str, Any], request: BaseEditingRequest) -> str:
        """获取目标碱基上下文"""
        # 简化的上下文分析
        return "AC"  # 示例返回值

# 初始化服务
crispr_design_service = CRISPRDesignService()
off_target_analysis_service = OffTargetAnalysisService()
base_editing_service = BaseEditingService()

# 启动事件
@app.on_event("startup")
async def startup_event():
    global redis_client, db_pool
    
    # 初始化Redis
    redis_client = redis.from_url(REDIS_URL)
    
    # 初始化数据库连接池
    db_pool = await asyncpg.create_pool(DATABASE_URL)
    
    logger.info("🚀 Gene Editing AI服务启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    global redis_client, db_pool
    
    if redis_client:
        await redis_client.close()
    
    if db_pool:
        await db_pool.close()
    
    logger.info("👋 Gene Editing AI服务关闭")

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "gene-editing-ai",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0"
    }

@app.post("/api/v1/design-crispr")
async def design_crispr(request: CRISPRDesignRequest):
    """设计CRISPR gRNAs"""
    result = await crispr_design_service.design_grnas(request)
    
    # 更新统计
    await redis_client.incr("stats:total_crispr_designs")
    
    return JSONResponse(content=result)

@app.post("/api/v1/analyze-off-targets")
async def analyze_off_targets(request: OffTargetAnalysisRequest):
    """分析脱靶效应"""
    result = await off_target_analysis_service.analyze_off_targets(request)
    
    # 更新统计
    await redis_client.incr("stats:total_off_target_analyses")
    
    return JSONResponse(content=result)

@app.post("/api/v1/design-base-editing")
async def design_base_editing(request: BaseEditingRequest):
    """设计碱基编辑"""
    result = await base_editing_service.design_base_editing(request)
    
    # 更新统计
    await redis_client.incr("stats:total_base_editing_designs")
    
    return JSONResponse(content=result)

@app.get("/api/v1/stats")
async def get_stats():
    """获取统计信息"""
    try:
        stats = {
            "total_crispr_designs": int(await redis_client.get("stats:total_crispr_designs") or 0),
            "total_off_target_analyses": int(await redis_client.get("stats:total_off_target_analyses") or 0),
            "total_base_editing_designs": int(await redis_client.get("stats:total_base_editing_designs") or 0),
            "success_rate": 0.91,  # 示例数据
            "avg_design_time": 1.8  # 示例数据
        }
        
        return JSONResponse(content={"stats": stats})
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9005)
