#!/bin/bash

# 测试路径修复是否成功

echo "🧪 测试文件路径..."

# 检测项目根目录
if [ -f "package.json" ] || [ -f "docker-compose.yml" ] || [ -d ".devcontainer" ]; then
    PROJECT_ROOT=$(pwd)
elif [ -f "/workspace/biocloude/package.json" ] || [ -f "/workspace/biocloude/docker-compose.yml" ]; then
    PROJECT_ROOT="/workspace/biocloude"
    cd "$PROJECT_ROOT"
else
    echo "❌ 无法找到项目根目录"
    exit 1
fi

echo "📍 项目根目录: $PROJECT_ROOT"

# 检查 AI 服务文件
echo ""
echo "🤖 检查 AI 服务文件:"
ai_services=("newsletter-ai" "scholar-ai" "primer-ai" "protein-ai" "gene-editing-ai" "metabolic-ai")
for service in "${ai_services[@]}"; do
    main_py="$PROJECT_ROOT/services/ai-services/$service/main.py"
    if [ -f "$main_py" ]; then
        echo "  ✅ $service/main.py 存在"
    else
        echo "  ❌ $service/main.py 不存在: $main_py"
    fi
done

# 检查后端服务文件
echo ""
echo "🔧 检查后端服务文件:"
backend_services=("auth-service" "subscription-service")
for service in "${backend_services[@]}"; do
    main_go="$PROJECT_ROOT/services/$service/main.go"
    if [ -f "$main_go" ]; then
        echo "  ✅ $service/main.go 存在"
    else
        echo "  ❌ $service/main.go 不存在: $main_go"
    fi
done

# 检查前端应用文件
echo ""
echo "🌐 检查前端应用文件:"
frontend_apps=("main-portal" "newsletter-app" "scholar-app")
for app in "${frontend_apps[@]}"; do
    package_json="$PROJECT_ROOT/web-apps/$app/package.json"
    if [ -f "$package_json" ]; then
        echo "  ✅ $app/package.json 存在"
    else
        echo "  ❌ $app/package.json 不存在: $package_json"
    fi
done

# 检查 PID 目录
echo ""
echo "📁 检查 PID 目录:"
pids_dir="$PROJECT_ROOT/.pids"
if [ -d "$pids_dir" ]; then
    echo "  ✅ .pids 目录存在: $pids_dir"
else
    echo "  ⚠️ .pids 目录不存在，将在启动时创建: $pids_dir"
fi

# 测试路径解析
echo ""
echo "🔍 测试路径解析:"
echo "  PROJECT_ROOT: $PROJECT_ROOT"
echo "  AI 服务目录: $PROJECT_ROOT/services/ai-services/"
echo "  后端服务目录: $PROJECT_ROOT/services/"
echo "  前端应用目录: $PROJECT_ROOT/web-apps/"
echo "  PID 目录: $PROJECT_ROOT/.pids/"

echo ""
echo "🎯 路径测试完成！"
echo "现在可以运行: ./start-dev.sh"
