version: '3.8'

services:
  workspace:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspace:cached
      - /var/run/docker.sock:/var/run/docker-host.sock
    command: sleep infinity
    networks:
      - biocloude-dev
    environment:
      - DOCKER_HOST=unix:///var/run/docker-host.sock
    depends_on:
      - postgres
      - redis

  # 数据库服务
  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: biocloude
      POSTGRES_USER: biocloude
      POSTGRES_PASSWORD: biocloude123
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - biocloude-dev
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U biocloude"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis服务
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass biocloude123
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - biocloude-dev
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "biocloude123", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 邮件服务 (开发环境)
  mailhog:
    image: mailhog/mailhog:latest
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - biocloude-dev

  # MinIO (S3兼容存储)
  minio:
    image: minio/minio:latest
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: biocloude
      MINIO_ROOT_PASSWORD: biocloude123
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"  # API
      - "9090:9001"  # Console
    networks:
      - biocloude-dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Prometheus (监控)
  prometheus:
    image: prom/prometheus:latest
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9091:9090"
    networks:
      - biocloude-dev

  # Grafana (可视化)
  grafana:
    image: grafana/grafana:latest
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: biocloude123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3030:3000"
    networks:
      - biocloude-dev
    depends_on:
      - prometheus

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  biocloude-dev:
    driver: bridge
