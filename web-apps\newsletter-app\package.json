{"name": "biocloude-newsletter-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"react": "^18", "react-dom": "^18", "next": "15.3.5", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0", "framer-motion": "^10.16.16", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^0.1.0", "@radix-ui/react-card": "^0.1.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-input": "^0.1.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-sheet": "^0.1.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-table": "^0.1.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-textarea": "^0.1.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "zustand": "^4.4.7", "recharts": "^2.8.0", "date-fns": "^2.30.0", "rss-parser": "^3.13.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.3.0", "eslint": "^8", "eslint-config-next": "15.3.5", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16"}}