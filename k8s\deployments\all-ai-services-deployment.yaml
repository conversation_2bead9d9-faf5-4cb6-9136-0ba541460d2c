apiVersion: apps/v1
kind: Deployment
metadata:
  name: scholar-ai
  namespace: biocloude
  labels:
    app: scholar-ai
    component: ai-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: scholar-ai
  template:
    metadata:
      labels:
        app: scholar-ai
        component: ai-service
    spec:
      containers:
      - name: scholar-ai
        image: biocloude/scholar-ai:latest
        ports:
        - containerPort: 9002
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: redis-url
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9002
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 9002
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: scholar-ai-service
  namespace: biocloude
  labels:
    app: scholar-ai
spec:
  selector:
    app: scholar-ai
  ports:
  - name: http
    port: 9002
    targetPort: 9002
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: primer-ai
  namespace: biocloude
  labels:
    app: primer-ai
    component: ai-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: primer-ai
  template:
    metadata:
      labels:
        app: primer-ai
        component: ai-service
    spec:
      containers:
      - name: primer-ai
        image: biocloude/primer-ai:latest
        ports:
        - containerPort: 9003
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: redis-url
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9003
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 9003
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: primer-ai-service
  namespace: biocloude
  labels:
    app: primer-ai
spec:
  selector:
    app: primer-ai
  ports:
  - name: http
    port: 9003
    targetPort: 9003
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: protein-ai
  namespace: biocloude
  labels:
    app: protein-ai
    component: ai-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: protein-ai
  template:
    metadata:
      labels:
        app: protein-ai
        component: ai-service
    spec:
      containers:
      - name: protein-ai
        image: biocloude/protein-ai:latest
        ports:
        - containerPort: 9004
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: redis-url
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9004
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 9004
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: protein-ai-service
  namespace: biocloude
  labels:
    app: protein-ai
spec:
  selector:
    app: protein-ai
  ports:
  - name: http
    port: 9004
    targetPort: 9004
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gene-editing-ai
  namespace: biocloude
  labels:
    app: gene-editing-ai
    component: ai-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: gene-editing-ai
  template:
    metadata:
      labels:
        app: gene-editing-ai
        component: ai-service
    spec:
      containers:
      - name: gene-editing-ai
        image: biocloude/gene-editing-ai:latest
        ports:
        - containerPort: 9005
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: redis-url
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9005
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 9005
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: gene-editing-ai-service
  namespace: biocloude
  labels:
    app: gene-editing-ai
spec:
  selector:
    app: gene-editing-ai
  ports:
  - name: http
    port: 9005
    targetPort: 9005
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metabolic-ai
  namespace: biocloude
  labels:
    app: metabolic-ai
    component: ai-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: metabolic-ai
  template:
    metadata:
      labels:
        app: metabolic-ai
        component: ai-service
    spec:
      containers:
      - name: metabolic-ai
        image: biocloude/metabolic-ai:latest
        ports:
        - containerPort: 9006
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: redis-url
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9006
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 9006
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: metabolic-ai-service
  namespace: biocloude
  labels:
    app: metabolic-ai
spec:
  selector:
    app: metabolic-ai
  ports:
  - name: http
    port: 9006
    targetPort: 9006
  type: ClusterIP
