# 生科云码平台开发环境停止脚本 (PowerShell版本)
# 停止所有前端和后端应用服务

Write-Host "🛑 停止生科云码平台开发环境..." -ForegroundColor Red

# 定义要停止的端口
$ports = @(3000, 3001, 3002, 8001, 8002, 9001, 9002, 9003, 9004, 9005, 9006)

# 定义服务名称映射
$serviceNames = @{
    3000 = "主站门户"
    3001 = "资讯处理AI应用"
    3002 = "文献阅读AI应用"
    8001 = "认证服务"
    8002 = "订阅服务"
    9001 = "资讯处理AI"
    9002 = "文献阅读AI"
    9003 = "引物AI"
    9004 = "蛋白质设计AI"
    9005 = "基因编辑AI"
    9006 = "代谢工程AI"
}

Write-Host "🔍 检查并停止服务..." -ForegroundColor Blue

$stoppedCount = 0
$totalCount = 0

foreach ($port in $ports) {
    $totalCount++
    $serviceName = $serviceNames[$port]
    
    try {
        # 查找占用端口的进程
        $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        
        if ($connections) {
            foreach ($connection in $connections) {
                $processId = $connection.OwningProcess
                $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
                
                if ($process) {
                    Write-Host "🛑 停止 $serviceName (端口 $port, PID: $processId)..." -ForegroundColor Yellow
                    
                    try {
                        # 尝试优雅停止
                        $process.CloseMainWindow() | Out-Null
                        Start-Sleep -Seconds 2
                        
                        # 检查是否还在运行
                        if (-not $process.HasExited) {
                            Write-Host "   强制停止 $serviceName..." -ForegroundColor Red
                            Stop-Process -Id $processId -Force
                        }
                        
                        Write-Host "   ✅ $serviceName 已停止" -ForegroundColor Green
                        $stoppedCount++
                    }
                    catch {
                        Write-Host "   ❌ 停止 $serviceName 失败: $($_.Exception.Message)" -ForegroundColor Red
                    }
                }
            }
        }
        else {
            Write-Host "ℹ️ 端口 $port ($serviceName) 没有运行的进程" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "❌ 检查端口 $port 时出错: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 停止可能的Node.js和Go进程
Write-Host ""
Write-Host "🔍 检查并停止相关进程..." -ForegroundColor Blue

# 停止Node.js进程 (可能是前端应用)
$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
if ($nodeProcesses) {
    Write-Host "🛑 停止 Node.js 进程..." -ForegroundColor Yellow
    foreach ($process in $nodeProcesses) {
        try {
            # 检查是否是我们的开发服务器
            $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($process.Id)").CommandLine
            if ($commandLine -and ($commandLine -like "*npm run dev*" -or $commandLine -like "*next dev*")) {
                Write-Host "   停止 Node.js 开发服务器 (PID: $($process.Id))..." -ForegroundColor Yellow
                Stop-Process -Id $process.Id -Force
                Write-Host "   ✅ Node.js 进程已停止" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "   ❌ 停止 Node.js 进程失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 停止Go进程 (可能是后端服务)
$goProcesses = Get-Process -Name "go" -ErrorAction SilentlyContinue
if ($goProcesses) {
    Write-Host "🛑 停止 Go 进程..." -ForegroundColor Yellow
    foreach ($process in $goProcesses) {
        try {
            Write-Host "   停止 Go 进程 (PID: $($process.Id))..." -ForegroundColor Yellow
            Stop-Process -Id $process.Id -Force
            Write-Host "   ✅ Go 进程已停止" -ForegroundColor Green
        }
        catch {
            Write-Host "   ❌ 停止 Go 进程失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 停止Python进程 (可能是AI服务)
$pythonProcesses = Get-Process -Name "python" -ErrorAction SilentlyContinue
if ($pythonProcesses) {
    Write-Host "🛑 停止 Python 进程..." -ForegroundColor Yellow
    foreach ($process in $pythonProcesses) {
        try {
            # 检查是否是我们的AI服务
            $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($process.Id)").CommandLine
            if ($commandLine -and $commandLine -like "*main.py*") {
                Write-Host "   停止 Python AI 服务 (PID: $($process.Id))..." -ForegroundColor Yellow
                Stop-Process -Id $process.Id -Force
                Write-Host "   ✅ Python 进程已停止" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "   ❌ 停止 Python 进程失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 清理PID文件
Write-Host ""
Write-Host "🧹 清理PID文件..." -ForegroundColor Blue
if (Test-Path ".pids") {
    try {
        Remove-Item ".pids\*.pid" -Force -ErrorAction SilentlyContinue
        Write-Host "✅ PID文件已清理" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️ 清理PID文件时出现问题: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# 最终状态检查
Write-Host ""
Write-Host "🔍 检查端口状态..." -ForegroundColor Blue

$stillRunning = 0
foreach ($port in $ports) {
    $serviceName = $serviceNames[$port]
    $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    
    if ($connections) {
        Write-Host "⚠️ 端口 $port ($serviceName) 仍有进程运行" -ForegroundColor Yellow
        $stillRunning++
    } else {
        Write-Host "✅ 端口 $port ($serviceName) 已释放" -ForegroundColor Green
    }
}

# 显示总结
Write-Host ""
Write-Host "📊 停止操作总结:" -ForegroundColor Cyan
Write-Host "  🛑 已停止服务: $stoppedCount"
Write-Host "  📊 检查的端口: $totalCount"
Write-Host "  ⚠️ 仍在运行: $stillRunning"

if ($stillRunning -eq 0) {
    Write-Host ""
    Write-Host "🎉 所有服务已成功停止！" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⚠️ 部分服务可能仍在运行，请手动检查" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "💡 提示:" -ForegroundColor Cyan
Write-Host "  重新启动服务: .\start-dev.ps1"
Write-Host "  查看服务状态: .\status-dev.ps1"
Write-Host ""

Read-Host "按回车键关闭"
