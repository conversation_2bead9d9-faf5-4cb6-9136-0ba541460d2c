#!/bin/bash

# 生科云码平台开发环境测试脚本
# 运行所有测试套件

set -e

echo "🧪 运行生科云码平台测试套件..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 记录测试结果
record_test() {
    local test_name="$1"
    local result="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$result" = "PASS" ]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "✅ $test_name"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_error "❌ $test_name"
    fi
}

# 检查服务是否运行
check_service_running() {
    local port="$1"
    local service_name="$2"
    
    if curl -f -s --max-time 5 "http://localhost:$port/health" > /dev/null 2>&1; then
        return 0
    else
        log_warning "$service_name 未运行，跳过相关测试"
        return 1
    fi
}

# 运行Go测试
run_go_tests() {
    log_info "🔧 运行Go服务测试..."
    
    # 认证服务测试
    if [ -d "services/auth-service" ]; then
        log_info "测试认证服务..."
        cd services/auth-service
        
        if go test ./... -v; then
            record_test "认证服务单元测试" "PASS"
        else
            record_test "认证服务单元测试" "FAIL"
        fi
        
        cd ../..
    fi
    
    # 订阅服务测试
    if [ -d "services/subscription-service" ]; then
        log_info "测试订阅服务..."
        cd services/subscription-service
        
        if go test ./... -v; then
            record_test "订阅服务单元测试" "PASS"
        else
            record_test "订阅服务单元测试" "FAIL"
        fi
        
        cd ../..
    fi
}

# 运行Python测试
run_python_tests() {
    log_info "🐍 运行Python AI服务测试..."
    
    ai_services=("newsletter-ai" "scholar-ai" "primer-ai" "protein-ai" "gene-editing-ai" "metabolic-ai")
    
    for service in "${ai_services[@]}"; do
        if [ -d "services/ai-services/$service" ]; then
            log_info "测试 $service..."
            cd "services/ai-services/$service"
            
            # 检查是否有测试文件
            if [ -d "tests" ] || find . -name "test_*.py" -o -name "*_test.py" | grep -q .; then
                if python -m pytest tests/ -v 2>/dev/null || python -m pytest . -v 2>/dev/null; then
                    record_test "$service 单元测试" "PASS"
                else
                    record_test "$service 单元测试" "FAIL"
                fi
            else
                log_warning "$service 没有测试文件，跳过"
            fi
            
            cd ../../..
        fi
    done
}

# 运行前端测试
run_frontend_tests() {
    log_info "🌐 运行前端应用测试..."
    
    frontend_apps=("main-portal" "newsletter-app" "scholar-app")
    
    for app in "${frontend_apps[@]}"; do
        if [ -d "web-apps/$app" ]; then
            log_info "测试 $app..."
            cd "web-apps/$app"
            
            # 检查是否有测试脚本
            if npm run test --if-present 2>/dev/null; then
                record_test "$app 前端测试" "PASS"
            else
                # 尝试运行构建测试
                if npm run build 2>/dev/null; then
                    record_test "$app 构建测试" "PASS"
                else
                    record_test "$app 构建测试" "FAIL"
                fi
            fi
            
            cd ../..
        fi
    done
}

# 运行API集成测试
run_api_tests() {
    log_info "🔌 运行API集成测试..."
    
    # 测试认证服务API
    if check_service_running "8001" "认证服务"; then
        log_info "测试认证服务API..."
        
        # 健康检查
        if curl -f -s "http://localhost:8001/health" > /dev/null; then
            record_test "认证服务健康检查" "PASS"
        else
            record_test "认证服务健康检查" "FAIL"
        fi
        
        # 测试注册端点
        if curl -f -s -X POST "http://localhost:8001/api/v1/auth/register" \
           -H "Content-Type: application/json" \
           -d '{"email":"<EMAIL>","password":"test123","name":"Test User"}' > /dev/null; then
            record_test "认证服务注册API" "PASS"
        else
            record_test "认证服务注册API" "FAIL"
        fi
    fi
    
    # 测试订阅服务API
    if check_service_running "8002" "订阅服务"; then
        log_info "测试订阅服务API..."
        
        # 健康检查
        if curl -f -s "http://localhost:8002/health" > /dev/null; then
            record_test "订阅服务健康检查" "PASS"
        else
            record_test "订阅服务健康检查" "FAIL"
        fi
        
        # 测试产品列表
        if curl -f -s "http://localhost:8002/api/v1/products" > /dev/null; then
            record_test "订阅服务产品API" "PASS"
        else
            record_test "订阅服务产品API" "FAIL"
        fi
    fi
    
    # 测试AI服务API
    ai_ports=("9001" "9002" "9003" "9004" "9005" "9006")
    ai_names=("资讯AI" "文献AI" "引物AI" "蛋白质AI" "基因编辑AI" "代谢工程AI")
    
    for i in "${!ai_ports[@]}"; do
        local port="${ai_ports[$i]}"
        local name="${ai_names[$i]}"
        
        if check_service_running "$port" "$name"; then
            log_info "测试 $name API..."
            
            # 健康检查
            if curl -f -s "http://localhost:$port/health" > /dev/null; then
                record_test "$name 健康检查" "PASS"
            else
                record_test "$name 健康检查" "FAIL"
            fi
            
            # 统计信息
            if curl -f -s "http://localhost:$port/api/v1/stats" > /dev/null; then
                record_test "$name 统计API" "PASS"
            else
                record_test "$name 统计API" "FAIL"
            fi
        fi
    done
}

# 运行前端访问测试
run_frontend_access_tests() {
    log_info "🌐 运行前端访问测试..."
    
    # 测试主站门户
    if curl -f -s --max-time 10 "http://localhost:3000" > /dev/null 2>&1; then
        record_test "主站门户访问" "PASS"
    else
        record_test "主站门户访问" "FAIL"
    fi
    
    # 测试资讯处理AI应用
    if curl -f -s --max-time 10 "http://localhost:3001" > /dev/null 2>&1; then
        record_test "资讯处理AI应用访问" "PASS"
    else
        record_test "资讯处理AI应用访问" "FAIL"
    fi
    
    # 测试文献阅读AI应用
    if curl -f -s --max-time 10 "http://localhost:3002" > /dev/null 2>&1; then
        record_test "文献阅读AI应用访问" "PASS"
    else
        record_test "文献阅读AI应用访问" "FAIL"
    fi
}

# 运行数据库连接测试
run_database_tests() {
    log_info "🗄️ 运行数据库连接测试..."
    
    # 测试PostgreSQL连接
    if pg_isready -h localhost -p 5432 -U biocloude 2>/dev/null; then
        record_test "PostgreSQL连接" "PASS"
    else
        record_test "PostgreSQL连接" "FAIL"
    fi
    
    # 测试Redis连接
    if redis-cli -h localhost -p 6379 -a biocloude123 ping 2>/dev/null | grep -q "PONG"; then
        record_test "Redis连接" "PASS"
    else
        record_test "Redis连接" "FAIL"
    fi
}

# 运行性能测试
run_performance_tests() {
    log_info "⚡ 运行基础性能测试..."
    
    # 简单的响应时间测试
    if check_service_running "8001" "认证服务"; then
        local response_time=$(curl -o /dev/null -s -w '%{time_total}' "http://localhost:8001/health")
        local response_ms=$(echo "$response_time * 1000" | bc -l 2>/dev/null || echo "0")
        
        if (( $(echo "$response_time < 1.0" | bc -l 2>/dev/null || echo "0") )); then
            record_test "认证服务响应时间 (${response_ms%.*}ms)" "PASS"
        else
            record_test "认证服务响应时间 (${response_ms%.*}ms)" "FAIL"
        fi
    fi
}

# 显示测试结果
show_test_results() {
    echo ""
    echo "📊 测试结果总结"
    echo "================================"
    echo "  📋 总测试数: $TOTAL_TESTS"
    echo "  ✅ 通过: $PASSED_TESTS"
    echo "  ❌ 失败: $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        log_success "🎉 所有测试通过！"
        echo ""
        echo "💡 下一步:"
        echo "  运行完整性能测试: ./scripts/performance_test.sh"
        echo "  部署到生产环境: ./scripts/deploy.sh"
    else
        log_warning "⚠️ 有 $FAILED_TESTS 个测试失败"
        echo ""
        echo "🔧 建议:"
        echo "  1. 检查失败的服务是否正常运行"
        echo "  2. 查看服务日志排查问题"
        echo "  3. 确保所有依赖已正确安装"
        echo "  4. 重新运行测试: ./test-dev.sh"
    fi
    
    # 计算成功率
    if [ $TOTAL_TESTS -gt 0 ]; then
        local success_rate=$(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l 2>/dev/null || echo "0")
        echo "  📈 成功率: ${success_rate}%"
    fi
}

# 主函数
main() {
    log_info "🧪 开始生科云码平台测试..."
    
    # 检查必要工具
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，无法运行API测试"
        exit 1
    fi
    
    # 运行各类测试
    run_database_tests
    run_go_tests
    run_python_tests
    run_frontend_tests
    run_api_tests
    run_frontend_access_tests
    run_performance_tests
    
    # 显示结果
    show_test_results
    
    # 返回适当的退出码
    if [ $FAILED_TESTS -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 解析命令行参数
case "${1:-}" in
    --unit-only)
        log_info "仅运行单元测试..."
        run_go_tests
        run_python_tests
        run_frontend_tests
        show_test_results
        ;;
    --api-only)
        log_info "仅运行API测试..."
        run_api_tests
        show_test_results
        ;;
    --help)
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --unit-only    仅运行单元测试"
        echo "  --api-only     仅运行API集成测试"
        echo "  --help         显示此帮助信息"
        echo ""
        echo "默认行为: 运行所有测试"
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        echo "使用 --help 查看帮助信息"
        exit 1
        ;;
esac
