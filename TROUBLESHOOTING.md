# 🔧 故障排除指南

## 🚨 常见问题解决方案

### 问题1: `./start-dev.sh: No such file or directory`

#### 🎯 **原因分析**
- 脚本文件没有执行权限
- 在 Windows 环境下运行 bash 脚本
- 文件路径不正确

#### ✅ **解决方案**

##### 方案A: 修复权限 (Codespaces/Linux)
```bash
# 快速修复
chmod +x *.sh
./start-dev.sh

# 或使用修复脚本
bash fix-permissions.sh
```

##### 方案B: 使用 bash 命令
```bash
# 直接用 bash 运行
bash start-dev.sh
bash status-dev.sh
bash stop-dev.sh
bash test-dev.sh
```

##### 方案C: Windows 环境
```powershell
# 使用 PowerShell 脚本
.\start-dev.ps1
.\status-dev.ps1
.\stop-dev.ps1

# 或使用批处理文件
start-dev.bat
```

##### 方案D: Git Bash (Windows)
```bash
# 在 Git Bash 中运行
bash start-dev.sh
```

### 问题2: `zsh: permission denied: ./start-dev.sh`

#### ✅ **解决方案**
```bash
# 添加执行权限
chmod +x start-dev.sh
chmod +x status-dev.sh
chmod +x stop-dev.sh
chmod +x test-dev.sh

# 然后运行
./start-dev.sh
```

### 问题3: 服务启动失败

#### 🔍 **检查步骤**
```bash
# 1. 检查基础服务
./status-dev.sh

# 2. 检查端口占用
netstat -tulpn | grep :3000
# 或在 Windows 中
netstat -an | findstr :3000

# 3. 检查进程
ps aux | grep node
ps aux | grep go
ps aux | grep python
```

#### ✅ **解决方案**
```bash
# 停止所有服务
./stop-dev.sh

# 等待几秒钟
sleep 5

# 重新启动
./start-dev.sh
```

### 问题4: 端口冲突

#### 🔍 **检查端口占用**
```bash
# Linux/macOS
lsof -i :3000
lsof -i :8001

# Windows
netstat -ano | findstr :3000
netstat -ano | findstr :8001
```

#### ✅ **解决方案**
```bash
# 停止冲突的进程
./stop-dev.sh

# 或手动杀死进程 (Linux/macOS)
sudo kill -9 $(lsof -ti:3000)

# Windows PowerShell
Get-Process -Id (Get-NetTCPConnection -LocalPort 3000).OwningProcess | Stop-Process -Force
```

### 问题5: 依赖安装失败

#### ✅ **解决方案**
```bash
# Go 依赖
cd services/auth-service
go mod download
go mod tidy

# Node.js 依赖
cd web-apps/main-portal
npm install

# Python 依赖
cd services/ai-services/newsletter-ai
pip install -r requirements.txt
```

### 问题6: PostgreSQL 启动超时

#### 🎯 **原因分析**
- PostgreSQL 初始化需要时间（2-5分钟）
- Docker 容器启动缓慢
- 数据库文件损坏或权限问题
- 内存不足

#### ✅ **解决方案**

##### 方案A: 等待更长时间
```bash
# 运行 PostgreSQL 诊断
./check-postgres.sh

# 或手动等待
echo "等待 PostgreSQL 启动..."
while ! pg_isready -h localhost -p 5432 -U biocloude; do
    echo "仍在等待..."
    sleep 5
done
echo "PostgreSQL 已准备就绪！"
```

##### 方案B: 重启 PostgreSQL 服务
```bash
# 检查 Docker 容器
docker ps -a | grep postgres

# 重启 PostgreSQL 容器
docker restart <postgres-container-name>

# 或重启所有服务
.devcontainer/start-services.sh
```

##### 方案C: 检查日志
```bash
# 查看 PostgreSQL 日志
docker logs <postgres-container-name>

# 查看最近的日志
docker logs --tail 50 <postgres-container-name>
```

### 问题7: 数据库连接失败

#### 🔍 **检查数据库状态**
```bash
# 使用诊断脚本
./check-postgres.sh

# 手动检查
pg_isready -h localhost -p 5432 -U biocloude
redis-cli -h localhost -p 6379 ping

# 测试连接
PGPASSWORD=biocloude123 psql -h localhost -p 5432 -U biocloude -d biocloude -c "SELECT version();"
```

#### ✅ **解决方案**
```bash
# 在 Codespaces 中，基础服务应该自动启动
# 如果没有启动，运行：
.devcontainer/start-services.sh

# 检查 Docker 容器状态
docker ps
docker-compose ps

# 如果容器未运行，启动它们
docker-compose up -d postgres redis
```

### 问题7: 前端应用无法访问

#### 🔍 **检查步骤**
```bash
# 检查服务状态
./status-dev.sh

# 检查端口
curl http://localhost:3000
curl http://localhost:3001
curl http://localhost:3002
```

#### ✅ **解决方案**
```bash
# 重启前端服务
cd web-apps/main-portal
npm run dev

# 或重启所有服务
./stop-dev.sh
./start-dev.sh
```

### 问题8: API 服务无响应

#### 🔍 **检查 API 健康状态**
```bash
curl http://localhost:8001/health
curl http://localhost:8002/health
curl http://localhost:9001/health
```

#### ✅ **解决方案**
```bash
# 检查服务日志
# 如果使用 Docker
docker logs <container-name>

# 重启 API 服务
cd services/auth-service
go run main.go
```

## 🛠️ 调试工具

### 检查脚本
```bash
# 检查所有服务状态
./status-dev.sh

# 运行测试
./test-dev.sh

# 检查脚本权限
ls -la *.sh
```

### 日志查看
```bash
# 查看系统日志
journalctl -f

# 查看 Docker 日志
docker logs -f <container-name>

# 查看应用日志
tail -f logs/*.log
```

### 网络诊断
```bash
# 检查端口监听
netstat -tulpn | grep LISTEN

# 检查网络连接
curl -v http://localhost:3000

# DNS 解析
nslookup localhost
```

## 🚀 快速恢复

### 完全重置
```bash
# 1. 停止所有服务
./stop-dev.sh

# 2. 清理进程
pkill -f "node"
pkill -f "go run"
pkill -f "python"

# 3. 重新启动
./start-dev.sh
```

### 重新初始化环境
```bash
# 在 Codespaces 中
.devcontainer/setup.sh

# 修复权限
bash fix-permissions.sh

# 启动服务
./start-dev.sh
```

## 📞 获取帮助

### 自助诊断
1. 🔍 **运行状态检查**: `./status-dev.sh`
2. 🧪 **运行测试**: `./test-dev.sh`
3. 📋 **查看日志**: 检查相关服务日志
4. 🔄 **重启服务**: `./stop-dev.sh && ./start-dev.sh`

### 寻求帮助
1. 📖 **查看文档**: README.md, SCRIPTS_GUIDE.md
2. 🐛 **创建 Issue**: 提供错误信息和环境详情
3. 💬 **团队讨论**: 在团队群组中询问
4. 🌐 **尝试 Codespaces**: 避免本地环境问题

## 💡 预防措施

### 最佳实践
- ✅ **使用 Codespaces** - 避免本地环境问题
- ✅ **定期检查状态** - 及时发现问题
- ✅ **正确停止服务** - 使用停止脚本
- ✅ **保持依赖更新** - 定期更新包

### 避免问题
- ❌ **不要手动杀死进程** - 可能导致端口占用
- ❌ **不要忽略错误信息** - 及时处理警告
- ❌ **不要混用不同环境** - 保持一致性

---

🎯 **记住：大多数问题都可以通过重启服务解决！** 

如果问题持续存在，请提供详细的错误信息和环境信息以获得更好的帮助。
