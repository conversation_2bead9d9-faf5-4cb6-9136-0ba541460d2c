name: BioCloude Platform CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: biocloude

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth-service, subscription-service, newsletter-ai, scholar-ai, primer-ai, protein-ai, gene-editing-ai, metabolic-ai]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go (for Go services)
      if: contains(matrix.service, 'service')
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'
    
    - name: Set up Python (for AI services)
      if: contains(matrix.service, 'ai')
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install Go dependencies
      if: contains(matrix.service, 'service')
      working-directory: ./services/${{ matrix.service }}
      run: go mod download
    
    - name: Install Python dependencies
      if: contains(matrix.service, 'ai')
      working-directory: ./services/ai-services/${{ matrix.service }}
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run Go tests
      if: contains(matrix.service, 'service')
      working-directory: ./services/${{ matrix.service }}
      run: go test -v ./...
    
    - name: Run Python tests
      if: contains(matrix.service, 'ai')
      working-directory: ./services/ai-services/${{ matrix.service }}
      run: python -m pytest tests/ -v

  frontend-test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [main-portal, newsletter-app, scholar-app]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: ./web-apps/${{ matrix.app }}/package-lock.json
    
    - name: Install dependencies
      working-directory: ./web-apps/${{ matrix.app }}
      run: npm ci
    
    - name: Run linting
      working-directory: ./web-apps/${{ matrix.app }}
      run: npm run lint
    
    - name: Run type checking
      working-directory: ./web-apps/${{ matrix.app }}
      run: npm run type-check
    
    - name: Build application
      working-directory: ./web-apps/${{ matrix.app }}
      run: npm run build

  build-and-push:
    needs: [test, frontend-test]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    strategy:
      matrix:
        include:
          - service: auth-service
            context: ./services/auth-service
          - service: subscription-service
            context: ./services/subscription-service
          - service: newsletter-ai
            context: ./services/ai-services/newsletter-ai
          - service: scholar-ai
            context: ./services/ai-services/scholar-ai
          - service: primer-ai
            context: ./services/ai-services/primer-ai
          - service: protein-ai
            context: ./services/ai-services/protein-ai
          - service: gene-editing-ai
            context: ./services/ai-services/gene-editing-ai
          - service: metabolic-ai
            context: ./services/ai-services/metabolic-ai
          - service: main-portal
            context: ./web-apps/main-portal
          - service: newsletter-app
            context: ./web-apps/newsletter-app
          - service: scholar-app
            context: ./web-apps/scholar-app
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ${{ matrix.context }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
    
    - name: Configure kubectl
      run: |
        mkdir -p $HOME/.kube
        echo "${{ secrets.KUBE_CONFIG }}" | base64 -d > $HOME/.kube/config
    
    - name: Deploy to Kubernetes
      run: |
        # Update image tags in deployment files
        sed -i "s|biocloude/auth-service:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/auth-service:${{ github.sha }}|g" k8s/deployments/auth-service-deployment.yaml
        sed -i "s|biocloude/subscription-service:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/subscription-service:${{ github.sha }}|g" k8s/deployments/subscription-service-deployment.yaml
        sed -i "s|biocloude/newsletter-ai:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/newsletter-ai:${{ github.sha }}|g" k8s/deployments/newsletter-ai-deployment.yaml
        sed -i "s|biocloude/main-portal:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/main-portal:${{ github.sha }}|g" k8s/deployments/main-portal-deployment.yaml
        sed -i "s|biocloude/newsletter-app:latest|${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/newsletter-app:${{ github.sha }}|g" k8s/deployments/newsletter-app-deployment.yaml
        
        # Apply infrastructure
        kubectl apply -f k8s/infrastructure/
        
        # Apply deployments
        kubectl apply -f k8s/deployments/
        
        # Wait for rollout
        kubectl rollout status deployment/auth-service -n biocloude
        kubectl rollout status deployment/subscription-service -n biocloude
        kubectl rollout status deployment/newsletter-ai -n biocloude
        kubectl rollout status deployment/main-portal -n biocloude
        kubectl rollout status deployment/newsletter-app -n biocloude
    
    - name: Run integration tests
      run: |
        # Wait for services to be ready
        sleep 60
        
        # Run integration tests
        chmod +x scripts/test.sh
        ./scripts/test.sh --base-url https://api.biocloude.cn
    
    - name: Notify deployment status
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  performance-test:
    needs: deploy
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y apache2-utils curl
    
    - name: Run performance tests
      run: |
        chmod +x scripts/performance_test.sh
        ./scripts/performance_test.sh --base-url https://api.biocloude.cn --concurrent 50 --duration 300
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance_report_*.html
