apiVersion: apps/v1
kind: Deployment
metadata:
  name: main-portal
  namespace: biocloude
  labels:
    app: main-portal
    component: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: main-portal
  template:
    metadata:
      labels:
        app: main-portal
        component: frontend
    spec:
      containers:
      - name: main-portal
        image: biocloude/main-portal:latest
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: NEXT_PUBLIC_API_URL
          value: "https://api.biocloude.cn"
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: main-portal-service
  namespace: biocloude
  labels:
    app: main-portal
spec:
  selector:
    app: main-portal
  ports:
  - name: http
    port: 3000
    targetPort: 3000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: main-portal-ingress
  namespace: biocloude
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - www.biocloude.cn
    - biocloude.cn
    secretName: main-portal-tls
  rules:
  - host: www.biocloude.cn
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: main-portal-service
            port:
              number: 3000
  - host: biocloude.cn
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: main-portal-service
            port:
              number: 3000
