#!/bin/bash

# 生科云码平台开发环境启动脚本
# 启动所有前端和后端应用服务

set -e

echo "🚀 启动生科云码平台开发环境..."

# 检测项目根目录
# 优先检查 biocloude 目录
if [ -f "/workspace/biocloude/docker-compose.yml" ] || [ -d "/workspace/biocloude/.devcontainer" ]; then
    PROJECT_ROOT="/workspace/biocloude"
    cd "$PROJECT_ROOT"
elif [ -f "docker-compose.yml" ] || [ -d ".devcontainer" ]; then
    PROJECT_ROOT=$(pwd)
else
    echo "❌ 无法找到项目根目录"
    echo "请确保在 /workspace/biocloude 目录中运行脚本"
    exit 1
fi

echo "📍 项目根目录: $PROJECT_ROOT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查基础服务是否运行
check_base_services() {
    log_info "检查基础服务状态..."

    # 检查PostgreSQL
    local pg_ready=false

    # 首先尝试使用 Docker 直接检查（如果可用）
    if command -v docker &> /dev/null; then
        # 尝试不使用 sudo 的 docker 命令
        if docker exec biocloude_devcontainer-postgres-1 pg_isready -U biocloude &> /dev/null; then
            pg_ready=true
        # 如果失败，尝试使用 sudo
        elif sudo docker exec biocloude_devcontainer-postgres-1 pg_isready -U biocloude &> /dev/null; then
            pg_ready=true
        fi
    fi

    # 如果 Docker 检查失败，回退到网络检查
    if [ "$pg_ready" = false ]; then
        if nc -z localhost 5432 2>/dev/null; then
            # 端口开放，进一步检查是否准备就绪
            if command -v pg_isready &> /dev/null; then
                if pg_isready -h localhost -p 5432 -U biocloude &> /dev/null; then
                    pg_ready=true
                fi
            else
                # 没有 pg_isready，假设端口开放就是准备好了
                pg_ready=true
            fi
        fi
    fi

    if [ "$pg_ready" = false ]; then
        log_error "PostgreSQL 未运行或未准备就绪"
        log_info "在 Codespaces 中，基础服务应该自动启动"
        log_info "如果没有启动，请运行: .devcontainer/start-services.sh"
        log_info "或等待 PostgreSQL 完成初始化（可能需要几分钟）"

        # 尝试等待一段时间
        log_info "等待 PostgreSQL 启动..."
        local attempts=0
        while [ $attempts -lt 30 ] && [ "$pg_ready" = false ]; do
            sleep 2
            attempts=$((attempts + 1))

            if nc -z localhost 5432 2>/dev/null; then
                if command -v pg_isready &> /dev/null; then
                    if pg_isready -h localhost -p 5432 -U biocloude &> /dev/null; then
                        pg_ready=true
                        break
                    fi
                else
                    pg_ready=true
                    break
                fi
            fi

            if [ $((attempts % 5)) -eq 0 ]; then
                log_info "仍在等待 PostgreSQL... ($attempts/30)"
            fi
        done

        if [ "$pg_ready" = false ]; then
            log_error "PostgreSQL 启动超时，请检查服务状态"
            exit 1
        fi
    fi
    log_success "PostgreSQL 运行正常"

    # 检查Redis
    if ! nc -z localhost 6379 2>/dev/null; then
        log_warning "Redis 未运行，某些功能可能受限"
    else
        log_success "Redis 运行正常"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装依赖..."
    
    # 检查Go依赖
    if [ -d "$PROJECT_ROOT/biocloude/services/auth-service" ]; then
        log_info "安装认证服务依赖..."
        cd "$PROJECT_ROOT/biocloude/services/auth-service"
        if [ ! -d "vendor" ] && [ -f "go.mod" ]; then
            go mod download
            go mod tidy
        fi
        cd "$PROJECT_ROOT"
    fi

    if [ -d "$PROJECT_ROOT/biocloude/services/subscription-service" ]; then
        log_info "安装订阅服务依赖..."
        cd "$PROJECT_ROOT/biocloude/services/subscription-service"
        if [ ! -d "vendor" ] && [ -f "go.mod" ]; then
            go mod download
            go mod tidy
        fi
        cd "$PROJECT_ROOT"
    fi
    
    # 检查Python依赖
    ai_services=("newsletter-ai" "scholar-ai" "primer-ai" "protein-ai" "gene-editing-ai" "metabolic-ai")
    for service in "${ai_services[@]}"; do
        if [ -d "$PROJECT_ROOT/biocloude/services/ai-services/$service" ] && [ -f "$PROJECT_ROOT/services/ai-services/$service/requirements.txt" ]; then
            log_info "检查 $service Python依赖..."
            cd "$PROJECT_ROOT/biocloude/services/ai-services/$service"
            # 只在需要时安装
            if ! python -c "import fastapi, uvicorn" 2>/dev/null; then
                log_info "安装 $service 依赖..."
                pip install -r requirements.txt
            fi
            cd "$PROJECT_ROOT"
        fi
    done
    
    # 检查前端依赖
    frontend_apps=("main-portal" "newsletter-app" "scholar-app")
    for app in "${frontend_apps[@]}"; do
        if [ -d "$PROJECT_ROOT/biocloude/web-apps/$app" ] && [ -f "$PROJECT_ROOT/web-apps/$app/package.json" ]; then
            log_info "检查 $app 前端依赖..."
            cd "$PROJECT_ROOT/biocloude/web-apps/$app"
            if [ ! -d "node_modules" ]; then
                log_info "安装 $app 依赖..."
                npm install
            fi
            cd "$PROJECT_ROOT"
        fi
    done
}

# 启动后端服务
start_backend_services() {
    log_info "启动后端服务..."
    
    # 启动认证服务
    if [ -d "$PROJECT_ROOT/biocloude/services/auth-service" ]; then
        log_info "启动认证服务 (端口 8001)..."
        cd "$PROJECT_ROOT/biocloude/services/auth-service"
        go run main.go &
        AUTH_PID=$!
        echo $AUTH_PID > "$PROJECT_ROOT/.pids/auth-service.pid"
        cd "$PROJECT_ROOT"
        log_success "认证服务已启动 (PID: $AUTH_PID)"
    fi

    # 启动订阅服务
    if [ -d "$PROJECT_ROOT/biocloude/services/subscription-service" ]; then
        log_info "启动订阅服务 (端口 8002)..."
        cd "$PROJECT_ROOT/biocloude/services/subscription-service"
        go run main.go &
        SUB_PID=$!
        echo $SUB_PID > "$PROJECT_ROOT/.pids/subscription-service.pid"
        cd "$PROJECT_ROOT"
        log_success "订阅服务已启动 (PID: $SUB_PID)"
    fi
    
    # 等待Go服务启动
    sleep 3
}

# 启动AI服务
start_ai_services() {
    log_info "启动AI服务..."
    
    ai_services=("newsletter-ai:9001" "scholar-ai:9002" "primer-ai:9003" "protein-ai:9004" "gene-editing-ai:9005" "metabolic-ai:9006")
    
    for service_port in "${ai_services[@]}"; do
        IFS=':' read -r service port <<< "$service_port"

        if [ -d "$PROJECT_ROOT/biocloude/services/ai-services/$service" ]; then
            log_info "启动 $service (端口 $port)..."
            cd "$PROJECT_ROOT/biocloude/services/ai-services/$service"
            python main.py &
            AI_PID=$!
            echo $AI_PID > "$PROJECT_ROOT/.pids/$service.pid"
            cd "$PROJECT_ROOT"
            log_success "$service 已启动 (PID: $AI_PID)"
        fi
    done
    
    # 等待AI服务启动
    sleep 5
}

# 启动前端应用
start_frontend_apps() {
    log_info "启动前端应用..."
    
    # 启动主站门户
    if [ -d "$PROJECT_ROOT/biocloude/web-apps/main-portal" ]; then
        log_info "启动主站门户 (端口 3000)..."
        cd "$PROJECT_ROOT/biocloude/web-apps/main-portal"
        npm run dev &
        PORTAL_PID=$!
        echo $PORTAL_PID > "$PROJECT_ROOT/.pids/main-portal.pid"
        cd "$PROJECT_ROOT"
        log_success "主站门户已启动 (PID: $PORTAL_PID)"
    fi

    # 启动资讯处理AI应用
    if [ -d "$PROJECT_ROOT/biocloude/web-apps/newsletter-app" ]; then
        log_info "启动资讯处理AI应用 (端口 3001)..."
        cd "$PROJECT_ROOT/web-apps/newsletter-app"
        npm run dev &
        NEWS_PID=$!
        echo $NEWS_PID > "$PROJECT_ROOT/.pids/newsletter-app.pid"
        cd "$PROJECT_ROOT"
        log_success "资讯处理AI应用已启动 (PID: $NEWS_PID)"
    fi

    # 启动文献阅读AI应用
    if [ -d "$PROJECT_ROOT/biocloude/web-apps/scholar-app" ]; then
        log_info "启动文献阅读AI应用 (端口 3002)..."
        cd "$PROJECT_ROOT/biocloude/web-apps/scholar-app"
        npm run dev &
        SCHOLAR_PID=$!
        echo $SCHOLAR_PID > "$PROJECT_ROOT/.pids/scholar-app.pid"
        cd "$PROJECT_ROOT"
        log_success "文献阅读AI应用已启动 (PID: $SCHOLAR_PID)"
    fi
    
    # 等待前端应用启动
    sleep 10
}

# 检查服务状态
check_services_status() {
    log_info "检查服务状态..."
    
    # 检查后端API
    services=("localhost:8001" "localhost:8002" "localhost:9001" "localhost:9002")
    for service in "${services[@]}"; do
        if curl -f -s "http://$service/health" > /dev/null 2>&1; then
            log_success "✅ $service 运行正常"
        else
            log_warning "⚠️ $service 可能未完全启动"
        fi
    done
    
    # 检查前端应用
    frontend_services=("localhost:3000" "localhost:3001" "localhost:3002")
    for service in "${frontend_services[@]}"; do
        if curl -f -s "http://$service" > /dev/null 2>&1; then
            log_success "✅ $service 运行正常"
        else
            log_warning "⚠️ $service 可能未完全启动"
        fi
    done
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "🎉 生科云码平台开发环境启动完成！"
    echo ""
    echo "📱 前端应用:"
    echo "  🏠 主站门户:     http://localhost:3000"
    echo "  📰 资讯处理AI:   http://localhost:3001"
    echo "  📚 文献阅读AI:   http://localhost:3002"
    echo ""
    echo "🔧 API服务:"
    echo "  🔐 认证服务:     http://localhost:8001"
    echo "  💳 订阅服务:     http://localhost:8002"
    echo "  🤖 资讯AI:       http://localhost:9001"
    echo "  📖 文献AI:       http://localhost:9002"
    echo "  🧬 引物AI:       http://localhost:9003"
    echo "  🔬 蛋白质AI:     http://localhost:9004"
    echo "  ✂️ 基因编辑AI:   http://localhost:9005"
    echo "  ⚗️ 代谢工程AI:   http://localhost:9006"
    echo ""
    echo "🛠️ 开发工具:"
    echo "  📧 MailHog:      http://localhost:8025"
    echo "  💾 MinIO:        http://localhost:9090"
    echo "  📊 Prometheus:   http://localhost:9091"
    echo "  📈 Grafana:      http://localhost:3030"
    echo ""
    echo "⚡ 快捷命令:"
    echo "  停止所有服务:    ./stop-dev.sh"
    echo "  查看服务状态:    ./status-dev.sh"
    echo "  运行测试:        ./test-dev.sh"
    echo ""
    echo "💡 提示: 前端应用支持热重载，修改代码后会自动刷新"
}

# 创建PID目录
create_pid_directory() {
    if [ ! -d "$PROJECT_ROOT/.pids" ]; then
        mkdir -p "$PROJECT_ROOT/.pids"
    fi
}

# 主函数
main() {
    log_info "🧬 生科云码平台开发环境启动中..."
    
    create_pid_directory
    check_base_services
    install_dependencies
    start_backend_services
    start_ai_services
    start_frontend_apps
    
    log_info "等待所有服务完全启动..."
    sleep 15
    
    check_services_status
    show_access_info
    
    log_info "按 Ctrl+C 停止所有服务"
    
    # 等待用户中断
    trap 'echo ""; log_info "正在停止所有服务..."; ./stop-dev.sh; exit 0' INT
    
    # 保持脚本运行
    while true; do
        sleep 1
    done
}

# 执行主函数
main "$@"
