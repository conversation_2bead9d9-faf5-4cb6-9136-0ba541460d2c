#!/bin/bash

# 生科云码平台开发环境启动脚本
# 启动所有前端和后端应用服务

set -e

echo "🚀 启动生科云码平台开发环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查基础服务是否运行
check_base_services() {
    log_info "检查基础服务状态..."
    
    # 检查PostgreSQL
    if ! nc -z localhost 5432 2>/dev/null; then
        log_error "PostgreSQL 未运行，请先启动基础服务"
        log_info "在 Codespaces 中，基础服务应该自动启动"
        log_info "如果没有启动，请运行: .devcontainer/start-services.sh"
        exit 1
    fi
    log_success "PostgreSQL 运行正常"
    
    # 检查Redis
    if ! nc -z localhost 6379 2>/dev/null; then
        log_warning "Redis 未运行，某些功能可能受限"
    else
        log_success "Redis 运行正常"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装依赖..."
    
    # 检查Go依赖
    if [ -d "services/auth-service" ]; then
        log_info "安装认证服务依赖..."
        cd services/auth-service
        if [ ! -d "vendor" ] && [ -f "go.mod" ]; then
            go mod download
            go mod tidy
        fi
        cd ../..
    fi
    
    if [ -d "services/subscription-service" ]; then
        log_info "安装订阅服务依赖..."
        cd services/subscription-service
        if [ ! -d "vendor" ] && [ -f "go.mod" ]; then
            go mod download
            go mod tidy
        fi
        cd ../..
    fi
    
    # 检查Python依赖
    ai_services=("newsletter-ai" "scholar-ai" "primer-ai" "protein-ai" "gene-editing-ai" "metabolic-ai")
    for service in "${ai_services[@]}"; do
        if [ -d "services/ai-services/$service" ] && [ -f "services/ai-services/$service/requirements.txt" ]; then
            log_info "检查 $service Python依赖..."
            cd "services/ai-services/$service"
            # 只在需要时安装
            if ! python -c "import fastapi, uvicorn" 2>/dev/null; then
                log_info "安装 $service 依赖..."
                pip install -r requirements.txt
            fi
            cd ../../..
        fi
    done
    
    # 检查前端依赖
    frontend_apps=("main-portal" "newsletter-app" "scholar-app")
    for app in "${frontend_apps[@]}"; do
        if [ -d "web-apps/$app" ] && [ -f "web-apps/$app/package.json" ]; then
            log_info "检查 $app 前端依赖..."
            cd "web-apps/$app"
            if [ ! -d "node_modules" ]; then
                log_info "安装 $app 依赖..."
                npm install
            fi
            cd ../..
        fi
    done
}

# 启动后端服务
start_backend_services() {
    log_info "启动后端服务..."
    
    # 启动认证服务
    if [ -d "services/auth-service" ]; then
        log_info "启动认证服务 (端口 8001)..."
        cd services/auth-service
        go run main.go &
        AUTH_PID=$!
        echo $AUTH_PID > ../../.pids/auth-service.pid
        cd ../..
        log_success "认证服务已启动 (PID: $AUTH_PID)"
    fi
    
    # 启动订阅服务
    if [ -d "services/subscription-service" ]; then
        log_info "启动订阅服务 (端口 8002)..."
        cd services/subscription-service
        go run main.go &
        SUB_PID=$!
        echo $SUB_PID > ../../.pids/subscription-service.pid
        cd ../..
        log_success "订阅服务已启动 (PID: $SUB_PID)"
    fi
    
    # 等待Go服务启动
    sleep 3
}

# 启动AI服务
start_ai_services() {
    log_info "启动AI服务..."
    
    ai_services=("newsletter-ai:9001" "scholar-ai:9002" "primer-ai:9003" "protein-ai:9004" "gene-editing-ai:9005" "metabolic-ai:9006")
    
    for service_port in "${ai_services[@]}"; do
        IFS=':' read -r service port <<< "$service_port"
        
        if [ -d "services/ai-services/$service" ]; then
            log_info "启动 $service (端口 $port)..."
            cd "services/ai-services/$service"
            python main.py &
            AI_PID=$!
            echo $AI_PID > "../../../.pids/$service.pid"
            cd ../../..
            log_success "$service 已启动 (PID: $AI_PID)"
        fi
    done
    
    # 等待AI服务启动
    sleep 5
}

# 启动前端应用
start_frontend_apps() {
    log_info "启动前端应用..."
    
    # 启动主站门户
    if [ -d "web-apps/main-portal" ]; then
        log_info "启动主站门户 (端口 3000)..."
        cd web-apps/main-portal
        npm run dev &
        PORTAL_PID=$!
        echo $PORTAL_PID > ../../.pids/main-portal.pid
        cd ../..
        log_success "主站门户已启动 (PID: $PORTAL_PID)"
    fi
    
    # 启动资讯处理AI应用
    if [ -d "web-apps/newsletter-app" ]; then
        log_info "启动资讯处理AI应用 (端口 3001)..."
        cd web-apps/newsletter-app
        npm run dev &
        NEWS_PID=$!
        echo $NEWS_PID > ../../.pids/newsletter-app.pid
        cd ../..
        log_success "资讯处理AI应用已启动 (PID: $NEWS_PID)"
    fi
    
    # 启动文献阅读AI应用
    if [ -d "web-apps/scholar-app" ]; then
        log_info "启动文献阅读AI应用 (端口 3002)..."
        cd web-apps/scholar-app
        npm run dev &
        SCHOLAR_PID=$!
        echo $SCHOLAR_PID > ../../.pids/scholar-app.pid
        cd ../..
        log_success "文献阅读AI应用已启动 (PID: $SCHOLAR_PID)"
    fi
    
    # 等待前端应用启动
    sleep 10
}

# 检查服务状态
check_services_status() {
    log_info "检查服务状态..."
    
    # 检查后端API
    services=("localhost:8001" "localhost:8002" "localhost:9001" "localhost:9002")
    for service in "${services[@]}"; do
        if curl -f -s "http://$service/health" > /dev/null 2>&1; then
            log_success "✅ $service 运行正常"
        else
            log_warning "⚠️ $service 可能未完全启动"
        fi
    done
    
    # 检查前端应用
    frontend_services=("localhost:3000" "localhost:3001" "localhost:3002")
    for service in "${frontend_services[@]}"; do
        if curl -f -s "http://$service" > /dev/null 2>&1; then
            log_success "✅ $service 运行正常"
        else
            log_warning "⚠️ $service 可能未完全启动"
        fi
    done
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "🎉 生科云码平台开发环境启动完成！"
    echo ""
    echo "📱 前端应用:"
    echo "  🏠 主站门户:     http://localhost:3000"
    echo "  📰 资讯处理AI:   http://localhost:3001"
    echo "  📚 文献阅读AI:   http://localhost:3002"
    echo ""
    echo "🔧 API服务:"
    echo "  🔐 认证服务:     http://localhost:8001"
    echo "  💳 订阅服务:     http://localhost:8002"
    echo "  🤖 资讯AI:       http://localhost:9001"
    echo "  📖 文献AI:       http://localhost:9002"
    echo "  🧬 引物AI:       http://localhost:9003"
    echo "  🔬 蛋白质AI:     http://localhost:9004"
    echo "  ✂️ 基因编辑AI:   http://localhost:9005"
    echo "  ⚗️ 代谢工程AI:   http://localhost:9006"
    echo ""
    echo "🛠️ 开发工具:"
    echo "  📧 MailHog:      http://localhost:8025"
    echo "  💾 MinIO:        http://localhost:9090"
    echo "  📊 Prometheus:   http://localhost:9091"
    echo "  📈 Grafana:      http://localhost:3030"
    echo ""
    echo "⚡ 快捷命令:"
    echo "  停止所有服务:    ./stop-dev.sh"
    echo "  查看服务状态:    ./status-dev.sh"
    echo "  运行测试:        ./test-dev.sh"
    echo ""
    echo "💡 提示: 前端应用支持热重载，修改代码后会自动刷新"
}

# 创建PID目录
create_pid_directory() {
    if [ ! -d ".pids" ]; then
        mkdir -p .pids
    fi
}

# 主函数
main() {
    log_info "🧬 生科云码平台开发环境启动中..."
    
    create_pid_directory
    check_base_services
    install_dependencies
    start_backend_services
    start_ai_services
    start_frontend_apps
    
    log_info "等待所有服务完全启动..."
    sleep 15
    
    check_services_status
    show_access_info
    
    log_info "按 Ctrl+C 停止所有服务"
    
    # 等待用户中断
    trap 'echo ""; log_info "正在停止所有服务..."; ./stop-dev.sh; exit 0' INT
    
    # 保持脚本运行
    while true; do
        sleep 1
    done
}

# 执行主函数
main "$@"
