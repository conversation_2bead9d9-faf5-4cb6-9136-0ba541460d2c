#!/bin/bash

# 生科云码平台开发环境停止脚本
# 停止所有前端和后端应用服务

set -e

echo "🛑 停止生科云码平台开发环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止单个服务
stop_service() {
    local service_name="$1"
    local pid_file=".pids/$service_name.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "停止 $service_name (PID: $pid)..."
            kill "$pid" 2>/dev/null || true
            
            # 等待进程结束
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制杀死
            if kill -0 "$pid" 2>/dev/null; then
                log_warning "强制停止 $service_name..."
                kill -9 "$pid" 2>/dev/null || true
            fi
            
            log_success "$service_name 已停止"
        else
            log_warning "$service_name 进程不存在 (PID: $pid)"
        fi
        
        # 删除PID文件
        rm -f "$pid_file"
    else
        log_warning "$service_name PID文件不存在"
    fi
}

# 按端口停止进程
stop_by_port() {
    local port="$1"
    local service_name="$2"
    
    log_info "检查端口 $port 上的进程..."
    
    # 查找占用端口的进程
    local pid=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$pid" ]; then
        log_info "停止端口 $port 上的 $service_name (PID: $pid)..."
        kill "$pid" 2>/dev/null || true
        
        # 等待进程结束
        sleep 2
        
        # 检查是否还在运行
        if kill -0 "$pid" 2>/dev/null; then
            log_warning "强制停止端口 $port 上的进程..."
            kill -9 "$pid" 2>/dev/null || true
        fi
        
        log_success "端口 $port 已释放"
    else
        log_info "端口 $port 没有运行的进程"
    fi
}

# 停止所有服务
stop_all_services() {
    log_info "停止所有应用服务..."
    
    # 停止前端应用
    log_info "停止前端应用..."
    stop_service "main-portal"
    stop_service "newsletter-app"
    stop_service "scholar-app"
    
    # 停止AI服务
    log_info "停止AI服务..."
    stop_service "newsletter-ai"
    stop_service "scholar-ai"
    stop_service "primer-ai"
    stop_service "protein-ai"
    stop_service "gene-editing-ai"
    stop_service "metabolic-ai"
    
    # 停止后端服务
    log_info "停止后端服务..."
    stop_service "auth-service"
    stop_service "subscription-service"
    
    # 按端口停止可能遗漏的进程
    log_info "检查并停止端口上的进程..."
    stop_by_port "3000" "主站门户"
    stop_by_port "3001" "资讯处理AI"
    stop_by_port "3002" "文献阅读AI"
    stop_by_port "8001" "认证服务"
    stop_by_port "8002" "订阅服务"
    stop_by_port "9001" "资讯AI"
    stop_by_port "9002" "文献AI"
    stop_by_port "9003" "引物AI"
    stop_by_port "9004" "蛋白质AI"
    stop_by_port "9005" "基因编辑AI"
    stop_by_port "9006" "代谢工程AI"
}

# 清理PID目录
cleanup_pids() {
    log_info "清理PID文件..."
    
    if [ -d ".pids" ]; then
        rm -f .pids/*.pid
        log_success "PID文件已清理"
    fi
}

# 显示停止后的状态
show_status() {
    echo ""
    echo "🔍 检查端口状态..."
    
    ports=("3000" "3001" "3002" "8001" "8002" "9001" "9002" "9003" "9004" "9005" "9006")
    
    for port in "${ports[@]}"; do
        if lsof -ti:$port >/dev/null 2>&1; then
            log_warning "⚠️ 端口 $port 仍有进程运行"
        else
            log_success "✅ 端口 $port 已释放"
        fi
    done
}

# 主函数
main() {
    log_info "🛑 开始停止生科云码平台服务..."
    
    stop_all_services
    cleanup_pids
    show_status
    
    echo ""
    log_success "🎉 所有服务已停止！"
    echo ""
    echo "💡 提示:"
    echo "  重新启动服务: ./start-dev.sh"
    echo "  查看服务状态: ./status-dev.sh"
    echo ""
}

# 检查是否安装了必要工具
check_tools() {
    if ! command -v lsof &> /dev/null; then
        log_warning "lsof 未安装，将跳过端口检查"
        log_info "安装 lsof: sudo apt-get install lsof (Ubuntu/Debian)"
    fi
}

# 执行主函数
check_tools
main "$@"
