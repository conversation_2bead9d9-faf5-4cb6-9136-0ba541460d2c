# 生科云码平台开发容器
FROM mcr.microsoft.com/devcontainers/base:ubuntu-22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 安装基础工具
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    tree \
    jq \
    unzip \
    zip \
    build-essential \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# 安装Node.js 18
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# 安装Go 1.21
RUN wget https://go.dev/dl/go1.21.5.linux-amd64.tar.gz \
    && tar -C /usr/local -xzf go1.21.5.linux-amd64.tar.gz \
    && rm go1.21.5.linux-amd64.tar.gz

# 安装Python 3.11
RUN add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update \
    && apt-get install -y \
        python3.11 \
        python3.11-dev \
        python3.11-venv \
        python3-pip \
        python3.11-distutils \
    && update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1 \
    && update-alternatives --install /usr/bin/python python /usr/bin/python3.11 1

# 安装pip和常用Python包
RUN python3 -m pip install --upgrade pip setuptools wheel \
    && pip install \
        fastapi \
        uvicorn \
        asyncpg \
        redis \
        aiohttp \
        pydantic \
        loguru \
        pytest \
        pytest-asyncio \
        black \
        flake8 \
        isort \
        mypy

# 安装Docker CLI
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce-cli

# 安装Docker Compose
RUN curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose \
    && chmod +x /usr/local/bin/docker-compose

# 安装kubectl
RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" \
    && install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# 安装Helm
RUN curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | tee /usr/share/keyrings/helm.gpg > /dev/null \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | tee /etc/apt/sources.list.d/helm-stable-debian.list \
    && apt-get update \
    && apt-get install -y helm

# 安装k9s (Kubernetes CLI管理工具)
RUN wget https://github.com/derailed/k9s/releases/latest/download/k9s_Linux_amd64.tar.gz \
    && tar -xzf k9s_Linux_amd64.tar.gz \
    && mv k9s /usr/local/bin/ \
    && rm k9s_Linux_amd64.tar.gz

# 安装PostgreSQL客户端
RUN apt-get update && apt-get install -y postgresql-client

# 安装Redis客户端
RUN apt-get install -y redis-tools

# 安装Apache Bench (性能测试)
RUN apt-get install -y apache2-utils

# 安装wrk (HTTP基准测试工具)
RUN git clone https://github.com/wg/wrk.git /tmp/wrk \
    && cd /tmp/wrk \
    && make \
    && cp wrk /usr/local/bin/ \
    && rm -rf /tmp/wrk

# 设置Go环境变量
ENV PATH="/usr/local/go/bin:${PATH}"
ENV GOPATH="/go"
ENV GOROOT="/usr/local/go"

# 创建Go工作目录
RUN mkdir -p /go/src /go/bin /go/pkg
RUN chmod -R 777 /go

# 安装常用Go工具
RUN go install golang.org/x/tools/gopls@latest \
    && go install github.com/go-delve/delve/cmd/dlv@latest \
    && go install honnef.co/go/tools/cmd/staticcheck@latest \
    && go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 安装全局npm包
RUN npm install -g \
    typescript \
    @types/node \
    eslint \
    prettier \
    @typescript-eslint/parser \
    @typescript-eslint/eslint-plugin \
    next \
    create-next-app \
    vercel \
    pm2

# 创建工作目录
WORKDIR /workspace

# 设置用户权限
RUN chown -R vscode:vscode /workspace /go

# 切换到vscode用户
USER vscode

# 配置Git (如果需要)
RUN git config --global init.defaultBranch main \
    && git config --global core.autocrlf input \
    && git config --global pull.rebase false

# 配置zsh (如果安装了oh-my-zsh)
RUN if [ -d "$HOME/.oh-my-zsh" ]; then \
        echo 'export PATH="/usr/local/go/bin:$GOPATH/bin:$PATH"' >> ~/.zshrc \
        && echo 'export GOPATH="/go"' >> ~/.zshrc \
        && echo 'export GOROOT="/usr/local/go"' >> ~/.zshrc \
        && echo 'alias ll="ls -la"' >> ~/.zshrc \
        && echo 'alias k="kubectl"' >> ~/.zshrc \
        && echo 'alias dc="docker-compose"' >> ~/.zshrc; \
    fi

# 配置bash
RUN echo 'export PATH="/usr/local/go/bin:$GOPATH/bin:$PATH"' >> ~/.bashrc \
    && echo 'export GOPATH="/go"' >> ~/.bashrc \
    && echo 'export GOROOT="/usr/local/go"' >> ~/.bashrc \
    && echo 'alias ll="ls -la"' >> ~/.bashrc \
    && echo 'alias k="kubectl"' >> ~/.bashrc \
    && echo 'alias dc="docker-compose"' >> ~/.bashrc

# 暴露端口
EXPOSE 3000 3001 3002 8001 8002 9001 9002 9003 9004 9005 9006

# 默认命令
CMD ["sleep", "infinity"]
