{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.tsx"], "sourcesContent": ["import { PureComponent } from 'react'\nimport { RuntimeErrorHandler } from '../../errors/runtime-error-handler'\nimport {\n  ErrorBoundary,\n  type GlobalErrorComponent,\n  GlobalError as DefaultGlobalError,\n} from '../../error-boundary'\n\ntype AppDevOverlayErrorBoundaryProps = {\n  children: React.ReactNode\n  globalError: [GlobalErrorComponent, React.ReactNode]\n  onError: (value: boolean) => void\n}\n\ntype AppDevOverlayErrorBoundaryState = {\n  isReactError: boolean\n  reactError: unknown\n}\n\nfunction ErroredHtml({\n  globalError: [GlobalError, globalErrorStyles],\n  error,\n}: {\n  globalError: [GlobalErrorComponent, React.ReactNode]\n  error: unknown\n}) {\n  if (!error) {\n    return (\n      <html>\n        <head />\n        <body />\n      </html>\n    )\n  }\n  return (\n    <ErrorBoundary errorComponent={DefaultGlobalError}>\n      {globalErrorStyles}\n      <GlobalError error={error} />\n    </ErrorBoundary>\n  )\n}\n\nexport class AppDevOverlayErrorBoundary extends PureComponent<\n  AppDevOverlayErrorBoundaryProps,\n  AppDevOverlayErrorBoundaryState\n> {\n  state = { isReactError: false, reactError: null }\n\n  static getDerivedStateFromError(error: Error) {\n    if (!error.stack) {\n      return { isReactError: false, reactError: null }\n    }\n\n    RuntimeErrorHandler.hadRuntimeError = true\n\n    return {\n      isReactError: true,\n      reactError: error,\n    }\n  }\n\n  componentDidCatch() {\n    this.props.onError(this.state.isReactError)\n  }\n\n  render() {\n    const { children, globalError } = this.props\n    const { isReactError, reactError } = this.state\n\n    const fallback = (\n      <ErroredHtml globalError={globalError} error={reactError} />\n    )\n\n    return isReactError ? fallback : children\n  }\n}\n"], "names": ["AppDevOverlayErrorBoundary", "ErroredHtml", "globalError", "GlobalError", "globalErrorStyles", "error", "html", "head", "body", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "DefaultGlobalError", "PureComponent", "getDerivedStateFromError", "stack", "isReactError", "reactError", "RuntimeError<PERSON>andler", "hadRuntimeError", "componentDidCatch", "props", "onError", "state", "render", "children", "fallback"], "mappings": ";;;;+BA0CaA;;;eAAAA;;;;uBA1CiB;qCACM;+BAK7B;AAaP,SAASC,YAAY,KAMpB;IANoB,IAAA,EACnBC,aAAa,CAACC,aAAaC,kBAAkB,EAC7CC,KAAK,EAIN,GANoB;IAOnB,IAAI,CAACA,OAAO;QACV,qBACE,sBAACC;;8BACC,qBAACC;8BACD,qBAACC;;;IAGP;IACA,qBACE,sBAACC,4BAAa;QAACC,gBAAgBC,0BAAkB;;YAC9CP;0BACD,qBAACD;gBAAYE,OAAOA;;;;AAG1B;AAEO,MAAML,mCAAmCY,oBAAa;IAM3D,OAAOC,yBAAyBR,KAAY,EAAE;QAC5C,IAAI,CAACA,MAAMS,KAAK,EAAE;YAChB,OAAO;gBAAEC,cAAc;gBAAOC,YAAY;YAAK;QACjD;QAEAC,wCAAmB,CAACC,eAAe,GAAG;QAEtC,OAAO;YACLH,cAAc;YACdC,YAAYX;QACd;IACF;IAEAc,oBAAoB;QAClB,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,KAAK,CAACP,YAAY;IAC5C;IAEAQ,SAAS;QACP,MAAM,EAAEC,QAAQ,EAAEtB,WAAW,EAAE,GAAG,IAAI,CAACkB,KAAK;QAC5C,MAAM,EAAEL,YAAY,EAAEC,UAAU,EAAE,GAAG,IAAI,CAACM,KAAK;QAE/C,MAAMG,yBACJ,qBAACxB;YAAYC,aAAaA;YAAaG,OAAOW;;QAGhD,OAAOD,eAAeU,WAAWD;IACnC;;QAhCK,qBAILF,QAAQ;YAAEP,cAAc;YAAOC,YAAY;QAAK;;AA6BlD"}