/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: [
      'images.unsplash.com',
      'via.placeholder.com',
      'pubmed.ncbi.nlm.nih.gov',
      'www.nature.com',
      'science.sciencemag.org',
      'www.cell.com',
      'www.nejm.org',
      'www.thelancet.com',
      'jamanetwork.com',
      'www.bmj.com',
      'academic.oup.com',
      'onlinelibrary.wiley.com',
      'link.springer.com',
      'www.frontiersin.org',
      'journals.plos.org',
      'elifesciences.org',
      'www.biorxiv.org',
      'www.medrxiv.org'
    ],
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:9001/api/:path*',
      },
    ]
  },
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9001',
    NEXT_PUBLIC_APP_NAME: 'IVD Newsletter AI',
    NEXT_PUBLIC_APP_DESCRIPTION: '生物医学资讯智能处理平台',
  },
}

module.exports = nextConfig
