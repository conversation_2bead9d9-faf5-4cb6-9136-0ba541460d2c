apiVersion: batch/v1
kind: Job
metadata:
  name: database-migration
  namespace: biocloude
  labels:
    app: database-migration
spec:
  template:
    metadata:
      labels:
        app: database-migration
    spec:
      restartPolicy: OnFailure
      containers:
      - name: migration
        image: migrate/migrate:latest
        command:
        - migrate
        - -path
        - /migrations
        - -database
        - ***********************************************/biocloude?sslmode=disable
        - up
        volumeMounts:
        - name: migrations
          mountPath: /migrations
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: database-url
      volumes:
      - name: migrations
        configMap:
          name: database-migrations

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: database-migrations
  namespace: biocloude
data:
  001_initial_schema.up.sql: |
    -- 创建用户表
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    
    CREATE TABLE users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(100) UNIQUE,
        phone VARCHAR(20) UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        avatar_url TEXT,
        ciam_user_id VARCHAR(255) UNIQUE,
        is_active BOOLEAN DEFAULT true,
        is_verified BOOLEAN DEFAULT false,
        is_locked BOOLEAN DEFAULT false,
        last_login_at TIMESTAMP,
        login_attempts INTEGER DEFAULT 0,
        locked_until TIMESTAMP,
        email_verified_at TIMESTAMP,
        phone_verified_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP
    );
    
    -- 创建产品表
    CREATE TABLE products (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(100) UNIQUE NOT NULL,
        domain VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        base_price DECIMAL(10,2) DEFAULT 0,
        billing_type VARCHAR(50) DEFAULT 'subscription',
        features JSONB,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP
    );
    
    -- 创建订阅表
    CREATE TABLE subscriptions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id),
        product_id UUID NOT NULL REFERENCES products(id),
        pricing_plan_id UUID,
        status VARCHAR(50) DEFAULT 'active',
        price DECIMAL(10,2) NOT NULL,
        billing_cycle VARCHAR(50) DEFAULT 'monthly',
        start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        end_date TIMESTAMP,
        auto_renew BOOLEAN DEFAULT true,
        metadata JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP
    );
    
    -- 创建会话表
    CREATE TABLE user_sessions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id),
        product_id UUID REFERENCES products(id),
        session_token VARCHAR(255) UNIQUE NOT NULL,
        refresh_token VARCHAR(255) UNIQUE,
        ip_address INET,
        user_agent TEXT,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 创建索引
    CREATE INDEX idx_users_email ON users(email);
    CREATE INDEX idx_users_ciam_user_id ON users(ciam_user_id);
    CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
    CREATE INDEX idx_subscriptions_product_id ON subscriptions(product_id);
    CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
    CREATE INDEX idx_sessions_token ON user_sessions(session_token);
    
  002_newsletter_tables.up.sql: |
    -- 创建资讯相关表
    CREATE TABLE articles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        title TEXT NOT NULL,
        summary TEXT,
        content TEXT,
        category VARCHAR(100),
        source VARCHAR(255),
        author VARCHAR(255),
        publish_time TIMESTAMP,
        priority VARCHAR(20) DEFAULT 'medium',
        tags JSONB,
        read_time VARCHAR(50),
        image_url TEXT,
        url TEXT,
        views INTEGER DEFAULT 0,
        likes INTEGER DEFAULT 0,
        ai_category VARCHAR(100),
        ai_confidence DECIMAL(5,4),
        ai_keywords JSONB,
        ai_sentiment JSONB,
        ai_relevance_score DECIMAL(5,3),
        language VARCHAR(10),
        is_duplicate BOOLEAN DEFAULT false,
        processed_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 创建资讯订阅配置表
    CREATE TABLE newsletter_subscriptions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        subscription_id UUID NOT NULL REFERENCES subscriptions(id),
        keywords JSONB,
        categories JSONB,
        sources JSONB,
        frequency VARCHAR(50) DEFAULT 'daily',
        email_notification BOOLEAN DEFAULT true,
        sms_notification BOOLEAN DEFAULT false,
        push_notification BOOLEAN DEFAULT true,
        digest_time VARCHAR(10) DEFAULT '09:00',
        max_articles_per_day INTEGER DEFAULT 50,
        min_relevance_score DECIMAL(5,3) DEFAULT 0.6,
        enable_ai_filtering BOOLEAN DEFAULT true,
        enable_duplicate_filter BOOLEAN DEFAULT true,
        priority_filter VARCHAR(20) DEFAULT 'all',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 创建索引
    CREATE INDEX idx_articles_category ON articles(category);
    CREATE INDEX idx_articles_source ON articles(source);
    CREATE INDEX idx_articles_publish_time ON articles(publish_time);
    CREATE INDEX idx_articles_priority ON articles(priority);
    CREATE INDEX idx_newsletter_subscriptions_subscription_id ON newsletter_subscriptions(subscription_id);
