#!/bin/bash

# GitHub Codespaces 快速配置脚本

echo "🚀 配置 GitHub Codespaces 环境..."

# 设置权限
chmod +x .devcontainer/setup.sh
chmod +x .devcontainer/start-services.sh
chmod +x scripts/deploy.sh
chmod +x scripts/test.sh
chmod +x scripts/performance_test.sh

# 创建快速启动别名
echo "alias start='./start-dev.sh'" >> ~/.bashrc
echo "alias test='./test-dev.sh'" >> ~/.bashrc
echo "alias deploy='./scripts/deploy.sh'" >> ~/.bashrc

# 设置Git配置
git config --global init.defaultBranch main
git config --global pull.rebase false

# 显示欢迎信息
cat << 'EOF'

🧬 欢迎使用生科云码平台 GitHub Codespaces！

📋 快速命令：
  start    - 启动开发环境
  test     - 运行测试
  deploy   - 部署到Kubernetes

🌐 访问地址：
  主站门户: http://localhost:3000
  资讯AI: http://localhost:3001
  文献AI: http://localhost:3002

📖 详细文档: .devcontainer/README.md

EOF

echo "✅ Codespaces 配置完成！"
