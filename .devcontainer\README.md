# 🧬 生科云码平台 - GitHub Codespaces 开发环境

## 🚀 快速开始

### 1. 在 GitHub Codespaces 中打开

1. 访问 GitHub 仓库
2. 点击绿色的 **Code** 按钮
3. 选择 **Codespaces** 标签
4. 点击 **Create codespace on main**

### 2. 等待环境初始化

Codespaces 会自动：
- 🐳 构建开发容器
- 📦 安装所有依赖
- 🗄️ 启动数据库和缓存服务
- 🔧 配置开发工具

初始化完成后，您将看到完整的 VS Code 开发环境。

## 🛠️ 开发环境包含

### 🖥️ 开发工具
- **VS Code** - 完整的IDE体验
- **Node.js 18** - 前端开发
- **Go 1.21** - 后端服务开发
- **Python 3.11** - AI服务开发
- **Docker** - 容器化支持
- **kubectl & Helm** - Kubernetes工具

### 🗄️ 基础服务
- **PostgreSQL 15** - 主数据库
- **Redis 7** - 缓存和会话存储
- **MailHog** - 邮件测试服务
- **MinIO** - S3兼容对象存储

### 📊 监控工具
- **Prometheus** - 指标收集
- **Grafana** - 数据可视化
- **日志聚合** - 集中式日志管理

## 🚀 启动应用

### 📋 脚本说明

- **`.devcontainer/setup.sh`** - 环境初始化脚本 (Codespaces创建时自动运行)
- **`.devcontainer/start-services.sh`** - 基础服务启动脚本 (Codespaces启动时自动运行)
- **`start-dev.sh`** - 应用服务启动脚本 (用户手动运行)
- **`stop-dev.sh`** - 停止所有服务脚本
- **`status-dev.sh`** - 服务状态检查脚本
- **`test-dev.sh`** - 测试运行脚本

### 方法1: 使用快速启动脚本
```bash
# 启动所有应用服务 (前端+后端+AI)
./start-dev.sh

# 检查服务状态
./status-dev.sh

# 停止所有服务
./stop-dev.sh

# 运行测试
./test-dev.sh
```

### 方法2: 手动启动各个服务

#### 启动后端服务
```bash
# 认证服务
cd services/auth-service
go run main.go

# 订阅服务  
cd services/subscription-service
go run main.go

# AI服务
cd services/ai-services/newsletter-ai
python main.py
```

#### 启动前端应用
```bash
# 主站门户
cd web-apps/main-portal
npm run dev

# 资讯处理AI
cd web-apps/newsletter-app  
npm run dev

# 文献阅读AI
cd web-apps/scholar-app
npm run dev
```

## 🌐 访问应用

### 前端应用
- 🏠 **主站门户**: http://localhost:3000
- 📰 **资讯处理AI**: http://localhost:3001  
- 📚 **文献阅读AI**: http://localhost:3002

### API服务
- 🔐 **认证服务**: http://localhost:8001
- 💳 **订阅服务**: http://localhost:8002
- 🤖 **AI服务**: http://localhost:9001-9006

### 开发工具
- 📧 **MailHog**: http://localhost:8025
- 💾 **MinIO Console**: http://localhost:9090
- 📊 **Prometheus**: http://localhost:9091
- 📈 **Grafana**: http://localhost:3030

## 🧪 运行测试

### 全面测试
```bash
./test-dev.sh
```

### 分别测试
```bash
# Go服务测试
cd services/auth-service && go test ./...

# Python AI服务测试  
cd services/ai-services/newsletter-ai && python -m pytest

# 前端测试
cd web-apps/main-portal && npm test
```

### 集成测试
```bash
chmod +x scripts/test.sh
./scripts/test.sh
```

### 性能测试
```bash
chmod +x scripts/performance_test.sh
./scripts/performance_test.sh
```

## 🔧 开发工作流

### 1. 代码开发
- 使用 VS Code 的智能提示和调试功能
- 支持 Go、Python、TypeScript 的语言服务
- 集成的 Git 工具

### 2. 实时预览
- 前端应用支持热重载
- API 服务自动重启
- 数据库变更实时同步

### 3. 调试支持
- Go 服务调试配置
- Python 服务调试配置
- 前端应用调试配置

### 4. 代码质量
- ESLint + Prettier (前端)
- golangci-lint (Go)
- Black + Flake8 (Python)
- Pre-commit hooks

## 📁 项目结构

```
biocloude/
├── .devcontainer/           # Codespaces配置
│   ├── devcontainer.json   # 容器配置
│   ├── docker-compose.yml  # 服务编排
│   ├── Dockerfile          # 开发容器
│   └── setup.sh            # 初始化脚本
├── web-apps/               # 前端应用
├── services/               # 后端服务
├── k8s/                    # Kubernetes配置
├── scripts/                # 工具脚本
└── README.md               # 项目文档
```

## 🔒 环境变量

开发环境已预配置所有必要的环境变量：

```bash
# 数据库
DATABASE_URL=*************************************************/biocloude

# Redis
REDIS_URL=redis://:biocloude123@redis:6379/0

# API配置
NEXT_PUBLIC_API_URL=http://localhost:8001

# 开发工具
SMTP_HOST=mailhog
S3_ENDPOINT=http://minio:9000
```

## 🐛 故障排除

### 服务无法启动
```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs [service-name]

# 重启服务
docker-compose restart [service-name]
```

### 端口冲突
```bash
# 查看端口使用
netstat -tulpn | grep [port]

# 停止冲突进程
kill -9 [pid]
```

### 数据库连接问题
```bash
# 测试数据库连接
pg_isready -h postgres -p 5432 -U biocloude

# 连接数据库
psql -h postgres -p 5432 -U biocloude -d biocloude
```

## 📞 获取帮助

- 📖 **文档**: 查看项目 README.md
- 🐛 **问题**: 创建 GitHub Issue  
- 💬 **讨论**: GitHub Discussions
- 📧 **邮件**: <EMAIL>

## 🎯 下一步

1. **熟悉代码结构** - 浏览各个服务的代码
2. **运行测试** - 确保环境正常工作
3. **开始开发** - 选择一个功能开始贡献
4. **提交代码** - 创建 Pull Request

---

🎉 **欢迎来到生科云码平台开发！让我们一起构建未来的生物科技AI平台！** 🧬
