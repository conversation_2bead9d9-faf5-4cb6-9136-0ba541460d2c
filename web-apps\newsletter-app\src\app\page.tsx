'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { 
  BeakerIcon, 
  DocumentTextIcon, 
  FunnelIcon, 
  BellIcon, 
  ChartBarIcon,
  ClockIcon,
  TagIcon,
  ArrowRightIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  SparklesIcon,
  GlobeAltIcon,
  UserGroupIcon,
  CogIcon,
  FireIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline'

// 模拟新闻数据
const mockNews = [
  {
    id: 1,
    title: "新型CRISPR基因编辑技术在治疗遗传性疾病方面取得重大突破",
    summary: "研究人员开发出更精确的基因编辑工具，能够有效治疗镰状细胞病和β地中海贫血症",
    category: "基因治疗",
    source: "Nature Medicine",
    publishTime: "2小时前",
    priority: "high",
    tags: ["CRISPR", "基因编辑", "遗传病", "临床试验"],
    readTime: "5分钟",
    image: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=200&fit=crop",
    url: "#"
  },
  {
    id: 2,
    title: "FDA批准首个基于AI的医疗诊断设备用于早期癌症检测",
    summary: "这款AI诊断系统能够在早期阶段检测出多种癌症类型，准确率达到95%以上",
    category: "医疗器械",
    source: "FDA News",
    publishTime: "4小时前",
    priority: "high",
    tags: ["FDA", "AI诊断", "癌症检测", "医疗器械"],
    readTime: "3分钟",
    image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=200&fit=crop",
    url: "#"
  },
  {
    id: 3,
    title: "新冠疫苗加强针对Omicron变异株显示出更强的保护效果",
    summary: "最新临床数据显示，更新的疫苗配方对当前流行的变异株具有更好的预防效果",
    category: "疫苗研发",
    source: "NEJM",
    publishTime: "6小时前",
    priority: "medium",
    tags: ["新冠疫苗", "Omicron", "临床试验", "免疫保护"],
    readTime: "4分钟",
    image: "https://images.unsplash.com/photo-1584118624012-df056829fbd0?w=400&h=200&fit=crop",
    url: "#"
  },
  {
    id: 4,
    title: "干细胞疗法在治疗帕金森病方面显示出显著疗效",
    summary: "临床试验结果表明，干细胞移植能够显著改善帕金森病患者的运动功能",
    category: "再生医学",
    source: "Cell Stem Cell",
    publishTime: "8小时前",
    priority: "medium",
    tags: ["干细胞", "帕金森病", "再生医学", "神经退行性疾病"],
    readTime: "6分钟",
    image: "https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=200&fit=crop",
    url: "#"
  },
  {
    id: 5,
    title: "新型抗生素对超级细菌显示出强大的杀菌效果",
    summary: "研究人员发现了一种新的抗生素化合物，能够有效对抗多重耐药性细菌",
    category: "抗感染",
    source: "Science",
    publishTime: "12小时前",
    priority: "low",
    tags: ["抗生素", "超级细菌", "耐药性", "感染治疗"],
    readTime: "4分钟",
    image: "https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=400&h=200&fit=crop",
    url: "#"
  },
  {
    id: 6,
    title: "mRNA技术在癌症免疫治疗中的新应用获得突破性进展",
    summary: "科学家利用mRNA技术开发出个性化癌症疫苗，在临床试验中显示出令人鼓舞的结果",
    category: "免疫治疗",
    source: "Nature",
    publishTime: "1天前",
    priority: "high",
    tags: ["mRNA", "癌症疫苗", "免疫治疗", "个性化医学"],
    readTime: "7分钟",
    image: "https://images.unsplash.com/photo-1628595351029-c2bf17511435?w=400&h=200&fit=crop",
    url: "#"
  }
]

// 统计数据
const stats = [
  { label: "今日资讯", value: "156", change: "+12%", icon: DocumentTextIcon, color: "text-blue-600" },
  { label: "活跃订阅", value: "2,847", change: "+8%", icon: UserGroupIcon, color: "text-green-600" },
  { label: "智能过滤", value: "89%", change: "+3%", icon: FunnelIcon, color: "text-purple-600" },
  { label: "推送准确率", value: "94%", change: "+2%", icon: SparklesIcon, color: "text-orange-600" },
]

// 热门标签
const popularTags = [
  "CRISPR", "AI诊断", "癌症治疗", "基因编辑", "免疫疗法", 
  "干细胞", "疫苗开发", "精准医学", "生物标志物", "临床试验"
]

// 数据源
const dataSources = [
  { name: "PubMed", count: "45", status: "active" },
  { name: "Nature", count: "23", status: "active" },
  { name: "Science", count: "18", status: "active" },
  { name: "Cell", count: "15", status: "active" },
  { name: "NEJM", count: "12", status: "active" },
  { name: "FDA News", count: "8", status: "active" }
]

export default function NewsletterHome() {
  const [selectedCategory, setSelectedCategory] = useState('全部')
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredNews, setFilteredNews] = useState(mockNews)
  const [isLoading, setIsLoading] = useState(false)

  const categories = ['全部', '基因治疗', '医疗器械', '疫苗研发', '再生医学', '抗感染', '免疫治疗']

  useEffect(() => {
    setIsLoading(true)
    setTimeout(() => {
      let filtered = mockNews
      
      if (selectedCategory !== '全部') {
        filtered = filtered.filter(news => news.category === selectedCategory)
      }
      
      if (searchQuery) {
        filtered = filtered.filter(news => 
          news.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          news.summary.toLowerCase().includes(searchQuery.toLowerCase()) ||
          news.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
        )
      }
      
      setFilteredNews(filtered)
      setIsLoading(false)
    }, 300)
  }, [selectedCategory, searchQuery])

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-500 bg-red-50'
      case 'medium': return 'border-yellow-500 bg-yellow-50'
      case 'low': return 'border-green-500 bg-green-50'
      default: return 'border-gray-300 bg-white'
    }
  }

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      '基因治疗': 'bg-red-100 text-red-800',
      '医疗器械': 'bg-orange-100 text-orange-800',
      '疫苗研发': 'bg-blue-100 text-blue-800',
      '再生医学': 'bg-purple-100 text-purple-800',
      '抗感染': 'bg-green-100 text-green-800',
      '免疫治疗': 'bg-pink-100 text-pink-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="glass-effect sticky top-0 z-50 border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-bio-500 to-medical-500 rounded-lg flex items-center justify-center">
                <BeakerIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold gradient-text">IVD Newsletter AI</h1>
                <p className="text-sm text-gray-600">生物医学资讯智能处理</p>
              </div>
            </div>
            
            <nav className="hidden md:flex space-x-8">
              <Link href="#dashboard" className="text-gray-600 hover:text-bio-600 transition-colors">仪表板</Link>
              <Link href="#subscriptions" className="text-gray-600 hover:text-bio-600 transition-colors">订阅管理</Link>
              <Link href="#filters" className="text-gray-600 hover:text-bio-600 transition-colors">智能过滤</Link>
              <Link href="#analytics" className="text-gray-600 hover:text-bio-600 transition-colors">数据分析</Link>
            </nav>
            
            <div className="flex items-center space-x-4">
              <button className="relative p-2 text-gray-600 hover:text-bio-600 transition-colors">
                <BellIcon className="w-6 h-6" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </button>
              <button className="btn-primary">
                登录
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-12 bg-gradient-to-br from-bio-50 via-white to-medical-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              生物医学资讯
              <span className="block gradient-text">智能处理平台</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              基于AI技术的专业资讯聚合平台，为生物医学领域提供精准的资讯筛选、智能分类和个性化推送服务
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <div key={index} className="stat-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">{stat.label}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600">{stat.change}</p>
                  </div>
                  <stat.icon className={`w-8 h-8 ${stat.color}`} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <aside className="lg:col-span-1">
            {/* Search */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <MagnifyingGlassIcon className="w-5 h-5 mr-2 text-bio-600" />
                智能搜索
              </h3>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索关键词、作者、期刊..."
                  className="search-input"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {/* Categories */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FunnelIcon className="w-5 h-5 mr-2 text-bio-600" />
                分类筛选
              </h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? 'bg-bio-500 text-white'
                        : 'text-gray-600 hover:bg-bio-50 hover:text-bio-600'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>

            {/* Popular Tags */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <TagIcon className="w-5 h-5 mr-2 text-bio-600" />
                热门标签
              </h3>
              <div className="flex flex-wrap gap-2">
                {popularTags.map((tag) => (
                  <button
                    key={tag}
                    onClick={() => setSearchQuery(tag)}
                    className="tag"
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>

            {/* Data Sources */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <GlobeAltIcon className="w-5 h-5 mr-2 text-bio-600" />
                数据源状态
              </h3>
              <div className="space-y-3">
                {dataSources.map((source) => (
                  <div key={source.name} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">{source.name}</span>
                    </div>
                    <span className="text-xs text-gray-500">{source.count}</span>
                  </div>
                ))}
              </div>
            </div>
          </aside>

          {/* News List */}
          <div className="lg:col-span-3">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                最新资讯
                <span className="text-sm font-normal text-gray-500 ml-2">
                  ({filteredNews.length} 条结果)
                </span>
              </h2>
              <div className="flex items-center space-x-4">
                <button className="flex items-center space-x-2 text-gray-600 hover:text-bio-600 transition-colors">
                  <AdjustmentsHorizontalIcon className="w-5 h-5" />
                  <span className="text-sm">高级筛选</span>
                </button>
                <button className="flex items-center space-x-2 text-gray-600 hover:text-bio-600 transition-colors">
                  <ClockIcon className="w-5 h-5" />
                  <span className="text-sm">时间排序</span>
                </button>
              </div>
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="flex items-center justify-center py-12">
                <div className="loading-spinner"></div>
                <span className="ml-3 text-gray-600">正在加载资讯...</span>
              </div>
            )}

            {/* News Cards */}
            {!isLoading && (
              <div className="space-y-6">
                {filteredNews.map((news) => (
                  <article key={news.id} className={`news-card ${getPriorityColor(news.priority)}`}>
                    <div className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <Image
                            src={news.image}
                            alt={news.title}
                            width={120}
                            height={80}
                            className="rounded-lg object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className={`category-badge ${getCategoryColor(news.category)}`}>
                              {news.category}
                            </span>
                            {news.priority === 'high' && (
                              <span className="flex items-center text-red-600 text-xs">
                                <FireIcon className="w-3 h-3 mr-1" />
                                热点
                              </span>
                            )}
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                            <Link href={news.url} className="hover:text-bio-600 transition-colors">
                              {news.title}
                            </Link>
                          </h3>
                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {news.summary}
                          </p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <span>{news.source}</span>
                              <span>•</span>
                              <span>{news.publishTime}</span>
                              <span>•</span>
                              <span>{news.readTime}</span>
                            </div>
                            <Link
                              href={news.url}
                              className="flex items-center text-bio-600 hover:text-bio-700 text-sm font-medium"
                            >
                              阅读全文
                              <ArrowRightIcon className="w-4 h-4 ml-1" />
                            </Link>
                          </div>
                          <div className="flex flex-wrap gap-1 mt-3">
                            {news.tags.map((tag) => (
                              <button
                                key={tag}
                                onClick={() => setSearchQuery(tag)}
                                className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded hover:bg-bio-100 hover:text-bio-700 transition-colors"
                              >
                                #{tag}
                              </button>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            )}

            {/* Empty State */}
            {!isLoading && filteredNews.length === 0 && (
              <div className="text-center py-12">
                <DocumentTextIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无相关资讯</h3>
                <p className="text-gray-600">尝试调整搜索条件或分类筛选</p>
              </div>
            )}

            {/* Load More */}
            {!isLoading && filteredNews.length > 0 && (
              <div className="text-center mt-8">
                <button className="btn-secondary">
                  加载更多资讯
                </button>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-bio-500 to-medical-500 rounded-lg flex items-center justify-center">
                  <BeakerIcon className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">IVD Newsletter AI</span>
              </div>
              <p className="text-gray-400">
                专业的生物医学资讯智能处理平台，为研究人员和从业者提供精准的信息服务。
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">产品功能</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">智能资讯聚合</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">AI分类过滤</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">个性化推送</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">数据分析</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">数据源</h3>
              <ul className="space-y-2 text-gray-400">
                <li>PubMed</li>
                <li>Nature</li>
                <li>Science</li>
                <li>Cell</li>
                <li>NEJM</li>
                <li>FDA News</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">联系我们</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">技术支持</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">商务合作</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">API文档</Link></li>
                <li><Link href="https://www.biocloude.cn" className="hover:text-white transition-colors">生科云码</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 生科云码. 保留所有权利. | IVD Newsletter AI - 生物医学资讯智能处理平台</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
