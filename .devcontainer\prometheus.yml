# Prometheus配置文件
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 生科云码平台服务监控
  - job_name: 'biocloude-auth-service'
    static_configs:
      - targets: ['host.docker.internal:8001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'biocloude-subscription-service'
    static_configs:
      - targets: ['host.docker.internal:8002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'biocloude-newsletter-ai'
    static_configs:
      - targets: ['host.docker.internal:9001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'biocloude-scholar-ai'
    static_configs:
      - targets: ['host.docker.internal:9002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'biocloude-primer-ai'
    static_configs:
      - targets: ['host.docker.internal:9003']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'biocloude-protein-ai'
    static_configs:
      - targets: ['host.docker.internal:9004']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'biocloude-gene-editing-ai'
    static_configs:
      - targets: ['host.docker.internal:9005']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'biocloude-metabolic-ai'
    static_configs:
      - targets: ['host.docker.internal:9006']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 前端应用监控
  - job_name: 'biocloude-main-portal'
    static_configs:
      - targets: ['host.docker.internal:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 60s

  - job_name: 'biocloude-newsletter-app'
    static_configs:
      - targets: ['host.docker.internal:3001']
    metrics_path: '/api/metrics'
    scrape_interval: 60s

  - job_name: 'biocloude-scholar-app'
    static_configs:
      - targets: ['host.docker.internal:3002']
    metrics_path: '/api/metrics'
    scrape_interval: 60s

  # 基础设施监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: '/minio/v2/metrics/cluster'
    scrape_interval: 30s

  # Node Exporter (如果安装)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['host.docker.internal:9100']
    scrape_interval: 30s

  # Docker监控 (如果安装cAdvisor)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['host.docker.internal:8080']
    scrape_interval: 30s
