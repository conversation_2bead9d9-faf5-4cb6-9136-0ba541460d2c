apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: biocloude
  labels:
    app: auth-service
    component: backend-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
        component: backend-service
    spec:
      containers:
      - name: auth-service
        image: biocloude/auth-service:latest
        ports:
        - containerPort: 8001
          name: http
        - containerPort: 9001
          name: grpc
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: jwt-secret
        - name: CIAM_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: ciam-client-id
        - name: CIAM_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: biocloude-secrets
              key: ciam-client-secret
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "info"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: biocloude
  labels:
    app: auth-service
spec:
  selector:
    app: auth-service
  ports:
  - name: http
    port: 8001
    targetPort: 8001
  - name: grpc
    port: 9001
    targetPort: 9001
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: auth-service-hpa
  namespace: biocloude
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: auth-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
