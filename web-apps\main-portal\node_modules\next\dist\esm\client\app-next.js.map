{"version": 3, "sources": ["../../src/client/app-next.ts"], "sourcesContent": ["// This import must go first because it needs to patch webpack chunk loading\n// before React patches chunk loading.\nimport './app-webpack'\nimport { appBootstrap } from './app-bootstrap'\n\nconst instrumentationHooks = require('../lib/require-instrumentation-client')\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index')\n  // Include app-router and layout-router in the main chunk\n  require('next/dist/client/components/app-router')\n  require('next/dist/client/components/layout-router')\n  hydrate(instrumentationHooks)\n})\n"], "names": ["appBootstrap", "<PERSON><PERSON><PERSON><PERSON>", "require", "hydrate"], "mappings": "AAAA,4EAA4E;AAC5E,sCAAsC;AACtC,OAAO,gBAAe;AACtB,SAASA,YAAY,QAAQ,kBAAiB;AAE9C,MAAMC,uBAAuBC,QAAQ;AAErCF,aAAa;IACX,MAAM,EAAEG,OAAO,EAAE,GAAGD,QAAQ;IAC5B,yDAAyD;IACzDA,QAAQ;IACRA,QAAQ;IACRC,QAAQF;AACV"}