{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/action-client-wrapper.ts"], "sourcesContent": ["// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nexport { callServer } from 'next/dist/client/app-call-server'\nexport { findSourceMapURL } from 'next/dist/client/app-find-source-map-url'\n\n// A noop wrapper to let the Flight client create the server reference.\n// See also: https://github.com/facebook/react/pull/26632\n// Since we're using the Edge build of Flight client for SSR [1], here we need to\n// also use the same Edge build to create the reference. For the client bundle,\n// we use the default and let Webpack to resolve it to the correct version.\n// 1: https://github.com/vercel/next.js/blob/16eb80b0b0be13f04a6407943664b5efd8f3d7d0/packages/next/src/server/app-render/use-flight-response.tsx#L24-L26\nexport const createServerReference = (\n  (!!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')) as typeof import('react-server-dom-webpack/client')\n).createServerReference\n"], "names": ["callServer", "findSourceMapURL", "createServerReference", "process", "env", "NEXT_RUNTIME", "require"], "mappings": "AAAA,gFAAgF;AAChF,0BAA0B;AAE1B,SAASA,UAAU,QAAQ,mCAAkC;AAC7D,SAASC,gBAAgB,QAAQ,2CAA0C;AAE3E,uEAAuE;AACvE,yDAAyD;AACzD,iFAAiF;AACjF,+EAA+E;AAC/E,2EAA2E;AAC3E,yJAAyJ;AACzJ,OAAO,MAAMC,wBAAwB,AAClC,CAAA,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEvBC,QAAQ,0CAERA,QAAQ,kCAAiC,EAC7CJ,qBAAqB,CAAA"}